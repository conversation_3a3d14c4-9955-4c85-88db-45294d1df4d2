<?php
namespace app\api\controller\ai;

use app\common\controller\Api;
use think\Db;
use PHPExcel;
use PHPExcel_IOFactory;
use app\common\service\IpService;
use app\common\service\Convert;
class Student extends Api
{
	// 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];

	/** 读取问题列表 */
	function question_list(){
		$robot = input('robot');
		$category = input('category');
		$return = [];
			// $question_list = ['请点击我领取相关问题'];
			
		$answer = \app\admin\model\flow\CaseList::where(['robot'=>$robot,'category_id'=>$category])->orderRaw('rand()')->find();
		$question = \app\admin\model\flow\CaseDetail::where(['first'=>1,'case_id'=>$answer['id']])->find();
		$next_id = \app\admin\model\flow\CaseDetail::where(['next_id'=>$question['id']])->value('id');
		$return['question_list'] = !empty($question['question'])?explode(',',$question['question']):'';
		$return['next_id'] = $next_id;
		$return['answer'] = $question['q'];
		$return['assistant_name'] = $answer['name'];
		/** 知识库 */
		$project = Db::name('ai_project')->where('id',$robot)->find();
		$word_count = Db::name('ai_console_file')->where(['console_id'=>['in', explode(',',$project['console_ids'])]])->sum('word_count');
		$page = ceil($word_count/500);
		$return['page'] = ['num'=>$page,'size'=>$project['size']."G",'question'=>'你的知识库有哪些？'];
		$num = Db::name('ai_msg')->where(['robot'=>$robot])->count();
		$return['question'] = ['num'=>$num,'question'=>'当前最热门的问题是什么？']; 
		
        $console_key = db::name('ai_project')->where('id',$robot)->value('console_key');
        if(empty($console_key)){
            $console_key = "8e36c365-f419-488c-8002-9789d4dcbaee";
        }
		$chatId = exec('/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/app.py "'.$console_key.'" 1');

		$return['chatId'] = $chatId;
		/** 虚拟人 */
		$return['assistant'] = $answer['assistant_url']?$answer['assistant_url']:(\app\admin\model\ai\Assistant::where('id',$answer['assistant_id'])->value('assistant_name') ?? 'yaoboshi');
		/** 发音人 */
		$return['voice'] = \app\admin\model\ai\Voice::where('id',$answer['voice_id'])->value('voice_name') ?? 'aisjiuxu';
		$this->success('success',$return);
	}

    /**
	 * 获取卡片
	 */
	function get() {

		$robot = input('robot');
		$category = input('category');
		$msg = input('msg');
		$chatId = input('chatId');
		$nextId = input('nextId');
		$return = [];

		if($nextId){
			$question = \app\admin\model\flow\CaseDetail::where(['id'=>$nextId])->find();
			
			$multimodelRow = $this->getEmbeddingsNew([$msg]);
			$vectorizationArr = json_decode(base64_decode($question['vectorization']),true);
			$cosine = $this->cosineSimilarity($multimodelRow[0]['embedding'],$vectorizationArr['embedding']);
			$question['score'] = $cosine*1000;
			if($question['score']>300)
			{
			    error_log(date("Y-m-d H:i:s")."question:".$msg."|val:".$question['q']."|cosine:".$cosine,3,ROOT_PATH."/runtime/log/".date("Ym")."/embedding_student.log");
			}
			$is_click = true;
			$status = 0;
			if($question['score']>500)
			{
				$frist = !empty($question['a1'])?"回答正确<br>":"";
				$answer = $frist.$question['a'];
				$question_list = !empty($question['question'])?explode(',',$question['question']):'';
				$next_id = \app\admin\model\flow\CaseDetail::where(['next_id'=>$question['id']])->value('id');
				// $is_click = $question['first']=='1'?true:false;
				$detail = $question['detail'];
				$status = !empty($question['a1'])?1:0;
			}else{
				$answer = "回答错误<br>".$question['a1'];
				$parent_question = \app\admin\model\flow\CaseDetail::where(['id'=>$question['next_id']])->find();
				$question_list = !empty($parent_question['question'])?explode(',',$parent_question['question']):'';
				$detail = $parent_question['detail'];
				$next_id = $nextId;
				// $is_click = $parent_question['first']=='1'?true:false;
				$status = 2;
			}
			
			$answer = Convert::style($answer);
			$return['answer'] = $answer;
			$return['detail'] = $detail;
			$return['question'] = $question_list;
			$return['next_id'] = $next_id;
			$return['is_click'] = $is_click;
			$return['status'] = $status;
			$this->success('success',$return);
		}


		
		$answer = $this->getEmbeddingsUnit($robot, $msg, $chatId);
		$return['answer'] = !empty($answer['answer'])?$answer['answer']:'';
		$return['question'] = !empty($answer['question'])?explode(',',$answer['question']):'';
		if(strpos($return['answer'],'video')!==false){
			$return['list'] = [];
		}else{
			$multimodelRow = $this->getEmbeddingsNew([$msg]);
			$multimodelList = Db::name('ai_card')->where(['robot'=>$robot])->select();
			$answerHtml = '';
			$oldArr = $sourceArr = [];
			foreach($multimodelList as $key=>&$val){
				$vectorizationArr = json_decode(base64_decode($val['vectorization']),true);
				$cosine = $this->cosineSimilarity($multimodelRow[0]['embedding'],$vectorizationArr['embedding']);
				$val['score'] = $cosine*1000;
				if(!in_array(md5($val['a']),$oldArr) && $val['score']>500)
				{
					unset($val['vectorization']);
					$sourceArr[] = $val;
					$oldArr[] = md5($val['a']);
				}
			}
			usort($sourceArr, function($a, $b) {
				return $b['score'] - $a['score'];
			});
			/** 优化格式 */
			if(!empty($sourceArr))
			{
				foreach($sourceArr as $key=>&$val)
				{
					if(strpos($val['a'],'mp4')!==false)
					{
						$result = preg_replace_callback('/\[\!\[(.*?)\]\((.*?)\)\]\((.*?)\)/', function($matches) {
							return "<div class=\"card videoEle\" onclick=\"openVideo('{$matches[1]}','{$matches[3]}')\"><div class=\"card-pic\"><img src=\"{$matches[2]}\"></div><div class=\"desc\">{$matches[1]}</div></div>";
						}, $val['a']);
					}else{
						$result = preg_replace_callback('/\[\!\[(.*?)\]\((.*?)\)/', function($matches) {
							return "<div class=\"card videoEle\" onclick=\"openVideo('{$matches[1]}','{$matches[2]}')\"><div class=\"card-pic\"><img src=\"{$matches[2]}\"></div><div class=\"desc\">{$matches[1]}</div></div>";
						}, $val['a']);
					}
					$val['a'] = $result;
				}
			}
			$return['list'] = $sourceArr?array_slice($sourceArr, 0, 5):[];
		}
		$this->success('success',$return);
	}

	/**
	 * 获取最常问题
	 */
	function questionHot($robot){
		switch ($robot) {
			case 11:
				$tags = ['气相色谱','液相色谱','原子吸收'];
				break;
			case 16:
				$tags = ['细胞实验'];
				break;
			case 3:
				$tags = ['流体力学实验操作'];
				break;
			case 5:
				$tags = ['压片机的结构'];
				break;
			case 19:
				$tags = ['拜耳法'];
				break;
			case 2:
				$tags = ['人参的功效是什么'];
				break;
			default:
				$tags = ['气相色谱','液相色谱','原子吸收'];
				break;
		}
		$result = [];
		foreach($tags as $item){
			$questionNum = Db::name('ai_msg')->where(['robot'=>$robot,'msg'=>['like',"%{$item}%"]])->count();
			$questionNum += 10;
			$result[] = ['q'=>$item,'number'=>$questionNum];
		}
		usort($result, function($a, $b) {
			return $b['number'] - $a['number'];
		});
		return $result[0];
	}

	/** 调用向量库UNIT */
	function getEmbeddingsUnit($robot,$msg, $chatId){
		$return = [];
		$multimodelRow = $this->getEmbeddingsNew([$msg]);
		$multimodelList = Db::name('ai_multimodal')->where(['robot'=>['in',[$robot]]])->select();
		$answerHtml = '';
		$oldArr = $sourceArr = [];
		foreach($multimodelList as $key=>&$val){
			$vectorizationArr = json_decode(base64_decode($val['vectorization']),true);
			$cosine = $this->cosineSimilarity($multimodelRow[0]['embedding'],$vectorizationArr['embedding']);
			$val['score'] = $cosine*1000;
			// 			trace(date("Y-m-d H:i:s")."|getEmbeddingsUnit:".$msg."|val:".$val['q']."|cosine:".$cosine);
			if(!in_array(md5($val['a']),$oldArr) && $val['score']>888)
			{
				unset($val['vectorization']);
				$sourceArr[] = $val;
				$oldArr[] = md5($val['a']);
			}
		}
		usort($sourceArr, function($a, $b) {
			return $b['score'] - $a['score'];
		});
		$context = $question = '';
		/** 优化格式 */
		if(!empty($sourceArr))
		{
			foreach($sourceArr as $key=>&$val)
			{
				$context = $val['a'];
				$question = $val['question'];
				break;
			}
			
			/** 问答记录 */
            if($context)
            {
                $ip = request()->ip();
				$area = IpService::getArea($ip);
                Db::name('ai_msg')->insert(['robot'=>$robot, 'chatId'=> $chatId, 'msg'=>$msg, 'time'=>time(), 'ip'=> $ip, 'content'=>$context,'city'=>$area]);
            }
		}
		$context = Convert::tag($context);
		$return['answer'] = $context;
		$return['question'] = $question;
		return $return;
	}

	/** 标签转换 */
	function convert() {
		$msg = input('msg');
		$return = convert::tag($msg);
		$this->success('success',$return);
	}
	
	/** 
	 * 调用生成向量数组匹配对应关系
	 */
	function getEmbeddingsNew($arr){
		
		$input = json_encode(['input'=>$arr]);
		
		$embeddings = new \app\common\library\Embeddings;
		$embeddingsStr = $embeddings->run($input);
		$embeddingsArr = json_decode($embeddingsStr,true);
		
		return $embeddingsArr['data'];
	}

	/**
	 * 计算余弦相似度
	 */
	function cosineSimilarity($vector1, $vector2) {  
		$dotProduct = 0;  
		$magnitude1 = 0;  
		$magnitude2 = 0;  
		$count = count($vector1);  
	
		for ($i = 0; $i < $count; $i++) {  
			$dotProduct += $vector1[$i] * $vector2[$i];  
			$magnitude1 += pow($vector1[$i], 2);  
			$magnitude2 += pow($vector2[$i], 2);  
		}  
	
		$magnitude1 = sqrt($magnitude1);  
		$magnitude2 = sqrt($magnitude2);  
		return $dotProduct / ($magnitude1 * $magnitude2);  
	}

	/**
	 * 获取追问
	 */
	function get_question() {

		$robot = input('robot');

		$msg = input('msg');
		$return = [];
		$return = $this->getFastGptQuestion($robot,$msg);
			
		$this->success('success',$return);
	}
	/** 调用FastGPT追问 */
	function getFastGptQuestion($robot,$msg){
		$apiUrl = 'https://share.fastgpt.in/api/core/ai/agent/createQuestionGuide';
		switch ($robot) {
			case '11':
			case '16':
				$apiKey = 'fastgpt-rk96CZ0W5mhXYc6vj23uLAVDsOXjx8OPDM7O';
				break;
			case '5':
				$apiKey = 'fastgpt-2GxtvBSekDS7w4MdCoiWAnByYQ';
				break;
				
			case '2':
				$apiKey = 'fastgpt-eTiyHB9xqWN5B3w33puTt1UxafrQLChg';
				break;
			case '3':
			case '7':
				$apiKey = 'fastgpt-KB4fVetStwyQhT56TwOOVyEIjdNfR0O';
				break;
			case '4':
				$apiKey = 'fastgpt-FDJWIY4mKsOh655KPAp4SIu6';
				$msg .= " 请用英文回复";
				break;
			case '6':
				$apiKey = 'fastgpt-a2zQc3FJSSrTJBbCRk0a81LUoks';
				break;
			case '9':
				$apiKey = 'fastgpt-ISKpfTE5TM0gQFCLbpAS6V2mSw';
				break;
			case '10':
				$apiKey = 'fastgpt-UIQG9nU9EdrwhXyqF6webLRojyzIh56vK';
				break;
			case '12':
				$apiKey = 'fastgpt-2Nz9vyrOI4LhG9v3lnJkwetrnDCRa';
				break;
			case '13':
				$apiKey = 'fastgpt-qZNWUNKGJcmVQLZhaeVLf5Cjb2W';
				$msg .= " 请用英文回复";
				break;
			case '14':
				$apiKey = 'fastgpt-DvuC9pMlVVDEiVmQbQbgdonwWbITlLWvUsnK';
				break;
			case '15':
				$apiKey = 'fastgpt-7F9bwT16bTyB1h2rPX9MEaGCkpi8gjc1';
				break;
			
			default:
				$apiKey = 'fastgpt-rk96CZ0W5mhXYc6vj23uLAVDsOXjx8OPDM7O';
				break;
		}
		$apiKey = 'fastgpt-3G3hQ6z3B0OG350MloBIbvgdnchTuAWYScoeNmdXFMp4eIBiD67i';
		$chatId = rand(11111,99999);
		$data = [
			"messages" => [
				[
					"content" => $msg,
					"role" => "assistant"
				]
			],
			'outLinkUid'=>'shareChat-1714300659349-DNgVzGbIAkc7kzWSSiHcts8n',
			'shareId'=>'xdsgb5ule9eh8x8rg1bw6aja'
		];
		
		$headers = [
			'Authorization: Bearer ' . $apiKey,
			'Content-Type: application/json'
		];
		
		$options = [
			'http' => [
				'header' => implode("\r\n", $headers),
				'method' => 'POST',
				'content' => json_encode($data),
				'timeout' => 30
			]
		];
		
		$context = stream_context_create($options);
		$result = @file_get_contents($apiUrl, false, $context);
		$list = [];
		if ($result !== false) {
			$data = json_decode($result, true);
			$list = isset($data['data'])?$data['data']:[];
		} else {
			trace(date("Y-m-d H:i:s")."|error:".print_r(error_get_last(),1));
		}
		return $list;
	}
}