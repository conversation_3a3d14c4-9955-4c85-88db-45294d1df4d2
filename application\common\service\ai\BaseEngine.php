<?php

namespace app\common\service\ai;


use think\Db;

abstract class BaseEngine
{


	protected $pythonPath = '/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9';
	

	public function sendMessage($id, $data) {
        echo "id: $id" . PHP_EOL;
        echo "data: $data" . PHP_EOL;
        echo PHP_EOL;
        ob_flush();
        flush();
    }

	/*
	* 知识库检索
	*/
	public function search() {}

	/*
	* 添加知识库
	*/
	public function addConsole($name, $memo) {}


	// public function addConsoleToDb($params)
	// {
    //     try {
    //         $params['console_number'] = $params['console_number'];
    //         $params['name'] = $params['name'];
    //         $params['user_id'] = $params['user_id'];
    //         $params['image'] = $params['image'];
    //         $params['createtime'] = time();
    //         $params['updatetime'] = time();
    //         $result = Db::name('ai_console')->insert($params);
    //     } catch (\Exception $e) {
    //         exception($e->getMessage());
    //     }
	// }

	/*
	* 编辑知识库
	*/
	public function editConsole() {}


	/*
	* 删除知识库
	*/
	public function deleteConsole($resource_id) {}

	// public function deleteConsoleFromDb($resource_id) 
	// {
	// 	return \app\admin\model\ai\Console::where('console_number',$resource_id)->delete();
	// }


	/*
	* 添加知识库文件
	*/
	public function addConsoleFile($console_number,$console_file_id,$file_url,$filename) {}


	/*
	* 删除知识库文件
	*/
	public function deleteConsoleFile($console_number,$console_file_id) {}


	/*
	* 刷新知识库文件训练状态
	*/
	public function freshConsoleFileStatus($console) {}



}