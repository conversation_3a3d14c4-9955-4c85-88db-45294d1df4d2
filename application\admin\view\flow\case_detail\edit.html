<style>
    .slider{
        width: 287px !important;
    }
    .slider-track{
        background-image: linear-gradient(to bottom, #9b9b9b, #9b9b9b);
    }
    .slider-selection{
        background-image: linear-gradient(to bottom, #4397fd, #4397fd);
    }
</style>
<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Case_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-case_id" data-rule="required" data-source="flow/case_list/index" class="form-control selectpage" name="row[case_id]" type="text" value="{$row.case_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Q')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-q" class="form-control" name="row[q]" type="text" value="{$row.q|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('A')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-a" class="form-control" name="row[a]" type="text">{$row.a|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('A1')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-a1" class="form-control" name="row[a1]" type="text">{$row.a1|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Detail')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-detail" class="form-control" name="row[detail]" type="text">{$row.detail|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Question')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-question" class="form-control" name="row[question]" type="text" value="{$row.question|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Next_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-next_id" data-source="flow/case_detail/index" data-field="q" data-search-field="q,a,a1" class="form-control selectpage" name="row[next_id]" type="text" value="{$row.next_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('First')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-first" class="form-control selectpicker" name="row[first]">
                {foreach name="firstList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.first"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Cosine')}:</label>
        <!-- <div class="col-xs-12 col-sm-8">
            <input id="c-cosine" class="form-control" name="row[cosine]" type="text" value="{$row.cosine|htmlentities}">
        </div> -->
        <div class="col-xs-12 col-sm-8">
            <input id="c-cosine" type="text" class="form-control slider" data-slider-min="0.01" data-slider-max="0.99" data-slider-step="0.01" data-slider-value="{$row.cosine?$row.cosine:'0.5'}" data-slider-orientation="horizontal" data-slider-selection="before" data-slider-tooltip="show"  name="row[cosine]"  data-slider-handle="triangle" />
            <div class="form-tip">在检索过程中，用来计算输入Query和卡片标签的相似度，高于或等于匹配分的片段将会被召回</div>
        </div>
    </div>
    <!-- <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Vectorization')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-vectorization" class="form-control " rows="5" name="row[vectorization]" cols="50">{$row.vectorization|htmlentities}</textarea>
        </div>
    </div> -->
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
