<?php

namespace app\common\service\ai\engine\strategy;

/**
 * 策略接口
 */
interface StrategyInterface
{
    /**
     * 设置引擎实例
     * @param object $engine
     */
    public function setEngine($engine);
    
    /**
     * 构造引用内容
     * @param array $project 项目信息
     * @param string $msg 用户消息
     * @param object $engine 引擎实例
     * @return string
     */
    public function buildReferenceContent($project, $msg, $engine);
    
    /**
     * 构造请求payload
     * @param array $messages 消息数组
     * @param object $engine 引擎实例
     * @return array
     */
    public function buildPayload($messages, $engine);
}
