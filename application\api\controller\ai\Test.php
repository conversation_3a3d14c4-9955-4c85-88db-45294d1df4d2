<?php
namespace app\api\controller\ai;

use app\common\controller\Api;
use think\Db;
use PHPExcel;
use PHPExcel_IOFactory;
use Parsedown;
use app\common\service\ai\AiService;
use think\Queue;
use app\common\service\BaiduSearchService;
use app\common\model\User;
use think\Cache;

class Test extends Api
{
	// 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];
    
    private $model;
	private $pgTable = 'public.vector_data_upgrades';

	protected $lang = 'cn';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\ConsoleFile;
    }
    
	public function translation(){
        $question = '煅烧温度和时间如何影响纳米铈镍复合氧化物的晶相结构和比表面积？';
        $TitlePredictor = new \app\common\library\aliyun\TitlePredictor;
        $title = $TitlePredictor->generateTitles($question);
       dump($title);exit;
    }
	// 实现调用AppBuilder接口并进行流式输出
    public function callStreamApi()
    {
        header("Access-Control-Allow-Origin: *");
		header('Content-Type: text/event-stream');
      	header('Cache-Control: no-cache');
      	header('Connection: keep-alive');
      	header('X-Accel-Buffering: no');
        
		$postData = file_get_contents("php://input");
        $postDataArr = json_decode($postData);
        $robot = $postDataArr->robot;
        $this->lang = isset($postDataArr->lang) ? $postDataArr->lang : '';
		$chatId = $postDataArr->chatId;
		$card = $postDataArr->card;
        $question = isset($postDataArr->prompt) ? $postDataArr->prompt : "";
        $project = db::name('ai_project')->where('id',$robot)->field('id,type,console_key')->find();
        $console_key = $project['console_key'];

        if(empty($console_key)){
            $console_key = "8e36c365-f419-488c-8002-9789d4dcbaee";
        }


        $aiEngine = AiService::getEngine('local');
        $aiEngine->chat($console_key,$chatId,$question,$robot,$card,$this->lang);
        exit;
    }

    

	/**
	 * 获取追问
	 */
	function get_question() {

		$robot = input('robot');
		$this->lang = input('lang');

		$msg = input('msg');
		$return = [];

        $aiEngine = AiService::getEngine('local');
        $return = $aiEngine->similar_question($msg);
		
		/** 翻译 */
		if($this->lang && $this->lang!='cn'){
			$Translation = new \app\common\library\Translation();
			if(!empty($return))
			{
				foreach($return as $qkey=>$qval)
				{
					$return[$qkey] = $Translation->xfyun($qval, 'cn', $this->lang);
				}
			}
		}

		$this->success('success',$return);
	}

    /**
     * 切片存储
     */
    public function saveSlice(){

        $aiEngine = AiService::getEngine('local');

        $list = $this->model->where('id', 94)->select();


        $request = Db::connect(
            [
                'type' => 'pgsql',
                'hostname' => '127.0.0.1',
                'database' => config('postgres.database'),
                'username' => 'postgres',
                'password' => 'DRrTmhCKrLWs2b34',
                'hostport' => '5432'
            ]
        );
        $query = $request->table('public.vector_data');
        $old = $query->query("select * from vector_data limit 1");
        dump($old);exit;
        // dump($request);

        foreach($list as $item){
            $sliceContent = $aiEngine->slice($item['file']);
            dump($sliceContent);
            $sliceList = json_decode($sliceContent,true);
            // dump($sliceList);exit;
            if(!empty($sliceList['content']['paragraphs']))
            {
                foreach($sliceList['content']['paragraphs'] as $slice){
                    $md5 = md5($slice['text']);
                    $old = $query->query("select * from vector_data where md5='{$md5}' limit 1");
                    dump($old);
                    if(!empty($old)){
                        continue;
                    }
                    $sliceText = mb_substr($slice['text'], 0, 384);
                    $vectorContent = $aiEngine->vector($sliceText);
                    usleep(500000);

                    $vectorList = json_decode($vectorContent,true);
                    dump($vectorList);
                    if(!empty($vectorList['content']))
                    {
                        $embedding = json_encode($vectorList['content']);

                        $word_count = mb_strlen($slice['text']);
                        $insert = [
                            'console_id' => $item['console_id'],
                            'file_id' => $item['id'],
                            'content' => $slice['text'],
                            'embedding' => $embedding,
                            'md5' => $md5,
                            'word_count' => $word_count
                        ];
                        $insert['content'] = str_replace("'","‘",$insert['content']);
                        $sql  = "INSERT INTO vector_data (console_id,file_id,content,embedding,md5,word_count) VALUES ('{$insert['console_id']}','{$insert['file_id']}','{$insert['content']}','{$insert['embedding']}','{$insert['md5']}','{$insert['word_count']}')";
                        $query->execute($sql);
                        
                    }
                }
                // dump($sliceContent);exit;
            }
        }
        // dump($list);exit;
        return 'Data saved successfully';

    }

    public function select(){
        $text = '核材料许可证';
        $request = Db::connect(
            [
                'type' => 'pgsql',
                'hostname' => '127.0.0.1',
                'database' => config('postgres.database'),
                'username' => 'postgres',
                'password' => 'DRrTmhCKrLWs2b34',
                'hostport' => '5432'
            ]
        );
        $query = $request->table('public.vector_data');

        $aiEngine = AiService::getEngine('local');

        $vectorContent = $aiEngine->vector($text);

        $vectorList = json_decode($vectorContent,true);
        if(!empty($vectorList['content']))
        {
            $queryVector = json_encode($vectorList['content']);
            $old = $query->query("SELECT id, content, 1 - (embedding <=> '{$queryVector}') AS cosine_similarity FROM vector_data ORDER BY embedding <-> '{$queryVector}' LIMIT 10");
            dump($old);exit;
        }

    }


    public function chat(){
        header("Access-Control-Allow-Origin: *");
		header('Content-Type: text/event-stream');
      	header('Cache-Control: no-cache');
      	header('Connection: keep-alive');
      	header('X-Accel-Buffering: no');
        $robot = input('robot');
        $msg = input('msg');
        // $project = db::name('ai_project')->where('id',$robot)->find();
        // $references = [];
        // if(in_array($robot,[61]))
        // {
        //     $aiEngine = AiService::getEngine('ragprofessional');
        // }else if($project['enhance_status']==1)
        // {
        //     $aiEngine = AiService::getEngine('ragenhance');
        // }else{
        //     $rag = AiService::getEngine('rag');
        //     $references = $rag->buildReferenceContent($project, $msg);
        //     dump($references);exit;
        // }
        $aiEngine = AiService::getEngine('localtest');
        // $aiEngine->references = $references;
        $modelId = "";
        $contextId = "baac0f04-e9fb-4acb-ab03-de792b275f7f";
        $card = 1;
        $lang = "";

        $vectorContent = $aiEngine->chat($modelId,$contextId,$msg,$robot,$card,$lang = 'cn', '', true);

        dump($vectorContent);exit;

    }

    public function chatenhance(){
        header("Access-Control-Allow-Origin: *");
		header('Content-Type: text/event-stream');
      	header('Cache-Control: no-cache');
      	header('Connection: keep-alive');
      	header('X-Accel-Buffering: no');
        
        $aiEngine = AiService::getEngine('localenhance');
        $modelId = "";
        $contextId = "90982592-ef23-4974-83b2-d5f06a07de53";
        $msg = "场景问题一";
        $robot = 51;
        $card = 1;
        $lang = "";

        $vectorContent = $aiEngine->chat($modelId,$contextId,$msg,$robot,$card,$lang = 'cn', '', true);

        dump($vectorContent);exit;

    }

    public function chatprofessional(){
        header("Access-Control-Allow-Origin: *");
		header('Content-Type: text/event-stream');
      	header('Cache-Control: no-cache');
      	header('Connection: keep-alive');
      	header('X-Accel-Buffering: no');
        
        $aiEngine = AiService::getEngine('localprofessional');
        $modelId = "";
        $contextId = "90982592-ef23-4974-83b2-d5f06a07".rand(1000,9999);
        $msg = "测定原料药和辅料中的亚硝酸钠，推荐用什么方法？";
        $robot = 61;
        $card = 1;
        $lang = "";

        $vectorContent = $aiEngine->chat($modelId,$contextId,$msg,$robot,$card,$lang = 'cn', '', true);

        dump($vectorContent);exit;

    }

    public function chattest(){
        header("Access-Control-Allow-Origin: *");
		header('Content-Type: text/event-stream');
      	header('Cache-Control: no-cache');
      	header('Connection: keep-alive');
      	header('X-Accel-Buffering: no');
        
        $aiEngine = AiService::getEngine('localtest');
        $modelId = "";
        $contextId = "90982592-ef23-4974-83b2-d5f06a07de53";
        $msg = "磺丁基倍他环糊精钠的离子色谱检测方法有哪些";
        $robot = 61;
        $card = 1;
        $lang = "";

        $vectorContent = $aiEngine->chat($modelId,$contextId,$msg,$robot,$card,$lang = 'cn', '', true);

        dump($vectorContent);exit;

    }

    public function aa(){
        $msg = "纳米铈镍复合氧化物的制备及结构性质表征";
        $robot = 53;
        $matchingScore = 0.4;
		$project = Db::name('ai_project')->where('id', $robot)->find();

        $aiEngine = AiService::getEngine('local');
        $aiEngine->debug = true;
        $sourceArr = $aiEngine->selectVectorData($robot, 1, $msg, $matchingScore, $this->lang, $project['console_ids']);
        dump($sourceArr);exit;
    }

    function generateUUIDv4() {
		$data = random_bytes(16);
		// 设置 UUID 版本 (4) 和 variant bits
		$data[6] = chr(ord($data[6]) & 0x0f | 0x40);
		$data[8] = chr(ord($data[8]) & 0x3f | 0x80);
	
		return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
	}

    public function getChatId(){
        $aiEngine = AiService::getEngine('local');

        $vectorContent = $aiEngine->getChatId();

        dump($vectorContent);exit;

    }

    public function add_console_file(){
        $project = Db::name('ai_project')->where('id', 30)->find();
        $consoleList = \app\admin\model\ai\ConsoleFile::where(['id'=>['in', [3392]]])->select();
        $console_number = Db::name('ai_console')->where('id', 107)->value('console_number');
        // dump($console_number);exit;
        foreach($consoleList as $Item){
            $file1 = $Item['file'];
            // $console_number = "d61f6017-332b-447b-808d-d64a74a1c062";
            $console_file_id = $Item['id'];
            $filename = $Item['name'];
            $url = request()->domain() . $file1;
            // Queue::push('app\queue\job\UploadFileToConsole', [
            //     'url' => request()->domain() . $file1,
            //     'console_number' => $console_number,
            //     'console_file_id' => $console_file_id,
            //     'filename' => $name,
            //     'engine_type' => 'local'
            // ]);
            $engine = AiService::getEngine('localtest');
            $res = $engine->addConsoleFile($console_number,$console_file_id,$url,$filename);
            dump($res);
            exit;
        }
        return "success";
    }

    public function get_url(){
        $result = 235;
        $url = "https://www.thermofisher.cn/cn/zh/home.html";
        $internal = true;
        $user_id = 53;
        $console_id = 234;

        //推入训练队列中
        Queue::push('app\queue\job\UrlToConsole', [
            'url_id' => $result,
            'url' => $url,
            'internal' => $internal,
            'user_id' => $user_id,
            'console_id' => $console_id
        ],'url_to_console');
        
    }

    public function card_data(){
        $aiEngine = AiService::getEngine('local');

        $robot = 11;
        $type = 1;
        $text = "PM管理文档";
        $matchingScore = 0.888;
        $lang = "cn";
        $console_ids = \app\admin\model\ai\Project::where('id', $robot)->value('console_ids');

        $vectorContent = $aiEngine->selectVectorData($robot,$type,$text, $matchingScore, $lang, $console_ids);
        dump($vectorContent);
        exit;

        $type = 1;
        $matchingScore = 0.25;
        $lang = "cn";
        $aiEngine->debug = true;
        $vectorContent = $aiEngine->selectVectorData($robot,$type,$text, $matchingScore, $lang, $console_ids);

        dump($vectorContent);exit;
    }

    public function add_card_data(){
        $type = 3;
        $aiEngine = AiService::getEngine('local');
        $aiEngine->debug = true;
        $user_card_ext =new \app\admin\model\ai\UserCardExt;
        $user_card_extList = $user_card_ext::where('sync', 0)->select();
        if(count($user_card_extList) > 0)
        {
            $ids = $insert = [];
            foreach($user_card_extList as $item){
                $userCard = \app\admin\model\ai\UserCard::where('id', $item['card_id'])->find();
                if(!$userCard)continue;

                $vectorizationArr = json_decode(base64_decode($item['vectorization']), true);
                $vectorization = $vectorizationArr['embedding'] ?? '';
                if($vectorization)
                {
                    $vectorization = json_encode($vectorization);
                    $q = str_replace(['"',"'"],"", $item['name']);
                    $insert[] = [
                        'robot'=>$userCard['robot'],
                        'type'=>$type,
                        'card_id'=>$item['card_id'],
                        'content'=>$q,
                        'embedding'=>$vectorization,
                        'status'=>1,
                        'console_id' => $userCard['console_id']
                    ];
                    $ids[] = $item['id'];
                    if(count($insert)>=100)
                    {
                        $aiEngine->addVectorData($insert);
                        $user_card_ext->where('id', 'in', $ids)->update(['sync'=>1]);
                        $ids = $insert = [];
                    }
                }else{
                    $user_card_ext->where('id', $item['id'])->update(['sync'=>2]);
                }
            }
            if($insert)
            {
                $aiEngine->addVectorData($insert);
                $user_card_ext->where('id', 'in', $ids)->update(['sync'=>1]);
            }
        }
    }

    public function mark(){
        $where = [];
        // $where = ['display_status'=>0,'createtime'=>['>', strtotime(date("Y-m-d"))-86400]];
        $where = ['console_id'=>['in',[191]],'display_status'=>0];
        // $where = ['id'=>3392];
        $list = \app\admin\model\ai\ConsoleFile::where($where)->limit(100)->select();
        foreach($list as $item)
        {
            dump($item);
            //推入训练队列中
            Queue::push('app\queue\job\UploadFileToConsole', [
                'url' => request()->domain() . $item['file'],
                'console_number' => $item['file_number'],
                'console_file_id' => $item['id'],
                'filename' => $item['name'],
                'engine_type' => 'local' ?? 'baidu'
            ], config('task.file'));

            // 推入知识图谱生成队列
            Queue::push('app\queue\job\BuildKnowledgeGraph', [
                'file_path' => ROOT_PATH . "public" . $item['file'],
                    'console_file_id' => $item['id']
            ], 'ai_file_buildgraph');
        }
        $this->success();
    }

    public function console1(){
        $consoleIds = \app\admin\model\ai\Project::where(['id'=>30])->field('id,name,console_ids')->select();
        foreach($consoleIds as $item)
        {
            dump($item);
            $where1 = ['id'=>['in',explode(",",$item['console_ids'])],'type'=>['<>','local']];
            $list = \app\admin\model\ai\Console::where($where1)->select();
            if($list)
            {
                dump($list);
            }
        }
        // exit;
        foreach($list as $item)
        {
			$query = $this->pg_connect();
			$row1 = $query->table($this->pgTable)
								->where(['file_id' => $item['id']])
								->find();
			if (!empty($row1)) {
				continue;
			}
            dump($item);
            //推入训练队列中
            Queue::push('app\queue\job\UploadFileToConsoleUpgrades', [
                'url' => request()->domain() . $item['file'],
                'console_number' => $item['file_number'],
                'console_file_id' => $item['id'],
                'filename' => $item['name'],
                'engine_type' => 'local'
            ],'ai_file_train_upgrades');
        }
        $this->success();
    }
    public function mark1(){
        // $project = \app\admin\model\ai\Project::where('id', $robot)->find();
        $projectList = \app\admin\model\ai\Project::where(['id'=>['in',[37,52]]])->limit(10)->select();
        foreach($projectList as $project)
        {
            if($project['type']=='local'){
                $this->error("项目类型已经为本地数据平台");
            }
            # 修改项目类型为本地数据平台
            $model_id = $project['model_id']==1 ? 9 : $project['model_id'];
            \app\admin\model\ai\Project::where('id', $project['id'])->update(['type'=>'local','model_id'=>$model_id]);
            if(empty($project['console_ids'])){
                continue;
            }
            $console_ids = explode(",", $project['console_ids']);
            
            $url_data = \app\admin\model\ai\UserUrl::where(['console_id'=>['in',$console_ids]])->select();
            foreach($url_data as $url1)
            {
                dump($url1);
                $internal = $url1['parse_type']==2 ?? false;
                //推入训练队列中
                Queue::push('app\queue\job\UrlToConsole', [
                    'url_id' => $url1['id'],
                    'url' => $url1['url'],
                    'internal' => $internal,
                    "user_id" => $url1['user_id'],
                    "console_id" => $url1['console_id']
                ],'url_to_console');
            }
            \app\admin\model\ai\Console::where(['id'=>['in', $console_ids]])->update(['type'=>'local']);
            $where = [];
            // $consoleIds = \app\admin\model\ai\Console::where(['type'=>'local'])->column('id');
            $where = ['console_id'=>['in',$console_ids]];
            $list = \app\admin\model\ai\ConsoleFile::where($where)->select();
            foreach($list as $item)
            {
                $query = $this->pg_connect();
                $row1 = $query->table($this->pgTable)
                                    ->where(['file_id' => $item['id']])
                                    ->find();
                if (!empty($row1)) {
                    continue;
                }
                dump($item);
                //推入训练队列中
                Queue::push('app\queue\job\UploadFileToConsoleUpgrades', [
                    'url' => request()->domain() . $item['file'],
                    'console_number' => $item['file_number'],
                    'console_file_id' => $item['id'],
                    'filename' => $item['name'],
                    'engine_type' => 'local'
                ],'ai_file_train_upgrades');
            }
        }
        $this->success();
    }

    public function mark2(){
        $list = \app\admin\model\ai\UserUrl::where(['console_id'=>18,'status'=>0])->select();
        foreach($list as $item)
        {
            $internal = $item['parse_type']==2 ?? false;
            //推入训练队列中
            Queue::push('app\queue\job\UrlToConsole', [
                'url_id' => $item['id'],
                'url' => $item['url'],
                'internal' => $internal,
                "user_id" => $item['user_id'],
                "console_id" => $item['console_id']
            ],'url_to_console');
            dump($item);
        }
        $this->success();
    }

	/** 链接PGsql数据库 */
	private function pg_connect()
	{
		$request = Db::connect(
			[
				'type' => 'pgsql',
				'hostname' => '127.0.0.1',
				'database' => config('postgres.database'),
				'username' => 'postgres',
				'password' => 'DRrTmhCKrLWs2b34',
				'hostport' => '5432'
			]
		);
		return $request;
	}

    public function cardtopgsql(){
        //推入训练队列中
        Queue::push('app\queue\job\CardToPGsql', [
            'type' => 1
        ],'card_to_pgsql');
    }

    public function rename(){
        $where['console_id'] = ['in', ['103','106','107']];

        $list = \app\admin\model\ai\ConsoleFile::where($where)->column('id,name');
        $this->success("",$list);
    }

    public function rename_action(){
        $data = <<<jjj
{
  "code": 1,
  "msg": "",
  "time": "1743068981",
  "data": {
    "2525": "DB37/T 668-2007 饲料级甜菜碱盐酸盐的测定 离子色谱法.pdf",
  }
}
jjj;
        $list = json_decode($data, true);
        foreach($list['data'] as $key=>$item) {
            $sql = \app\admin\model\ai\ConsoleFile::where(['id'=>$key])->fetchSql(true)->update(['name'=>$item]);
            dump($sql);
            $sql = \app\admin\model\ai\ConsoleFile::where(['id'=>$key])->update(['name'=>$item]);
            // exit;
        }
        exit;
    }

    public function create1(){
        $directory = '/mnt/sdc/wwwroot/ai-master/master/public/uploads/20250408';
        $fileName = '瑞士万通'; // 文件名

        // 检查目录是否存在
        if (!is_dir($directory)) {
            echo "目录不存在: $directory";
            exit;
        }

        $console_id = 107;
        $console = Db::name('ai_console')->where('id',$console_id)->find();
        $count = 0;
        // 打开目录
        if ($handle = opendir($directory)) {
            while (false !== ($entry = readdir($handle))) {
                // 去掉 "." 和 ".." 目录项
                if ($entry != "." && $entry != "..") {
                    // 匹配文件名
                    if (mb_stripos($entry, $fileName) !== false) {
                        $filePath = $directory . '/' . $entry;
                        if (is_file($filePath)) {
                            // dump($filePath);
                            // 获取文件信息
                            // $fileInfo = pathinfo($filePath);
                            // dump($fileInfo);exit;
                            $md5 = md5_file($filePath);
                            $suffix = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
                            $fileName1 =  $md5.".".$suffix;
                            dump($filePath);
                            dump($directory . '/' .$fileName1);
                            rename($filePath, $directory . '/' .$fileName1);

                            $url = "/uploads/20250408/".$fileName1;
                            $params['console_id'] = $console_id;
                            $params['file_number'] = '';
                            $params['name'] = $entry;
                            $params['display_status'] = 0;
                            $params['file'] = $url;
                            $params['user_id'] = 53;
                            switch ($suffix) {
                                case 'docx':
                                case 'doc':
                                    $type = 1;
                                    break;
                                case 'pdf':
                                    $type = 2;
                                    break;
                                case 'txt':
                                    $type = 3;
                                    break;
                                case 'xlsx':
                                case 'xls':
                                    $type = 4;
                                    break;
                                default:
                                    $type = 0;
                                    break;
                            }
                            $params['type'] = $type;
                            $params['file_size'] = filesize($directory . '/' .$fileName1);
                            $params['createtime'] = time();
                            $params['updatetime'] = time();
                            $console_file_id = $this->model->insertGetId($params);
                            // dump($sql);exit;
                            
                            // 推入训练队列中
                                Queue::push('app\queue\job\UploadFileToConsole', [
                                'url' => $params['file'],
                                    'console_number' => $console['console_number'],
                                    'console_file_id' => $console_file_id,
                                    'filename' => $params['name'],
                                    'engine_type' => $console['type'] ?? 'baidu'
                            ], config('task.file'));

                            // 推入知识图谱生成队列
                                Queue::push('app\queue\job\BuildKnowledgeGraph', [
                                'file_path' => ROOT_PATH . "public" . $params['file'],
                                    'console_file_id' => $console_file_id
                            ], 'ai_file_buildgraph');
                        }
                    }
                }
                $count++;
            }

            // 修改知识库整体知识图谱状态为未完成
            Db::name('ai_console')->where('id',$console_id)->update(['build_graph_status' => 0]);
            closedir($handle);
        } else {
            echo "无法打开目录: $directory";
        }
    }

    public function file_md5(){
        $list = \app\admin\model\ai\ConsoleFile::whereRaw('file_md5 IS NULL')->select();
        foreach($list as $item)
        {
            $file1 = $item['file'];
            $filePath = ROOT_PATH . "public" . $file1;
            if(is_file($filePath) && file_exists($filePath))
            {
                $md5 = md5_file($filePath);
                \app\admin\model\ai\ConsoleFile::where(['id'=>$item['id']])->update(['file_md5'=>$md5]);
                dump($md5);
            }
            else
            {
                dump("Path is a directory or file does not exist: " . $filePath);
            }
        }
    }

    /**
     * 文档增强
     */
    public function file_enhance(){
        // $file_id = 2151;
        // $engine = AiService::getEngine('localtest');
        // $res = $engine->buildKnowledgeEnhance($file_id);
        // print_r($res);exit;
        $robot = input('robot');
        $file_id = input('file_id');
        $console_ids = \app\admin\model\ai\Project::where(['id'=>$robot])->value('console_ids');
        $where = ['enhance_status'=>0,'console_id'=>['in', explode(',', $console_ids)],'display_status'=>2];
        if($file_id){
            $where['id'] = $file_id;
        }
        $fileList = \app\admin\model\ai\ConsoleFile::where($where)->select();
        // dump($fileList);exit;

        foreach($fileList as $item)
        {
            // 推入构建文档增强
            Queue::push('app\queue\job\BuildFileEnhance', [
                'file_id' => $item['id'],
            ], 'ai_file_emhance');
            
            dump($item);
            
        }
        exit;
    }
    

    public function batch_admin(){
        $arr = [
        ];
        $success = $error = 0;
        foreach($arr as $item){
            $row = \app\admin\model\User::whereRaw("username={$item[2]} OR mobile='{$item[1]}'")->find();
            if(empty($row))
            {
                if ($this->auth->register($item[2], $item[3], '', $item[1], ['nickname'=>$item[0],'group_id'=>1])){
                    $success++;
                }else{
                    $error++;
                }
            }
        }
        $this->success("创建账号".$success."条成功,失败".$error."条");
    }

    public function translate()
    {
        $translator = new \app\common\library\QwenTranslator();

        $termMap = [
            '磺丁基倍他环糊精钠' => 'Sulfobutylether-beta-cyclodextrin sodium',
            '注射剂' => 'injectable formulation'
        ];

        $zh = '我想做磺丁基倍他环糊精钠中的检测项目，你有方法吗?';
        $en = $translator->translate($zh, $termMap);

        echo $en;

    }

    public function addGraph(){
        $robot = input('robot');
        $console_ids = \app\admin\model\ai\Project::where(['id'=>$robot])->value('console_ids');
        $fileList = \app\admin\model\ai\ConsoleFile::where(['enhance_status'=>0,'console_id'=>['in', explode(',', $console_ids)],'display_status'=>2])->select();
        // dump($fileList);exit;

        foreach($fileList as $item)
        {
            // 推入知识图谱生成队列
                Queue::push('app\queue\job\BuildKnowledgeGraph', [
                'file_path' => ROOT_PATH . "public" . $item['file'],
                    'console_file_id' => $item['id']
            ], 'ai_file_buildgraph');
            dump($item);
        }
        exit;
    }

    public function vectorizeAndStoreTitles()
    {
        $robot = input('robot');
        $console_ids = \app\admin\model\ai\Project::where(['id'=>$robot])->value('console_ids');
        $fileList = \app\admin\model\ai\ConsoleFile::where([
            'console_id'=>['in', explode(',', $console_ids)],
            'display_status' => 2
        ])->select();

        if (empty($fileList)) {
            echo "没有需要处理的文档";
            return;
        }

        try {
            // 初始化 Embedding 类
		    $embeddings = new \app\common\library\Embeddings;

            // 获取 PGSQL 连接
            $pg = $this->pg_connect();

            $batchSize = 15; // 每批处理数量
            $batches = array_chunk($fileList, $batchSize);

            foreach ($batches as $batch) {

                $docIds = array_map('intval', array_map(function ($file) {
                    return $file['id'];
                }, $batch));

                // 查询已存在的 doc_id
                $existingDocIds = [];

                if (!empty($docIds)) {
                    // 手动构造数组字面量
                    $arrayLiteral = '{' . implode(',', $docIds) . '}';
                    $sql = "SELECT doc_id FROM public.document_titles WHERE doc_id = ANY(?)";
                    $existing = $pg->query($sql, [$arrayLiteral]);
                    
                    if (!empty($existing)) {
                        $existingDocIds = array_column($existing, 'doc_id');
                    }
                }

                // 过滤掉已存在的记录
                $newBatch = [];
                foreach ($batch as $index => $file) {
                    if (!in_array($file['id'], $existingDocIds)) {
                        $newBatch[] = $file;
                    }
                }

                if (empty($newBatch)) {
                    echo "本批次所有文档均已存在，跳过处理。\n";
                    continue;
                }

                // 调用 API 批量生成向量
                $titlesForVector = array_map(function ($file) {
                    return $file['name'];
                }, $newBatch);
                $input = json_encode(['input'=>$titlesForVector]);
                $vectorData = $embeddings->run($input);
                $vectorArray = json_decode($vectorData, true);

                if (!isset($vectorArray['data']) || count($vectorArray['data']) !== count($newBatch)) {
                    throw new \Exception("向量生成失败或数量不匹配: " . $vectorData);
                }
                foreach ($vectorArray['data'] as $index => $vecData) {
                    $file = $newBatch[$index];
                    $titleEmbedding = json_encode($vecData['embedding']);

                    $insertData = [
                        'knowledge_base_id' => $file['console_id'],
                        'doc_id' => $file['id'],
                        'title' => str_replace("'", "''", $file['name']),
                        'title_embedding' => $titleEmbedding,
                        'tags' => '{}',
                        'created_at' => date('Y-m-d H:i:s')
                    ];

                    // 使用参数化方式插入
                    $sql = "INSERT INTO \"public\".\"document_titles\" 
                                (knowledge_base_id, doc_id, title, title_embedding, tags, created_at) 
                            VALUES (?, ?, ?, ?, ?, ?)";

                    $params = [
                        $insertData['knowledge_base_id'],
                        $insertData['doc_id'],
                        $insertData['title'],
                        $insertData['title_embedding'],
                        $insertData['tags'],
                        $insertData['created_at']
                    ];

                    $pg->execute($sql, $params);
                }

                echo "本批次文档标题已成功写入数据库。\n";
            }

            echo "文档标题已全部成功向量化并写入数据库。";

        } catch (\Exception $e) {
            echo '错误：' . $e->getMessage();
        }
    }

    public function add_term(){
        $termMap = [
			'阿米卡星硫酸盐注射液' => 'Amikacin Sulfate Injection',
			'阿米卡星' => 'Amikacin',
			'β-葡聚糖' => 'Beta Glucan',
			'单糖分析' => '〈210〉 Monosaccharide Analysis',
			'阿米卡星硫酸盐' => 'Amikacin Sulfate',
			'胶原酶 II' => '〈89.2〉 Collagenase II',
			'锌测定' => '〈591〉 Zinc Determination',
			'重组治疗性单克隆抗体分析程序' => '〈129〉 Analytical Procedures for Recombinant Therapeutic Monoclonal Antibodies',
			'低聚糖分析' => '〈212〉 Oligosaccharide Analysis',
			'胶原酶 I' => '〈89.1〉 Collagenase I',
			'盐酸贝沙胆碱片' => 'Bethanechol Chloride Tablets',
			'醋酸钙片' => 'Calcium Acetate Tablets',
			'盐酸贝沙胆碱' => 'Bethanechol Chloride',
			'柠檬酸钙' => 'Calcium Citrate',
			'葡萄糖酸钙' => 'Calcium Gluconate',
			'醋酸钙胶囊' => 'Calcium Acetate Capsules',
			'羧甲基纤维素钠腹腔注射液（兽用）' => 'Carboxymethylcellulose Sodium Compounded Intraperitoneal Solution, Veterinary',
			'柠檬酸钙镁' => 'Calcium Magnesium Citrate',
			'头孢吡肟注射用' => 'Cefepime for Injection',
			'磺丁基倍他环糊精钠' => 'Betadex Sulfobutyl Ether Sodium',
			'红霉素眼膏' => 'Erythromycin Ointment',
			'胆碱非诺贝特' => 'Choline Fenofibrate',
			'胆碱 C 11 注射液' => 'Choline C 11 Injection',
			'依诺肝素钠注射液' => 'Enoxaparin Sodium Injection',
			'头孢吡肟盐酸盐' => 'Cefepime Hydrochloride',
			'依诺肝素钠' => 'Enoxaparin Sodium',
			'稀释硝酸异山梨酯' => 'Diluted Isosorbide Mononitrate',
			'环磷酰胺注射用' => 'Cyclophosphamide for Injection',
			'促红细胞生成素' => 'Epoetin',
			'达肝素钠' => 'Dalteparin Sodium',
			'红霉素眼用软膏' => 'Erythromycin Ophthalmic Ointment',
			'磷甲酸钠' => 'Foscarnet Sodium',
			'美福拉明甲磺酸盐' => 'Fenoldopam Mesylate',
			'氟脱氧葡糖 F 18 注射液' => 'Fludeoxyglucose F 18 Injection',
		];
        $robot = 61;
        $time = time();
        $insert = [];
        foreach ($termMap as $key => $value) {
            $insert[] = [
                'robot' => $robot,
                'chinese_term' => $key,
                'english_term' => $value,
                'createtime' => $time,
                'updatetime' => $time,
            ];
        }
        $return = \app\admin\model\ai\Term::insertAll($insert);
        if($return){
            $this->success("添加成功");
        }else{
            $this->error("添加失败");
        }
    }

    public function select_term(){
        $robot = 61;
        $termMap = \app\admin\model\ai\Term::where('robot', $robot)->column("chinese_term,english_term");
        dump($termMap);exit;
    }

    public function baidu(){
        $baiduSearch = new BaiduSearchService();

        $query = input('query', '河北天气');
        $site = input('site', ''); // 例如 www.weather.com.cn
        $timeRange = input('time_range', 'year'); // week/month/year 等

        try {
            $result = $baiduSearch->search($query, [
                'site' => $site ? [$site] : [],
                'time_range' => $timeRange
            ]);
            return json(['code' => 1, 'data' => $result]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }


    public function rename_new(){
        // 定义标准映射关系
        $patterns = [
            '/(DB\d+)T/'        => '$1_T',     // DBxxT → DBxx_T
            '/CJT/'             => 'CJ_T',     // CJT → CJ_T
            '/(GB)T/'           => '$1_T',     // GBT → GB_T
            '/HGT/'             => 'HG_T',     // HGT → HG_T
            '/GBZT/'            => 'GBZ_T',    // GBZT → GBZ_T
            '/JYT/'             => 'JY_T',     // JYT → JY_T
            '/NYT/'             => 'NY_T',     // NYT → NY_T
            '/SNT/'             => 'SN_T',     // SNT → SN_T
            '/YCT/'             => 'YC_T',     // YCT → YC_T
            '/DZT/'             => 'DZ_T',     // DZT → DZ_T
            '/SYT/'             => 'SY_T',     // SYT → SY_T
            '/GAT/'             => 'GA_T',     // GAT → GA_T
            '/NBT/'             => 'NB_T',     // NBT → NB_T
            '/WWT/'             => 'WW_T',     // WWT → WW_T
            '/DLT/'             => 'DL_T',     // DLT → DL_T
            '/HJT/'             => 'HJ_T',     // DLT → DL_T
        ];

        $list = $this->model->where('console_id', 103)->field('id,name')->select();
        foreach($list as $k=>$v){
            $name = $v['name'];

            // 第一步：替换标准缩写格式
            $name = preg_replace(array_keys($patterns), array_values($patterns), $name);

            // 第二步：为标准代号前后添加空格（如果不存在）
            $standardPatterns = [
                'GB_T', 'GBZ_T', 'CJ_T', 'JY_T', 'SN_T', 'YC_T',
                'DZ_T', 'SY_T', 'GA_T', 'NB_T', 'WW_T', 'DL_T', 'HJ_T'
            ];

            foreach ($standardPatterns as $std) {
                // 匹配所有出现的标准代号，并在其前后统一加空格
                $regex = "/(\b|$std\b)/";
                $name = preg_replace("/\b($std)\b/", " $1 ", $name);
            }

            // 第三步：确保标准代号和编号之间有一个空格（即使已存在也保持一致）
            foreach ($standardPatterns as $std) {
                // 匹配标准代号后紧跟编号的情况（比如：GB_T12345 → GB_T 12345）
                $regex = "/($std)([\d\-\.]+)/";
                $name = preg_replace($regex, "$1 $2", $name);
            }

            // 第四步：清理多余空格
            $name = preg_replace('/\s+/', ' ', $name);
            $name = trim($name);

            echo "原名：{$v['name']}，新名称：{$name}\n\n";
            $this->model->where('id', $v['id'])->update(['name' => $name]);
        }
        exit;
    }

    public function console_file_delete(){
        $delete = input('delete');
        if($delete=='099af53f601532dbd31e0ea99ffdeb64')
        {
            $where = [];
            $where = ['deletetime'=>['<', time() - 3600*24*7]];
            $list = \app\admin\model\ai\Console::onlyTrashed()->where($where)->select();
            foreach($list as $k=>$v){
                $consoleFileList = $this->model->where('console_id', $v['id'])->select();
                $aiEngine = AiService::getEngine($v['type']);
                foreach($consoleFileList as $kk=>$item){
                    $aiEngine->deleteConsoleFile($item['console_number'],$item);
                    $this->model->where('id', $item['id'])->delete(true);
                    dump($item);
                }
                dump($v);
            }
            $this->success('删除成功');
        }
    }

    public function project(){
        $list = User::where('project_id', null)->select();
        foreach($list as $item){
            $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$item['id'].',user_ids)')->value('id');
            dump($project_id);
            User::where('id', $item['id'])->update(['project_id' => $project_id]);
        }
    }

    public function case1(){
        $robot = input('robot');
        $console_ids = \app\admin\model\ai\Project::where('id', $robot)->value('console_ids');
        $consoleIdArr = explode(',',$console_ids);
        $consoleArr = \app\admin\model\ai\Console::where('id', 'in', $consoleIdArr)->column('id,name');
        $file = \app\admin\model\ai\ConsoleFile::where('console_id','in', $consoleIdArr)->select();
        $arr = [
            ['分类','名称','类型','链接','权重'],  //这一行是表头
          ];
        $order = 1;
        foreach($file as $item){
            $console_name = !empty($consoleArr[$item['console_id']])?$consoleArr[$item['console_id']]:'';
            $file = cdnurl($item['file'], true);
            $tmp = [$console_name, $item['name'], 'pdf', $file, $order];
            array_push($arr,$tmp);
            $order++;
        }
        $filename = "案例库" . date('Ymd');
        try {
            $cache = Cache::init();
            // 缓存数据
            $cache->set($filename, json_encode($arr), 60);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('导出成功',['url' => request()->domain() . '/api/master/console_file_log/downloadFile?filename='.$filename]);
    }

}