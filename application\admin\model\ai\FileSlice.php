<?php

namespace app\admin\model\ai;

use think\Model;


class FileSlice extends Model
{

    

    

    // 表名
    protected $name = 'ai_file_slice';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'enabled_text',
        'status_text',
        'createTime_text',
        'updateTime_text',
        'deleteTime_text'
    ];
    
    public function sync($console_id, $document_id){
        $done = true;
        $last_slice_id = $this->where('documentId', $document_id)->order('id','desc')->value('slice_id') ?? '';
        while($done){
            $python = '/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/python/knowledge.py 1 "%s" "%s" "'.$last_slice_id.'"';
            // dump(sprintf($python,$console_id, $document_id));
            $return = exec(sprintf($python,$console_id, $document_id));
            // $return = '{"data":[{"id":"57bdaad5-4bcc-428b-9b99-b08ec5fb660a","type":"RAW","knowledgeBaseId":"64d8e9be-2e2a-4cb2-a4a9-4ace8ce7d588","documentId":"cc918b35-45fa-41b1-986f-15614825f34b","content":"早期的人工取火、狩猎和农业的出现，导致人们对火的依赖性增强。火，在人类文明史上第一次大显身手，人类开始了用火来生产食物、取暖和防御野兽。现在的化学已经发展成为一门精深的科学，它广泛地应用于自然科学的各","enabled":true,"wordCount":119,"tokenCount":91,"status":"indexed","statusMessage":"None","createTime":1722436800,"updateTime":1722436801},{"id":"d8279ab4-19bb-4546-8230-3bcf0737d262","type":"RAW","knowledgeBaseId":"64d8e9be-2e2a-4cb2-a4a9-4ace8ce7d588","documentId":"cc918b35-45fa-41b1-986f-15614825f34b","content":"因此可以说化工不仅仅是一门学科，更是一项技术！化工的简介在古代，人类通过观察天体运行、矿物变化和生物生长，积累了许多有关化学变化和物质变化的知识，并逐渐认识到，日常生活中的燃烧、金屋的冶炼、酸碱盐的生","enabled":true,"wordCount":336,"tokenCount":258,"status":"indexed","statusMessage":"None","createTime":1722436800,"updateTime":1722436801},{"id":"1c13e295-ea63-46ff-9a34-f538caf24902","type":"RAW","knowledgeBaseId":"64d8e9be-2e2a-4cb2-a4a9-4ace8ce7d588","documentId":"cc918b35-45fa-41b1-986f-15614825f34b","content":"也可以认为化学工业是生产无机非金属材料及其制品（如硫酸、烧碱、纯碱等）的工业因此，从范围上说化工包括化学工业，它们与治金工业同厘于材料工业的一个分支，是制造人类所需的物质产品--即三大材料之一的材料","enabled":true,"wordCount":280,"tokenCount":215,"status":"indexed","statusMessage":"None","createTime":1722436800,"updateTime":1722436801},{"id":"92edb900-e9a2-4815-bbec-5ad7565743a8","type":"RAW","knowledgeBaseId":"64d8e9be-2e2a-4cb2-a4a9-4ace8ce7d588","documentId":"cc918b35-45fa-41b1-986f-15614825f34b","content":"化工的定义化工，是将天然资源及其加工产品，通过化学方法改变或赋予其特殊的化学、物理性质，形成各种专门产品的技术，如石油炼制技术、煤炭洗选加工技术、肥皂制造技术、纺织技术等。也可以认为化学工业是生产无机","enabled":true,"wordCount":303,"tokenCount":233,"status":"indexed","statusMessage":"None","createTime":1722436800,"updateTime":1722436801}],"marker":"","isTruncated":false,"nextMarker":"92edb900-e9a2-4815-bbec-5ad7565743a8","maxKeys":4}';
            $list = json_decode($return, true);
            // dump($list);exit;
            $insert = [];
            foreach($list['data'] as $item){
                $row = $this->where('slice_id', $item['id'])->find();
                if($row){
                    continue;
                }
                $python1 = '/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/python/knowledge.py 2 "%s" "%s"';
                // dump(sprintf($python,$console_id, $document_id));
                $return1 = exec(sprintf($python1,$console_id, $item['id']));
                $detail = json_decode($return1, true);
                $insert[] = [
                    'slice_id' => $detail['id'],
                    'type' => $detail['type'],
                    'knowledgeBaseId' => $detail['knowledgeBaseId'],
                    'documentId' => $detail['documentId'],
                    'content' => $detail['content'],
                    'enabled' => $detail['enabled'],
                    'wordCount' => $detail['wordCount'],
                    'tokenCount' => $detail['tokenCount'],
                    'status' => $detail['status'],
                    'statusMessage' => $detail['statusMessage'],
                    'createTime' => $detail['createTime'],
                    'updateTime' => $detail['updateTime']
                ];
                $last_slice_id = $item['id'];
            }
            $this->insertAll($insert);

            if($list['isTruncated']===false){
                $done = false;
            }
        }
        return ;
    }
    
    public function getEnabledList()
    {
        return ['1' => __('Enabled 1'), '0' => __('Enabled 0')];
    }

    public function getStatusList()
    {
        return ['50' => __('Status 50')];
    }


    public function getEnabledTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['enabled']) ? $data['enabled'] : '');
        $list = $this->getEnabledList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getCreatetimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['createTime']) ? $data['createTime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getUpdatetimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['updateTime']) ? $data['updateTime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getDeletetimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['deleteTime']) ? $data['deleteTime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setCreatetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setUpdatetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setDeletetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


}
