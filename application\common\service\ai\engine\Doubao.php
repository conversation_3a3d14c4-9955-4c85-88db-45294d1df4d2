<?php

namespace app\common\service\ai\engine;

use app\common\service\ai\BaseEngine;
use app\common\service\ai\BaseClient;
use app\common\service\IpService;

use Volc\Base\SignatureV4;
use Volc\Base\Model\SignParam;

use think\Db;
use think\Queue;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class <PERSON>ubao extends BaseEngine
{

	private $AccessKey = 'AKLTYTg1ZTQyODJlOGFhNGY2MGI0YWEyM2IwNGVjODQzYzg';
	private $Secret = 'TmpVM05XSmtZalU0TXpGak5ERTBaV0U0TnpjMk5EZzNabVUxT0RFMFpUVQ==';
	private $ApiKey = '87d2f9b6-a4e1-4004-8f79-79ba3676e1d6';

	private $Region = 'cn-beijing';
	private $Service = "ark";
	private $Host = 'open.volcengineapi.com';
	private $Version = '2024-01-01';


	/*
	* 允许上传的文件格式
	*/
	private $fileExt = [
		'pdf',
		'doc',
		'docx',
		'pptx',
		'markdown',
		'txt',
		'faq.xlsx',
	];



	/*
	* 创建上下文缓存获取对话id
	*/
	public function getChatId($modelId)
	{
		$url = 'https://ark.cn-beijing.volces.com/api/v3/context/create';
		$message = [
			[
				'role' => 'system',
				'content' => '你是一名AI助手'
			]
		];
		$params = [
			'model' => $modelId,
			'messages' => $message,
			'mode' => 'session'
		];
		// $params = json_encode($params);
		$headers = [
			'Content-Type' => "application/json",
			'Authorization' => 'Bearer ' . $this->ApiKey
		];

		$res = BaseClient::requestClient($url,'POST',['json' => $params],$headers);

		if (empty($res['id'])) {
			exception('请求缓存id接口出错，错误信息：'.json_encode($res));
		}
		return $res['id'];
	}


	/*
	* 聊天
	*/
	public function chat($modelId,$contextId,$msg,$robot,$card,$lang = 'cn')
	{
		$url = 'https://ark.cn-beijing.volces.com/api/v3/context/chat/completions';
		$stream = true; //流式输出
		$message = [
			[
				'role' => 'user',
				'content' => $msg
			]
		];
		$params = [
			'model' => $modelId,
			'context_id' => $contextId,
			'messages' => $message,
			'mode' => 'session',
			'stream' => $stream
		];
		$params = json_encode($params);
		$headers = [
			'Content-Type:application/json',
			'Authorization:Bearer ' . $this->ApiKey
		];

		$answerStr = '';
		$ip = request()->ip();
		$area = IpService::getArea($ip);

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($curl, $data) use (
			$lang,
			&$answerStr,
			$contextId,
			$robot,
			$msg,
			$ip,
			$area,
			$card) {


			// 判断返回的数据是否包含 [DONE]
		    if (strpos($data, '[DONE]') !== false) {
		        // 删除 [DONE] 数据
		        $data = str_replace('[DONE]', '', $data);
		        // 发送关闭 SSE 连接的响应头
		        // header('Connection: close');
		        // 刷新输出缓冲区并关闭输出缓冲
		        ob_end_flush();

		        /** 问答记录 */
		        if($answerStr)
		        {
		            // $references = base64_encode(json_encode($referencesListArr));
		            $msg_id = Db::name('ai_msg')
		            ->insertGetId([
		            	'robot'=>$robot, 
		            	'chatId'=> $contextId, 
		            	'msg'=>$msg, 
		            	'time'=>time(), 
		            	'ip'=> $ip, 
		            	'content'=>$answerStr,
		            	'city'=>$area, 
		            	'referrer'=>'', 
		            	'card'=>$card,
						'lang'=>$lang
		            ]);
		            $result = [
		                'msg' => $msg,
		                'robot' => $robot,
		                'msg_id' => $msg_id
		            ];
		            \fast\Http::sendAsyncRequest(request()->domain() . '/api/ai/card/tagExtractionOne',$result);
		        }
		        // 结束当前脚本
		        exit;
		    }
			$str = trim(str_replace('data:','',$data));
			$strArr = explode("\n",$str);
			foreach ($strArr as $v) {
				if (empty($v)) {
					continue;
				} 
				$v = json_decode($v,true);
				$text = $v['choices'][0]['delta']['content'];

				/** 翻译 */
                if ($lang && $lang != 'cn') {
                    $Translation = new \app\common\library\Translation();
                    preg_match_all('/(《.*》)/isU', $text, $matchResult);
                    $text = preg_replace_callback('/《([^》]+)》/', function($matches) {
                        return "";
                    }, $text);
                    $text = $Translation->xfyun($text, 'cn', $lang);
                    if(!empty($matchResult[1]))
                    {
                        $text .= implode("\n",$matchResult[1]);
                    }

                    $text .= ' ';
                    // foreach($referencesList as $rkey=>&$rvalue){
                    //     $referencesList[$rkey]['title'] = $Translation->xfyun($rvalue['title'], 'cn', $lang);
                    //     $referencesList[$rkey]['content'] = $Translation->xfyun($rvalue['content'], 'cn', $lang);
                    // }
                }
                $text = str_replace(["    ","~"], ["","—"], $text);
				$r = json_encode(['text' => $text,'references' => ''],JSON_UNESCAPED_UNICODE);

				$answerStr .= $v['choices'][0]['delta']['content'];
				$this->sendMessage($contextId,$r);
			}

		    return strlen($data);
		});
		$response = curl_exec($ch);
		if (curl_errno($ch)) {
		    echo 'Curl error: ' . curl_error($ch);
		}
		curl_close($ch);


	}


	public function addConsole($name,$memo)
	{
		$path = '/mnt/sdc/wwwroot/ai-master/python/huoshan/collection/create_collection.py';
		$c = $this->pythonPath . ' ' . $path . " %s %s";
        $console_number = exec(sprintf($c, $name, $memo));

        // if($console_number){
        	return $console_number;
        // 	// $params = [
        // 	// 	'console_number' => $console_number,
        // 	// 	'name' => $name,
        // 	// 	'user_id' => $user_id,
        // 	// 	'image' => $image
        // 	// ];
        // 	// return parent::addConsoleToDb($params);
        // }else{
        // 	exception('执行添加脚本失败');
        // }
	}


	public function deleteConsole($resource_id)
	{
		$path = '/mnt/sdc/wwwroot/ai-master/python/huoshan/collection/drop_collection.py';
		$c = $this->pythonPath . ' ' . $path . " %s";
        $res = exec(sprintf($c, $resource_id));
        
        // if ($res) {
        // 	// return parent::deleteConsoleFromDb($resource_id);
        // } else {
        // 	exception('执行删除脚本失败');
        // }
	}



	public function addConsoleFile($console_number,$console_file_id,$file_url,$filename) 
	{
		// $first_letter = strtolower(chr(rand(65, 90)));
		// $doc_id = $first_letter . \think\helper\Str::random(15);

		$doc_id = 'doubao_' . $console_file_id;

		$pathinfo = pathinfo($file_url);
        $doc_name = $filename;
        $filetype = $pathinfo['extension'];
        $filename = $pathinfo['filename'];
        //xlsx文件需要增加.faq后缀
        if ($filetype == 'xlsx') {
        	$parse = parse_url($file_url);
        	$new_name = $filename . '.faq.xlsx';
        	$relative_path = str_replace($doc_name,$new_name,$parse['path']);
        	$save_path = ROOT_PATH .'public' . $relative_path;
        	downloadFile($file_url,$save_path);
        	$file_url = request()->domain() . $relative_path;
        	$filetype = 'faq.xlsx';
        }

        if (!in_array($filetype,$this->fileExt)) {
        	exception('不允许的文件格式');
        }

		$path = '/mnt/sdc/wwwroot/ai-master/python/huoshan/collection/add_doc.py';
		$c = $this->pythonPath . ' ' . $path . " %s %s %s %s %s";

		$name = \app\admin\model\ai\Console::where('console_number',$console_number)->value('name');
        $res = exec(sprintf($c, $name,$file_url,$doc_id,$doc_name,$filetype));
        echo sprintf($c, $name,$file_url,$doc_id,$doc_name,$filetype);
        echo $res;
        if ($res == 'None') {
        	//修改状态为处理中
			\app\admin\model\ai\ConsoleFile::where("id", $console_file_id)->update(['file_number' => $doc_id,'display_status'=>1]);

			// //推入训练结果查询队列中
            // Queue::push('app\queue\job\FreshConsoleFileStatus', [
            //     'console_number' => $console_number,
            //     'engine_type' => 'doubao',
            //     'file_number' => $doc_id
            // ]);

			return true;
        } else {
        	//修改状态为失败
			\app\admin\model\ai\ConsoleFile::where("id", $console_file_id)->update(['file_number' => $doc_id,'display_status'=>3]);
			return false;
        }
        

        return $res;
	}


	public function deleteConsoleFile($console_number,$console_file) 
	{

		$name = \app\admin\model\ai\Console::where('console_number',$console_number)->value('name');
		$doc_id = 'doubao_' . $console_file['id'];
		$path = '/mnt/sdc/wwwroot/ai-master/python/huoshan/collection/delete_doc.py';
		$c = $this->pythonPath . ' ' . $path . " %s %s";
        $res = exec(sprintf($c, $name,$doc_id));

        // \app\admin\model\ai\ConsoleFile::where("id", $console_file_id)->delete();

	}


	/**
	 * 创建推理接入点
	 **/
	public function createEndPoint($rowId,$name)
	{

		$requestBody = [
            'ModelReference' => [
                'FoundationModel' => [
                    'Name' => 'Doubao-pro-32k',
                    'ModelVersion' => '241215'
                ]
            ],
            'Name' => $name,
        ];
        $requestBody = json_encode($requestBody);

       	$res = $this->create("POST", [], [], "CreateEndpoint", $requestBody);
       	Db::name('ai_project')->where('id',$rowId)->update(['console_key' => $res['Result']['Id']]);

	}


	/**
	 * 删除推理接入点
	 **/
	public function deleteEndPoint($id)
	{

		//删除前需要先停止推理点
		$requestBody = [
            'Id' => $id
        ];
        $requestBody = json_encode($requestBody);

        $this->create("POST", [], [], "StopEndpoint", $requestBody);
        $this->create("POST", [], [], "DeleteEndpoint", $requestBody);

        
	}



	public function freshConsoleFileStatus($console)
    {
        $path = '/mnt/sdc/wwwroot/ai-master/python/huoshan/collection/get_doc.py %s %s';

        $list = Db::name('ai_console_file')->where('console_id',$console['id'])->where('display_status','in',[0,1])->field('id,file_number,display_status')->select();
        $c = $this->pythonPath . ' ' . $path;
        foreach ($list as $v) {
        	$return = exec(sprintf($c,$console['name'],$v['file_number']));
        	// echo sprintf($c,$console['name'],$v['file_number']);
        	$return = str_replace('\'','"',$return);
        	$return = json_decode($return,true);

        	$display_status = 0;
        	if ($return['process_status'] === 0) {
        		$display_status = 2;
        	}
            if($display_status===0)continue;
            $row = \app\admin\model\ai\ConsoleFile::where(['file_number'=>$v['file_number']])->find();
            if($row && $row['display_status']===2)continue;
            \app\admin\model\ai\ConsoleFile::where(['file_number'=>$v['file_number']])->update([
            	'display_status'=>$display_status,
            	// 'word_count'=>$rval['word_count']
            ]);

        }

        


    }


	public function create($method, $query, $header, $action, $body)
	{
	    $credential = [
	        'accessKeyId' => $this->AccessKey,
	        'secretKeyId' => $this->Secret,
	        'service' => 'ark',
	        'region' => 'cn-beijing',
	    ];

	    $ContentType = "application/json; charset=UTF-8";

	    // 初始化签名结构体
	    $query = array_merge($query, [
	      'Action' => $action,
	      'Version' => $this->Version
	    ]);
	    ksort($query);
	    $requestParam = [
	        // body是http请求需要的原生body
	        'body' => $body,
	        'host' => $this->Host,
	        'path' => '/',
	        'method' => $method,
	        'contentType' => $ContentType,
	        'date' => gmdate('Ymd\THis\Z'),
	        // 'date' => '20250102T092713Z',
	        'query' => $query
	    ];

	    // 第三步：接下来开始计算签名。在计算签名前，先准备好用于接收签算结果的 signResult 变量，并设置一些参数。
	    // 初始化签名结果的结构体
	    $xDate = $requestParam['date'];
	    $shortXDate = substr($xDate, 0, 8);
	    $xContentSha256 = hash('sha256', $requestParam['body']);
	    $signResult = [
	        'Host' => $requestParam['host'],
	        'X-Content-Sha256' => $xContentSha256,
	        'X-Date' => $xDate,
	        'Content-Type' => $requestParam['contentType']
	    ];
	    // 第四步：计算 Signature 签名。
	    $signedHeaderStr = join(';', [
	    	// 'content-type',
	    	'host', 
	    	'x-content-sha256', 
	    	'x-date'
	    ]);
	    $canonicalRequestStr = join("\n", [
	        $requestParam['method'],
	        $requestParam['path'],
	        http_build_query($requestParam['query']),
	        join("\n", [
	        	// 'content-type:'.$requestParam['contentType'],
	        	'host:' . $requestParam['host'], 
	        	'x-content-sha256:' . $xContentSha256, 
	        	'x-date:' . $xDate
	        ]),
	        '',
	        $signedHeaderStr,
	        $xContentSha256
	    ]);

	    $hashedCanonicalRequest = hash("sha256", $canonicalRequestStr);
	    $credentialScope = join('/', [$shortXDate, $credential['region'], $credential['service'], 'request']);
	    $stringToSign = join("\n", ['HMAC-SHA256', $xDate, $credentialScope, $hashedCanonicalRequest]);
	    $kDate = hash_hmac("sha256", $shortXDate, $credential['secretKeyId'], true);
	    $kRegion = hash_hmac("sha256", $credential['region'], $kDate, true);
	    $kService = hash_hmac("sha256", $credential['service'], $kRegion, true);
	    $kSigning = hash_hmac("sha256", 'request', $kService, true);

	    $signature = hash_hmac("sha256", $stringToSign, $kSigning);
	    $signResult['Authorization'] = sprintf("HMAC-SHA256 Credential=%s, SignedHeaders=%s, Signature=%s", $credential['accessKeyId'] . '/' . $credentialScope, $signedHeaderStr, $signature);
	    $header = array_merge($header, $signResult);

	    // 第五步：将 Signature 签名写入 HTTP Header 中，并发送 HTTP 请求。
	    $client = new Client([
	        'base_uri' => 'https://' . $requestParam['host'],
	        'timeout' => 120.0,
	        'verify' => false
	    ]);

	    try {
	    	$response = $client->request($method, 'https://' . $requestParam['host'] . $requestParam['path'], [
		        'headers' => $header,
		        'query' => $requestParam['query'],
		        'body' => $requestParam['body']
		    ]);
	    	return json_decode($response->getBody(),true);
	    } catch (\Exception $e) {
        	// exception($e->getMessage());
	    }

	}



	/*
	* 应用调用生成图谱
	*/
	public function buildGraphChat($msg)
	{
		$url = 'https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions';
		$stream = false; //流式输出
		$message = [
			[
				'role' => 'user',
				'content' => $msg
			]
		];
		$params = [
			'model' => 'bot-20250214111001-744dk',
			'messages' => $message,
			'stream' => $stream,
			// 'max_tokens' => 8192
		];
		$params = json_encode($params);
		$headers = [
			'Content-Type:application/json',
			'Authorization:Bearer ' . $this->ApiKey
		];


		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
		// $answer = '';

		// curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($curl, $data) use ($answer) {


		// 	// 判断返回的数据是否包含 [DONE]
		//     if (strpos($data, '[DONE]') !== false) {
		//         // 删除 [DONE] 数据
		//         $data = str_replace('[DONE]', '', $data);
		//         // 发送关闭 SSE 连接的响应头
		//         // header('Connection: close');
		//         // 刷新输出缓冲区并关闭输出缓冲
		//         echo $answer;
		//         ob_end_flush();
		//         // 结束当前脚本
		//         exit;
		//     }
		// 	$str = trim(str_replace('data:','',$data));
		// 	$strArr = explode("\n",$str);
		// 	foreach ($strArr as $v) {
		// 		$v = json_decode($v,true);
		// 		if (empty($v)) {
		// 			continue;
		// 		} 
		// 		$text = $v['choices'][0]['delta']['content'];
		// 		// echo $text;
		// 		$answer .= $text;
		//         ob_flush();
		//         flush();
		// 	}

		//     return strlen($data);
		// });
		$response = curl_exec($ch);
		$err = curl_error($ch);
		// if (curl_errno($ch)) {
		//     echo 'Curl error: ' . curl_error($ch);
		// }
		curl_close($ch);
		return $response;
	}



	/*
	* 应用调用生成文件名分类信息
	*/
	public function buildFileCate($msg)
	{
		$url = 'https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions';
		$stream = false; //流式输出
		$message = [
			[
				'role' => 'user',
				'content' => $msg
			]
		];
		$params = [
			'model' => 'bot-20250320181500-ljqkc',
			'messages' => $message,
			'stream' => $stream,
			// 'max_tokens' => 8192
		];
		$params = json_encode($params);
		$headers = [
			'Content-Type:application/json',
			'Authorization:Bearer ' . $this->ApiKey
		];

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);


		$response = curl_exec($ch);
		$err = curl_error($ch);

		curl_close($ch);
		return $response;
	}






}