<?php

namespace app\admin\controller\ai;

use app\common\controller\Backend;
use app\common\service\ai\AiService;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use think\Queue;
use think\Log;

/**
 * 知识库管理
 *
 * @icon fa fa-circle-o
 */
class Console extends Backend
{

    /**
     * Console模型对象
     * @var \app\admin\model\ai\Console
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\Console;
        $this->dataLimit = true;
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                    
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','name','console_number','createtime','image', 'project_name', 'sort']);
                $projectName = \app\admin\model\ai\Project::where('FIND_IN_SET('.$row['id'].',console_ids)')->column('name');
                $row['project_name'] = implode(',', $projectName) ?? '';
                $row['image'] = cdnurl($row['image'], true);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            // $python = '/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/console_dataset.py "%s" 4 "%s"';
            // // dump(sprintf($python,$name));exit;
            // $console_number = exec(sprintf($python,$params['name'],$params['memo']));
            $AiEngine = AiService::getEngine($params['type']);
            $console_number = $AiEngine->addConsole($params['name'],$params['memo']);
            $params['console_number'] = $console_number;

            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);
            \app\admin\model\ai\ConsoleFileLog::insert(['user_id'=> $this->auth->id, 'action'=>1, 'type'=>2, 'filename'=> $params['name'], 'createtime' => time(), 'updatetime' => time()]);

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        $oldRow = $row;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            if($row['type'] == 'baidu')
            {
                $python = '/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/console_dataset.py "%s" 5 "%s" "%s"';
                // dump(sprintf($python, $params['console_number'], $params['name'], $params['memo']));exit;
                exec(sprintf($python, $params['console_number'], $params['name'], $params['memo']));
            }
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            
            $ConsoleFile = new \app\admin\model\ai\ConsoleFile;
            $admin_id = session('admin')['id'];
            $ConsoleFile->where('console_id',$ids)->update(['admin_id'=>$admin_id]);

            $content = "";
            if($oldRow['name'] != $params['name']){
                $content .= "知识库{$oldRow['name']}重命名，".$oldRow['name']."改为".$params['name'];
            }
            if($oldRow['image'] != $params['image']){
                $content .= ($content?"，":"")."知识库{$oldRow['name']}更换封面图";
            }
            if($content){
                \app\admin\model\ai\ConsoleFileLog::insert(['user_id'=> $this->auth->id, 'action'=>2, 'type'=>2, 'filename'=> $content, 'createtime' => time(), 'updatetime' => time()]);
            }


            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }


    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $list = $this->model->where($pk, 'in', $ids)->select();
        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
                $aiEngine = AiService::getEngine($item['type']);
                $aiEngine->deleteConsole($item['console_number']);

                \app\admin\model\ai\ConsoleFileLog::insert(['user_id'=> $this->auth->id, 'action'=>3, 'type'=>2, 'filename'=> $item['name'], 'createtime' => time(), 'updatetime' => time()]);
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }

    /**
     * 详情
     */
    public function look(){
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','name','console_number','createtime']);
                
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 同步
     */
    public function sync(){
        $ConsoleFile = new \app\admin\model\ai\ConsoleFile;
        $return = $ConsoleFile->sync([]);
        $this->success("同步成功,总条数:".$return['count']."条,成功:".$return['success']."条！");
    }

    /**
     * 复制
     */
    public function copy($ids = null){
        $consoleCopyService = new \app\admin\service\ai\ConsoleCopyService();
        $return = $consoleCopyService->copy($ids);
        if($return){
            $this->success('复制成功');
        }else{
            $this->error('复制失败');
        }
    }

    function generateUUIDv4() {
		$data = random_bytes(16);
		// 设置 UUID 版本 (4) 和 variant bits
		$data[6] = chr(ord($data[6]) & 0x0f | 0x40);
		$data[8] = chr(ord($data[8]) & 0x3f | 0x80);
	
		return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
	}

    /**
     * 命中测试
     */
    public function test(){
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','name','console_number','createtime']);
                
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

}
