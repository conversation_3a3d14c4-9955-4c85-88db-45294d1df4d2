<?php

namespace app\common\service\ai\engine\strategy;

use app\admin\model\ai\ConsoleFile;
use app\admin\model\ai\UrlData;

/**
 * 基础策略类 - 对应原 Local.php 的功能
 */
class BasicStrategy implements StrategyInterface
{
    protected $engine;
    
    public function setEngine($engine)
    {
        $this->engine = $engine;
    }
    
    /**
     * 构造引用内容
     */
    public function buildReferenceContent($project, $msg, $engine)
    {
        $vectorStr = '';
        $vectorId = 1;
        $vectorList = $this->queryVector($project, $msg);
        
        if($engine->getDebug()){
            dump($vectorList);exit;
        }
        
        $vectorScore = $project['vector_score']>0 ? $project['vector_score'] : 0.3;
        $list = [];
        
        foreach ($vectorList as &$item) {
            if ((isset($item['cosine_similarity']) && $item['cosine_similarity'] > $vectorScore) || 
                (isset($item['relevance_score']) && $item['relevance_score'] > $vectorScore)) {
                $consoleFile = ConsoleFile::where('id', $item['file_id'])->find();
                if($consoleFile)
                {
                    $file_number = $consoleFile['file_number'] ?? 0;
                    if(!empty($consoleFile['type']) && $consoleFile['type'] == 5){
                        $urlData = UrlData::where('file_number', $file_number)->find();
                        $fileName = !empty($urlData['title']) ? $urlData['title'] : $consoleFile['name'];
                        $filePath = !empty($urlData['url']) ? $urlData['url'] : cdnurl($consoleFile['file'], true);
                    }else{
                        $fileName = !empty($consoleFile['name']) ? $consoleFile['name'] : '';
                        $filePath = !empty($consoleFile['file'])? cdnurl($consoleFile['file'], true) : '';
                    }
                    $item['title'] = $fileName;
                    $item['file'] = $filePath;
                    $item['file_id'] = $item['file_id'];
                    $item['file_number'] = $file_number;
                    $list[] = $item;
                }
            }
        }
        
        if($list)
        {
            $list = $this->reorderReferences($list);
            $vectorNum = $project['vector_num']>0 ? $project['vector_num'] : 6;
            $list = array_slice($list, 0, $vectorNum);

            $mergedReferences = [];

            foreach ($list as $reference) {
                $fileId = $reference['file_id'];

                if (isset($mergedReferences[$fileId])) {
                    // 如果已存在相同的 file_id，则合并 content
                    $mergedReferences[$fileId]['content'] .= "\n" . $reference['content'];
                } else {
                    // 否则直接存储
                    $mergedReferences[$fileId] = $reference;
                }
            }

            // 转换为索引数组（去除键名）
            $list = array_values($mergedReferences);
            $references = [];

            foreach ($list as $item) {
                $vectorStr .= "[{$vectorId}] " . $item['title'] . "：" . $item['content'] . "\n";
                $slice = $item['content'];
                $references[] = [
                    'title'   => $item['title'],
                    'content' => $slice,
                    'file'    => $item['file'],
                    'cosine_similarity' => $item['cosine_similarity'] ?? 0,
                    'relevance_score' => $item['relevance_score'] ?? 0,
                    'document_id' => $item['file_number'],
                    'file_id' => $item['file_id']
                ];
                $vectorId++;
            }
            
            $engine->setReferences($references);
        }
        
        return $vectorStr;
    }
    
    /**
     * 构造请求payload
     */
    public function buildPayload($messages, $engine)
    {
        return [
            "model"              => $engine->model,
            "messages"           => $messages,
            "stream"             => true,
            "max_tokens"         => 2096,
            "stop"               => ["null"],
            "temperature"        => 0.3,
            "top_p"              => 0.3,
            "top_k"              => 50,
            "frequency_penalty"  => 1,
            "n"                  => 1,
            "response_format"    => ["type" => "text"],
            "usage"              => [
                "prompt_tokens"         => 3019,
                "completion_tokens"     => 104,
                "total_tokens"          => 3123,
                "prompt_tokens_details" => [
                    "cached_tokens" => 2048
                ]
            ]
        ];
    }
    
    /**
     * 查询向量库
     */
    protected function queryVector($project, $text)
    {
        $list = [];
        $query = $this->engine->getPgConnect();
        $text = mb_strcut($text, 0, 384);
        
        if($this->engine->getDebug()){
            dump($text);
        }
        
        if($text)
        {
            $vectorArr = $this->getEmbeddingsNew([$text]);
            $vectorContent = json_encode(['content'=>$vectorArr[0]['embedding']]);

            $vectorList = json_decode($vectorContent,true);
            if(!empty($vectorList['content']))
            {
                $queryVector = json_encode($vectorList['content']);
                $query->query("SET hnsw.ef_search = 100;");
                $sql = "SELECT id, file_id, content, 1 - (embedding <=> '{$queryVector}') AS cosine_similarity FROM {$this->engine->getPgTable()} where console_id  in ({$project['console_ids']}) ORDER BY embedding <=> '{$queryVector}' LIMIT 20";
                if($this->engine->getDebug()){
                    dump($sql);
                }
                $list = $query->query($sql);
            }
        }

        return $list;
    }
    
    /**
     * 重排引用内容
     */
    protected function reorderReferences($references, $msg = '')
    {
        $batchSize = 40;
        $path = '/mnt/sdc/wwwroot/ai-master/python/qianwen/rerank.py';
        $msgEscaped = str_replace(['"', "'"], ['"', "'"], $msg?$msg:$this->engine->getMsg());

        $sortedReferences = [];
        $unusedKeys = [];

        $batches = array_chunk($references, $batchSize, true);
        foreach ($batches as $batch) {
            $vectorArr = [];
            $keyMap = [];

            foreach ($batch as $originalKey => $v) {
                $content = mb_substr((isset($v['title'])?$v['title'].":":'').$v['content'], 0, 2000);
                $escapedContent = str_replace(['"', "'", "(", ")"], ['"', "'", "（", "）"], $content);
                $vectorArr[] = $escapedContent;
                $keyMap[] = $originalKey;
            }
            
            if($this->engine->getDebug()){
                dump($keyMap);
            }

            $vectorStr = implode('@@', $vectorArr);
            $cmd = $this->engine->getPythonPath() . ' ' . $path . ' "%s" "%s" ';
            $return = [];
            exec(sprintf($cmd, $msgEscaped, $vectorStr), $return, $returnCode);
            $usedKeys = [];

            if (!empty($return[0])) {
                $arr = json_decode($return[0], true);
                if (!empty($arr['output']['results'])) {
                    foreach ($arr['output']['results'] as $result) {
                        $indexInBatch = $result['index'];
                        if(isset($keyMap[$indexInBatch]))
                        {
                            $key = $keyMap[$indexInBatch];
                            $sortedReferences[] = [
                                'key' => $key,
                                'relevance_score' => $result['relevance_score']
                            ];
                            $usedKeys[] = $key;
                        }
                    }
                }
            }

            $unusedKeys = array_merge($unusedKeys, array_diff($keyMap, $usedKeys));
        }

        // 根据排序结果重建 references 顺序
        $final = [];

        if($this->engine->getDebug()){
            dump($sortedReferences);
        }
        
        foreach ($sortedReferences as $item) {
            $key = $item['key'];
            $references[$key]['relevance_score'] = $item['relevance_score'];
            $final[] = $references[$key];
        }

        // 追加未排序的
        foreach ($unusedKeys as $key) {
            $references[$key]['relevance_score'] = 0;
            $final[] = $references[$key];
        }
        
        // 按 relevance_score 倒序排序
        usort($final, function ($a, $b) {
            return ($b['relevance_score'] ?? 0) <=> ($a['relevance_score'] ?? 0);
        });

        return $final;
    }
    
    /**
     * 调用生成向量数组匹配对应关系
     */
    protected function getEmbeddingsNew($arr) {
        $input = json_encode(['input' => $arr]);

        $embeddings = new \app\common\library\Embeddings;
        $embeddingsStr = $embeddings->run($input);
        $embeddingsArr = json_decode($embeddingsStr, true);

        // 检查 data 键是否存在
        if (!isset($embeddingsArr['data'])) {
            usleep(500000); // 停顿 0.5 秒
            $embeddingsStr = $embeddings->run($input); // 重试
            $embeddingsArr = json_decode($embeddingsStr, true);
        }

        return $embeddingsArr['data'] ?? [];
    }
}
