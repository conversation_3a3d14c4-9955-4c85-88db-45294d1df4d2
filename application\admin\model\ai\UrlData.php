<?php

namespace app\admin\model\ai;

use think\Model;
use traits\model\SoftDelete;

class UrlData extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'ai_url_data';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [

    ];
    

    







    public function url()
    {
        return $this->belongsTo('app\admin\model\ai\UserUrl', 'url_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
