<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Queue;

/**
 * 首页接口
 */
class SyncCard extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    protected $embeddings = null;

    /**
     * 单条
     *
     */
	function index(){
		$siteId = $this->request->get('siteid');
        $apiKey = $this->request->get('apikey');
		$robot = $this->request->get('robot');
		$site = $this->request->get('site');
		$project_name = $this->request->get('projectname');
		$num = $this->request->get('num');
		$this->read($siteId,$apiKey,$robot,$site,$project_name,$num);
		exit;
	}
	

	 /** 处理列表 */
	 function list(){
		$site = 'drugs';
		$robot = 5;
		$apiKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMyIsIm5hbWUiOiJ5YW5nY2hhbyIsInJvbGUiOiJBZG1pbmlzdHJhdG9yIiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9pc3BlcnNpc3RlbnQiOiJGYWxzZSIsIm5iZiI6MTcyMzUxMzQwMiwiZXhwIjoxNzIzNTk5ODAyLCJpYXQiOjE3MjM1MTM0MDJ9.-c-Q9CAc7f_zXGL4Qin0SFZoM6emwtVJLf6UZmpCMZY';
		$list = [
			// ['siteid'=>'162','project_name'=>'GMP','name'=>'校内GMP-固体', 'number'=>'3'],
			// ['siteid'=>'2608','project_name'=>'gmp_qianchuli','name'=>'校内GMP-中药前处理', 'number'=>'0'],
			// ['siteid'=>'2677','project_name'=>'gmp_qiwuji','name'=>'校内GMP-气雾剂', 'number'=>'0'],
			// ['siteid'=>'4105','project_name'=>'gmp_gengyi','name'=>'校内GMP-更衣', 'number'=>'0'],
			// ['siteid'=>'780','project_name'=>'gmp_kongtiao','name'=>'校内GMP-公共', 'number'=>'0'],
			// ['siteid'=>'756','project_name'=>'gmp_zhenji','name'=>'校内GMP-针剂', 'number'=>'0'],
			// ['siteid'=>'732','project_name'=>'gmp_jiaonang','name'=>'校内GMP-口服液', 'number'=>'0'],
			// ['siteid'=>'4231','project_name'=>'A231_gengyi','name'=>'更衣、洗衣', 'number'=>'0'],
			// ['siteid'=>'3324','project_name'=>'C121_guti','name'=>'口服固体制剂', 'number'=>'0'],
			// ['siteid'=>'3186','project_name'=>'C132_ruanjiaonang','name'=>'软胶囊剂', 'number'=>'0'],
			// ['siteid'=>'3048','project_name'=>'A231_wujun','name'=>'无菌冻干粉针剂', 'number'=>'0'],
			// ['siteid'=>'3255','project_name'=>'A212_miejun','name'=>'注射剂最终灭菌产品', 'number'=>'0'],
			// ['siteid'=>'3117','project_name'=>'A231_baozhuang','name'=>'包装', 'number'=>'0'],
			// ['siteid'=>'4365','project_name'=>'gmp_jiucuo','name'=>'GMP纠错', 'number'=>'0'],
			['siteid'=>'4296','project_name'=>'gmpzhishidian','name'=>'GMP知识点收集', 'number'=>'0'],

		];
		foreach($list as $item){
			$this->read($item['siteid'],$apiKey,$robot,$site,$item['project_name'], $item['number']);
		}
	}
    public function read($siteId, $apiKey, $robot, $site, $project_name, $number = 0)
    {
        $this->embeddings = new \app\common\library\Embeddings;
        // $siteId = $this->request->get('siteid');
        // $apiKey = $this->request->get('apikey');
		// $robot = $this->request->get('robot');
		$host = "https://".$site.".jw100.com.cn";
		// $project_name = $this->request->get('projectname');
		
		$options = [
			'Authorization: Bearer ' . $apiKey
		];
        
        $url = $host.'/api/admin/cms/templates/templatesMatch?siteId='.$siteId;

		$headers = [
			'Authorization: Bearer ' . $apiKey,
			'Content-Type: application/json'
		];
		
		$options = [
			'http' => [
				'header' => implode("\r\n", $headers),
				'method' => 'GET',
				'timeout' => 30
			]
		];
		
		$context = stream_context_create($options);
		$result = @file_get_contents($url, false, $context);
		// $result = '{"channels":{"value":11929,"label":"云南农职","children":[{"value":11930,"label":"CB「细胞实验室」","children":[{"value":11931,"label":"超净工作台","children":[{"value":11962,"label":"导入","children":[],"count":3,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11964,"label":"安全","children":[],"count":5,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11963,"label":"原理","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11961,"label":"操作","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11960,"label":"学以致用","children":[],"count":3,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""}],"count":5,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11932,"label":"移液器","children":[{"value":11965,"label":"导入","children":[],"count":3,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11966,"label":"结构","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11967,"label":"操作","children":[],"count":2,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11968,"label":"学以致用","children":[],"count":2,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""}],"count":2,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11939,"label":"试剂","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11940,"label":"倒置荧光显微镜","children":[{"value":11970,"label":"导入","children":[],"count":3,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11972,"label":"安全","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11973,"label":"结构","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11974,"label":"操作","children":[],"count":5,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11971,"label":"学以致用","children":[],"count":2,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""}],"count":4,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11941,"label":"冷藏柜","children":[],"count":2,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11942,"label":"二氧化碳培养箱","children":[{"value":11976,"label":"导入","children":[],"count":3,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11977,"label":"安全","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11978,"label":"结构","children":[],"count":2,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11979,"label":"操作","children":[],"count":3,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11975,"label":"学以致用","children":[],"count":2,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""}],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11944,"label":"气瓶","children":[{"value":11980,"label":"导入","children":[],"count":3,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11981,"label":"安全","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""}],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11938,"label":"个人防护用品","children":[],"count":5,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11934,"label":"管理规定","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11935,"label":"操作规范","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11936,"label":"安全制度","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11943,"label":"封面缩略图链接","children":[],"count":27,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11933,"label":"操作要求","children":[{"value":11937,"label":"废弃处理","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11983,"label":"试剂要求","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11982,"label":"操作要求","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11984,"label":"废弃处理","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""}],"count":0,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""}],"count":0,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11945,"label":"AI","children":[],"count":0,"channelTemplateId":0,"contentTemplateId":0,"linkType":"None","linkUrl":""},{"value":11946,"label":"SM「系统菜单-2」","children":[],"count":0,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11947,"label":"右上菜单","children":[],"count":0,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11948,"label":"LG「免密码登录」","children":[],"count":0,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11949,"label":"注册","children":[],"count":0,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11950,"label":"个人信息","children":[],"count":0,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11951,"label":"SideM「侧边菜单」","children":[{"value":11952,"label":"路线1","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11953,"label":"签到","children":[],"count":0,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11954,"label":"分享","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11955,"label":"测试","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11956,"label":"九宫格1","children":[],"count":9,"channelTemplateId":1008,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11957,"label":"AI对话","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11958,"label":"项目简介","children":[],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""}],"count":1,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},{"value":11959,"label":"SceneDes「场景描述」","children":[],"count":2,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""}],"count":0,"channelTemplateId":1006,"contentTemplateId":1007,"linkType":"None","linkUrl":""},"channelTemplates":[{"siteId":11929,"templateName":"九宫格","templateType":"ChannelTemplate","relatedFileName":"T_gongge.html","createdFileFullName":"index.html","createdFileExtName":".html","defaultTemplate":false,"content":null,"id":1008,"guid":"15ab2dba-8235-4cae-a275-aa50a7e9db57","createdDate":"2024-03-27 17:16:38","lastModifiedDate":"2024-03-27 17:16:38"},{"siteId":11929,"templateName":"系统栏目模板","templateType":"ChannelTemplate","relatedFileName":"T_系统栏目模板.html","createdFileFullName":"index.html","createdFileExtName":".html","defaultTemplate":true,"content":null,"id":1006,"guid":"9b5d639b-1aa9-4ea5-b82c-31490ad623e7","createdDate":"2024-03-27 17:16:38","lastModifiedDate":"2024-03-27 17:16:41"}],"contentTemplates":[{"siteId":11929,"templateName":"系统内容模板","templateType":"ContentTemplate","relatedFileName":"T_系统内容模板.html","createdFileFullName":"index.html","createdFileExtName":".html","defaultTemplate":true,"content":null,"id":1007,"guid":"e484bc09-2f10-4d25-a84c-ba4ec05ed6bd","createdDate":"2024-03-27 17:16:38","lastModifiedDate":"2024-03-27 17:16:41"}]}';
		$data = json_decode($result,true);
		$insert = [];
		$detail_url = $host.'/api/admin/cms/contents/contents/actions/list';
		$Vrhot = new \app\admin\model\ai\Vrhot;
		// dump($data['channels']['children']);exit;
		// $number = $site=='drugs'?3:0;
		foreach($data['channels']['children'][$number]['children'] as $item)
		{
			if(empty($item['children']))
			{
				$tag = $item['label'];
				$url = $host . '/'.$project_name.'/channels/'.$item['value'].'.html';
				
				$post = [
					'siteId' => $siteId,
					'channelId' => $item['value'],
					'page' => 1,
					'searchType' => 'Title',
				];
				$options1 = [
					'http' => [
						'header' => implode("\r\n", $headers),
						'method' => 'POST',
						'content' => json_encode($post),
						'timeout' => 30
					]
				];
				
				$context1 = stream_context_create($options1);
				$result1 = @file_get_contents($detail_url, false, $context1);
				// dump($result1);exit;
				// $result1 = '{"pageContents":[{"contents":"","imageUrlCount":0,"videoUrlCount":0,"fileUrlCount":0,"state":"已审核","sequence":1,"channelName":"导入","adminName":"文娟(wenjuan)","lastEditAdminName":"文娟(wenjuan)","userName":"","sourceName":"","channelId":11962,"siteId":11929,"adminId":6,"lastEditAdminId":6,"userId":0,"taxis":3,"groupNames":[],"tagNames":[],"sourceId":0,"referenceId":0,"templateId":0,"checked":true,"checkedLevel":1,"hits":0,"downloads":0,"title":"安全案例","subTitle":"","imageUrl":"","videoUrl":"/xbsys/upload/files/2024/4/b4bf76e32931c675.mp4","fileUrl":"","body":"","summary":"","author":"","source":"","top":false,"recommend":false,"hot":false,"color":false,"linkType":"None","linkUrl":null,"addDate":"2024-04-01 13:51:19","id":72,"guid":"8da8b37f-38c7-4de1-8ba5-4200b336acd6","createdDate":"2024-04-01 13:51:30","lastModifiedDate":"2024-06-13 16:08:06"},{"contents":"","imageUrlCount":0,"videoUrlCount":0,"fileUrlCount":0,"state":"已审核","sequence":2,"channelName":"导入","adminName":"文娟(wenjuan)","lastEditAdminName":"文娟(wenjuan)","userName":"","sourceName":"","channelId":11962,"siteId":11929,"adminId":6,"lastEditAdminId":6,"userId":0,"taxis":2,"groupNames":[],"tagNames":[],"sourceId":0,"referenceId":0,"templateId":0,"checked":true,"checkedLevel":1,"hits":0,"downloads":0,"title":"问题导入","subTitle":"","imageUrl":"/xbsys/upload/images/2024/4/1a9c0b43f77b0aaf.png","videoUrl":"","fileUrl":"/xbsys/upload/files/2024/4/7e4a893e1de2b139.mp3","body":"","summary":"","author":"","source":"","top":false,"recommend":false,"hot":false,"color":false,"linkType":"None","linkUrl":null,"addDate":"2024-04-01 13:51:31","id":73,"guid":"2eb34fb8-b7fb-407d-bfd6-abdf323627a5","createdDate":"2024-04-01 13:51:41","lastModifiedDate":"2024-06-13 16:08:06"},{"contents":"","imageUrlCount":0,"videoUrlCount":0,"fileUrlCount":0,"state":"已审核","sequence":3,"channelName":"导入","adminName":"文娟(wenjuan)","lastEditAdminName":"文娟(wenjuan)","userName":"","sourceName":"","channelId":11962,"siteId":11929,"adminId":6,"lastEditAdminId":6,"userId":0,"taxis":1,"groupNames":[],"tagNames":[],"sourceId":0,"referenceId":0,"templateId":0,"checked":true,"checkedLevel":1,"hits":0,"downloads":0,"title":"认知目标","subTitle":"","imageUrl":"/xbsys/upload/images/2024/4/7134407ccf233345.png","videoUrl":"","fileUrl":"/xbsys/upload/files/2024/4/fd06802398d19f80.mp3","body":"","summary":"","author":"","source":"","top":false,"recommend":false,"hot":false,"color":false,"linkType":"None","linkUrl":null,"addDate":"2024-04-01 13:51:42","id":74,"guid":"718fe60b-4fdb-4830-9ae6-fe2d90e72189","createdDate":"2024-04-01 13:51:51","lastModifiedDate":"2024-06-13 16:08:06"}],"total":3,"pageSize":30,"titleColumn":{"attributeName":"Title","displayName":"标题","inputType":"Text","width":160,"isList":true,"isSearchable":true},"columns":[{"attributeName":"Sequence","displayName":"序号","inputType":"Text","width":70,"isList":false,"isSearchable":false},{"attributeName":"Id","displayName":"内容Id","inputType":"Text","width":70,"isList":false,"isSearchable":true},{"attributeName":"Guid","displayName":"识别码","inputType":"Text","width":310,"isList":false,"isSearchable":true},{"attributeName":"SubTitle","displayName":"副标题","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"ImageUrl","displayName":"图片","inputType":"Image","width":100,"isList":false,"isSearchable":true},{"attributeName":"VideoUrl","displayName":"视频","inputType":"Video","width":160,"isList":false,"isSearchable":true},{"attributeName":"FileUrl","displayName":"附件","inputType":"File","width":160,"isList":false,"isSearchable":true},{"attributeName":"Body","displayName":"外部链接","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"Summary","displayName":"内容摘要","inputType":"TextArea","width":160,"isList":false,"isSearchable":true},{"attributeName":"Author","displayName":"作者","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"Source","displayName":"来源","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"LinkType","displayName":"链接类型","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"LinkUrl","displayName":"外部链接","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"ChannelId","displayName":"所属栏目","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"AddDate","displayName":"添加时间","inputType":"Text","width":160,"isList":true,"isSearchable":false},{"attributeName":"LastModifiedDate","displayName":"最后修改时间","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"GroupNames","displayName":"内容组","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"TagNames","displayName":"标签","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"AdminId","displayName":"添加人","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"LastEditAdminId","displayName":"最后修改人","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"UserId","displayName":"投稿用户","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"SourceId","displayName":"来源标识","inputType":"Text","width":310,"isList":false,"isSearchable":false},{"attributeName":"TemplateId","displayName":"内容模板","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"Hits","displayName":"点击量","inputType":"Text","width":70,"isList":false,"isSearchable":true},{"attributeName":"Downloads","displayName":"下载量","inputType":"Text","width":70,"isList":false,"isSearchable":true},{"attributeName":"CheckAdminId","displayName":"审核人","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"CheckDate","displayName":"审核时间","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"CheckReasons","displayName":"审核原因","inputType":"Text","width":160,"isList":false,"isSearchable":false}],"isAllContents":false,"checkedLevels":[{"label":-99,"text":"草稿"},{"label":0,"text":"待审核"},{"label":1,"text":"已审核"},{"label":-1,"text":"终审退稿"}],"permissions":{"isAdd":true,"isDelete":true,"isEdit":true,"isArrange":true,"isTranslate":true,"isCheck":true,"isCreate":true},"menus":[]}';
				
				$data1 = json_decode($result1,true);
				$image = '';
				foreach($data1['pageContents'] as $val){
					if($val['imageUrl']){
						$image = $host . $val['imageUrl'];
					}
				}
				if(empty($image) && isset($data1['pageContents'][0]['videoUrl'])){
					$image = $host . $data1['pageContents'][0]['videoUrl'];
				}else if(empty($image) && !isset($data1['pageContents'][0]['videoUrl'])){
					$image = '';
				}
				if(empty($image))continue;
				$row = $Vrhot::where(['robot' => $robot, 'siteid' => $siteId, 'tag' => $tag])->find();
				if($row)continue;
				$insert[] = [
					'robot' => $robot,
					'siteid' => $siteId,
					'tag' => $tag,
					'url' => $url,
					'image' => $image,
					'vectorization' => '',
					'createtime' => time(),
					'updatetime' => time()
				];

			}else{
				foreach($item['children'] as $child){
					if(empty($child['children']))
					{
						$tag = $child['label'];
						$url = $host . '/'.$project_name.'/channels/'.$child['value'].'.html';
						
						$post = [
							'siteId' => $siteId,
							'channelId' => $child['value'],
							'page' => 1,
							'searchType' => 'Title',
						];
						$options1 = [
							'http' => [
								'header' => implode("\r\n", $headers),
								'method' => 'POST',
								'content' => json_encode($post),
								'timeout' => 30
							]
						];
						
						$context1 = stream_context_create($options1);
						$result1 = @file_get_contents($detail_url, false, $context1);
						// dump($result1);exit;
						// $result1 = '{"pageContents":[{"contents":"","imageUrlCount":0,"videoUrlCount":0,"fileUrlCount":0,"state":"已审核","sequence":1,"channelName":"导入","adminName":"文娟(wenjuan)","lastEditAdminName":"文娟(wenjuan)","userName":"","sourceName":"","channelId":11962,"siteId":11929,"adminId":6,"lastEditAdminId":6,"userId":0,"taxis":3,"groupNames":[],"tagNames":[],"sourceId":0,"referenceId":0,"templateId":0,"checked":true,"checkedLevel":1,"hits":0,"downloads":0,"title":"安全案例","subTitle":"","imageUrl":"","videoUrl":"/xbsys/upload/files/2024/4/b4bf76e32931c675.mp4","fileUrl":"","body":"","summary":"","author":"","source":"","top":false,"recommend":false,"hot":false,"color":false,"linkType":"None","linkUrl":null,"addDate":"2024-04-01 13:51:19","id":72,"guid":"8da8b37f-38c7-4de1-8ba5-4200b336acd6","createdDate":"2024-04-01 13:51:30","lastModifiedDate":"2024-06-13 16:08:06"},{"contents":"","imageUrlCount":0,"videoUrlCount":0,"fileUrlCount":0,"state":"已审核","sequence":2,"channelName":"导入","adminName":"文娟(wenjuan)","lastEditAdminName":"文娟(wenjuan)","userName":"","sourceName":"","channelId":11962,"siteId":11929,"adminId":6,"lastEditAdminId":6,"userId":0,"taxis":2,"groupNames":[],"tagNames":[],"sourceId":0,"referenceId":0,"templateId":0,"checked":true,"checkedLevel":1,"hits":0,"downloads":0,"title":"问题导入","subTitle":"","imageUrl":"/xbsys/upload/images/2024/4/1a9c0b43f77b0aaf.png","videoUrl":"","fileUrl":"/xbsys/upload/files/2024/4/7e4a893e1de2b139.mp3","body":"","summary":"","author":"","source":"","top":false,"recommend":false,"hot":false,"color":false,"linkType":"None","linkUrl":null,"addDate":"2024-04-01 13:51:31","id":73,"guid":"2eb34fb8-b7fb-407d-bfd6-abdf323627a5","createdDate":"2024-04-01 13:51:41","lastModifiedDate":"2024-06-13 16:08:06"},{"contents":"","imageUrlCount":0,"videoUrlCount":0,"fileUrlCount":0,"state":"已审核","sequence":3,"channelName":"导入","adminName":"文娟(wenjuan)","lastEditAdminName":"文娟(wenjuan)","userName":"","sourceName":"","channelId":11962,"siteId":11929,"adminId":6,"lastEditAdminId":6,"userId":0,"taxis":1,"groupNames":[],"tagNames":[],"sourceId":0,"referenceId":0,"templateId":0,"checked":true,"checkedLevel":1,"hits":0,"downloads":0,"title":"认知目标","subTitle":"","imageUrl":"/xbsys/upload/images/2024/4/7134407ccf233345.png","videoUrl":"","fileUrl":"/xbsys/upload/files/2024/4/fd06802398d19f80.mp3","body":"","summary":"","author":"","source":"","top":false,"recommend":false,"hot":false,"color":false,"linkType":"None","linkUrl":null,"addDate":"2024-04-01 13:51:42","id":74,"guid":"718fe60b-4fdb-4830-9ae6-fe2d90e72189","createdDate":"2024-04-01 13:51:51","lastModifiedDate":"2024-06-13 16:08:06"}],"total":3,"pageSize":30,"titleColumn":{"attributeName":"Title","displayName":"标题","inputType":"Text","width":160,"isList":true,"isSearchable":true},"columns":[{"attributeName":"Sequence","displayName":"序号","inputType":"Text","width":70,"isList":false,"isSearchable":false},{"attributeName":"Id","displayName":"内容Id","inputType":"Text","width":70,"isList":false,"isSearchable":true},{"attributeName":"Guid","displayName":"识别码","inputType":"Text","width":310,"isList":false,"isSearchable":true},{"attributeName":"SubTitle","displayName":"副标题","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"ImageUrl","displayName":"图片","inputType":"Image","width":100,"isList":false,"isSearchable":true},{"attributeName":"VideoUrl","displayName":"视频","inputType":"Video","width":160,"isList":false,"isSearchable":true},{"attributeName":"FileUrl","displayName":"附件","inputType":"File","width":160,"isList":false,"isSearchable":true},{"attributeName":"Body","displayName":"外部链接","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"Summary","displayName":"内容摘要","inputType":"TextArea","width":160,"isList":false,"isSearchable":true},{"attributeName":"Author","displayName":"作者","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"Source","displayName":"来源","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"LinkType","displayName":"链接类型","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"LinkUrl","displayName":"外部链接","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"ChannelId","displayName":"所属栏目","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"AddDate","displayName":"添加时间","inputType":"Text","width":160,"isList":true,"isSearchable":false},{"attributeName":"LastModifiedDate","displayName":"最后修改时间","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"GroupNames","displayName":"内容组","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"TagNames","displayName":"标签","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"AdminId","displayName":"添加人","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"LastEditAdminId","displayName":"最后修改人","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"UserId","displayName":"投稿用户","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"SourceId","displayName":"来源标识","inputType":"Text","width":310,"isList":false,"isSearchable":false},{"attributeName":"TemplateId","displayName":"内容模板","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"Hits","displayName":"点击量","inputType":"Text","width":70,"isList":false,"isSearchable":true},{"attributeName":"Downloads","displayName":"下载量","inputType":"Text","width":70,"isList":false,"isSearchable":true},{"attributeName":"CheckAdminId","displayName":"审核人","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"CheckDate","displayName":"审核时间","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"CheckReasons","displayName":"审核原因","inputType":"Text","width":160,"isList":false,"isSearchable":false}],"isAllContents":false,"checkedLevels":[{"label":-99,"text":"草稿"},{"label":0,"text":"待审核"},{"label":1,"text":"已审核"},{"label":-1,"text":"终审退稿"}],"permissions":{"isAdd":true,"isDelete":true,"isEdit":true,"isArrange":true,"isTranslate":true,"isCheck":true,"isCreate":true},"menus":[]}';
						
						$data1 = json_decode($result1,true);
						$image = '';
						foreach($data1['pageContents'] as $val){
							if($val['imageUrl']){
								$image = $host . $val['imageUrl'];
							}
						}
						if(empty($image) && isset($data1['pageContents'][0]['videoUrl'])){
							$image = $host . $data1['pageContents'][0]['videoUrl'];
						}else if(empty($image) && !isset($data1['pageContents'][0]['videoUrl'])){
							$image = '';
						}
						if(empty($image))continue;
						$row = $Vrhot::where(['robot' => $robot, 'siteid' => $siteId, 'tag' => $tag])->find();
						if($row)continue;
						$insert[] = [
							'robot' => $robot,
							'siteid' => $siteId,
							'tag' => $tag,
							'url' => $url,
							'image' => $image,
							'vectorization' => '',
							'createtime' => time(),
							'updatetime' => time()
						];

					}else{
						foreach($child['children'] as $child2){
							$tag = $item['label'] . "-" . $child['label'] . "-" . $child2['label'];
							$url = $host . '/'.$project_name.'/channels/'.$child2['value'].'.html';
							
							$post = [
								'siteId' => $siteId,
								'channelId' => $child2['value'],
								'page' => 1,
								'searchType' => 'Title',
							];
							$options1 = [
								'http' => [
									'header' => implode("\r\n", $headers),
									'method' => 'POST',
									'content' => json_encode($post),
									'timeout' => 30
								]
							];
							
							$context1 = stream_context_create($options1);
							$result1 = @file_get_contents($detail_url, false, $context1);
							// dump($result1);exit;
							// $result1 = '{"pageContents":[{"contents":"","imageUrlCount":0,"videoUrlCount":0,"fileUrlCount":0,"state":"已审核","sequence":1,"channelName":"导入","adminName":"文娟(wenjuan)","lastEditAdminName":"文娟(wenjuan)","userName":"","sourceName":"","channelId":11962,"siteId":11929,"adminId":6,"lastEditAdminId":6,"userId":0,"taxis":3,"groupNames":[],"tagNames":[],"sourceId":0,"referenceId":0,"templateId":0,"checked":true,"checkedLevel":1,"hits":0,"downloads":0,"title":"安全案例","subTitle":"","imageUrl":"","videoUrl":"/xbsys/upload/files/2024/4/b4bf76e32931c675.mp4","fileUrl":"","body":"","summary":"","author":"","source":"","top":false,"recommend":false,"hot":false,"color":false,"linkType":"None","linkUrl":null,"addDate":"2024-04-01 13:51:19","id":72,"guid":"8da8b37f-38c7-4de1-8ba5-4200b336acd6","createdDate":"2024-04-01 13:51:30","lastModifiedDate":"2024-06-13 16:08:06"},{"contents":"","imageUrlCount":0,"videoUrlCount":0,"fileUrlCount":0,"state":"已审核","sequence":2,"channelName":"导入","adminName":"文娟(wenjuan)","lastEditAdminName":"文娟(wenjuan)","userName":"","sourceName":"","channelId":11962,"siteId":11929,"adminId":6,"lastEditAdminId":6,"userId":0,"taxis":2,"groupNames":[],"tagNames":[],"sourceId":0,"referenceId":0,"templateId":0,"checked":true,"checkedLevel":1,"hits":0,"downloads":0,"title":"问题导入","subTitle":"","imageUrl":"/xbsys/upload/images/2024/4/1a9c0b43f77b0aaf.png","videoUrl":"","fileUrl":"/xbsys/upload/files/2024/4/7e4a893e1de2b139.mp3","body":"","summary":"","author":"","source":"","top":false,"recommend":false,"hot":false,"color":false,"linkType":"None","linkUrl":null,"addDate":"2024-04-01 13:51:31","id":73,"guid":"2eb34fb8-b7fb-407d-bfd6-abdf323627a5","createdDate":"2024-04-01 13:51:41","lastModifiedDate":"2024-06-13 16:08:06"},{"contents":"","imageUrlCount":0,"videoUrlCount":0,"fileUrlCount":0,"state":"已审核","sequence":3,"channelName":"导入","adminName":"文娟(wenjuan)","lastEditAdminName":"文娟(wenjuan)","userName":"","sourceName":"","channelId":11962,"siteId":11929,"adminId":6,"lastEditAdminId":6,"userId":0,"taxis":1,"groupNames":[],"tagNames":[],"sourceId":0,"referenceId":0,"templateId":0,"checked":true,"checkedLevel":1,"hits":0,"downloads":0,"title":"认知目标","subTitle":"","imageUrl":"/xbsys/upload/images/2024/4/7134407ccf233345.png","videoUrl":"","fileUrl":"/xbsys/upload/files/2024/4/fd06802398d19f80.mp3","body":"","summary":"","author":"","source":"","top":false,"recommend":false,"hot":false,"color":false,"linkType":"None","linkUrl":null,"addDate":"2024-04-01 13:51:42","id":74,"guid":"718fe60b-4fdb-4830-9ae6-fe2d90e72189","createdDate":"2024-04-01 13:51:51","lastModifiedDate":"2024-06-13 16:08:06"}],"total":3,"pageSize":30,"titleColumn":{"attributeName":"Title","displayName":"标题","inputType":"Text","width":160,"isList":true,"isSearchable":true},"columns":[{"attributeName":"Sequence","displayName":"序号","inputType":"Text","width":70,"isList":false,"isSearchable":false},{"attributeName":"Id","displayName":"内容Id","inputType":"Text","width":70,"isList":false,"isSearchable":true},{"attributeName":"Guid","displayName":"识别码","inputType":"Text","width":310,"isList":false,"isSearchable":true},{"attributeName":"SubTitle","displayName":"副标题","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"ImageUrl","displayName":"图片","inputType":"Image","width":100,"isList":false,"isSearchable":true},{"attributeName":"VideoUrl","displayName":"视频","inputType":"Video","width":160,"isList":false,"isSearchable":true},{"attributeName":"FileUrl","displayName":"附件","inputType":"File","width":160,"isList":false,"isSearchable":true},{"attributeName":"Body","displayName":"外部链接","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"Summary","displayName":"内容摘要","inputType":"TextArea","width":160,"isList":false,"isSearchable":true},{"attributeName":"Author","displayName":"作者","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"Source","displayName":"来源","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"LinkType","displayName":"链接类型","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"LinkUrl","displayName":"外部链接","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"ChannelId","displayName":"所属栏目","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"AddDate","displayName":"添加时间","inputType":"Text","width":160,"isList":true,"isSearchable":false},{"attributeName":"LastModifiedDate","displayName":"最后修改时间","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"GroupNames","displayName":"内容组","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"TagNames","displayName":"标签","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"AdminId","displayName":"添加人","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"LastEditAdminId","displayName":"最后修改人","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"UserId","displayName":"投稿用户","inputType":"Text","width":160,"isList":false,"isSearchable":true},{"attributeName":"SourceId","displayName":"来源标识","inputType":"Text","width":310,"isList":false,"isSearchable":false},{"attributeName":"TemplateId","displayName":"内容模板","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"Hits","displayName":"点击量","inputType":"Text","width":70,"isList":false,"isSearchable":true},{"attributeName":"Downloads","displayName":"下载量","inputType":"Text","width":70,"isList":false,"isSearchable":true},{"attributeName":"CheckAdminId","displayName":"审核人","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"CheckDate","displayName":"审核时间","inputType":"Text","width":160,"isList":false,"isSearchable":false},{"attributeName":"CheckReasons","displayName":"审核原因","inputType":"Text","width":160,"isList":false,"isSearchable":false}],"isAllContents":false,"checkedLevels":[{"label":-99,"text":"草稿"},{"label":0,"text":"待审核"},{"label":1,"text":"已审核"},{"label":-1,"text":"终审退稿"}],"permissions":{"isAdd":true,"isDelete":true,"isEdit":true,"isArrange":true,"isTranslate":true,"isCheck":true,"isCreate":true},"menus":[]}';
							
							$data1 = json_decode($result1,true);
							$image = '';
							foreach($data1['pageContents'] as $val){
								if($val['imageUrl']){
									$image = $host . $val['imageUrl'];
								}
							}
							if(empty($image) && isset($data1['pageContents'][0]['videoUrl'])){
								$image = $host . $data1['pageContents'][0]['videoUrl'];
							}else if(empty($image) && !isset($data1['pageContents'][0]['videoUrl'])){
								$image = '';
							}
							if(empty($image))continue;
							$row = $Vrhot::where(['robot' => $robot, 'siteid' => $siteId, 'tag' => $tag])->find();
							if($row)continue;
							$insert[] = [
								'robot' => $robot,
								'siteid' => $siteId,
								'tag' => $tag,
								'url' => $url,
								'image' => $image,
								'vectorization' => '',
								'createtime' => time(),
								'updatetime' => time()
							];
						}
					}
				}
			}
		}
		$segmentedArray = array_chunk($insert, 15);
		foreach($segmentedArray as $sval)
		{
			$newData = $data = [];
			foreach($sval as $key=>$item)
			{
				$data[] = $item['tag'];
			}
			$newData = $this->embeddings->getEmbeddingsNew($data);
			foreach($sval as $key1=>&$item1)
			{
				$vectorization = base64_encode(json_encode($newData[$key1]));
				$item1['vectorization'] = $vectorization;
			}
			$Vrhot::insertAll($sval);
			usleep(500000);
		}
		//推入训练队列中
		Queue::push('app\queue\job\CardToPGsql', [
			'type' => 2
		],'card_to_pgsql');
        exit;
    }
}
