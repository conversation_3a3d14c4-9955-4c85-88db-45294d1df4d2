<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx as Xlsx1;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use think\Cache;

/**
 * API-伴学统计
 */
class Msg extends Api
{
    // protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     * Msg模型对象
     * @var \app\admin\model\ai\Msg
     */
    protected $model = null;
    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\Msg;
    }
    

    /**
     * 列表
     */
    public function index()
    {
        $limit = $this->request->post('limit') ?? 10;
        $page = $this->request->post('page') ?? 1;
        $this->request->get([config('paginate.var_page') => $page]);
        $sort = 'id';
        $order = 'DESC';
        $where = [];
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $where['robot'] = $project_id;
        if($this->request->post('msg')){
            $where['msg'] = ['like',"%{$this->request->post('msg')}%"];
        }

        if($this->request->post('content')){
            $where['content'] = ['like',"%{$this->request->post('content')}%"];
        }

        if($this->request->post('ip')){
            $where['ip'] = ['like',"%{$this->request->post('ip')}%"];
        }

        if($this->request->post('city')){
            $where['city'] = ['like',"%{$this->request->post('city')}%"];
        }

        $start_date = $this->request->post('start_date') ?? date("Y-m-d", strtotime('-3month'));
        $end_date = $this->request->post('end_date') ?? date("Y-m-d", strtotime('+1 day'));
        if($this->request->post('start_date') && $this->request->post('end_date')){
            $where['time'] = ['between',[strtotime($start_date),strtotime($end_date)]];
        }
        $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
        foreach ($list as $row) {
            $row->visible(['id','chatId','time','msg','content','ip','city','referrer','model']);
            if($row['referrer'])
            {
                $references = json_decode(base64_decode($row['referrer']),true);
                $referrerList = [];
                foreach($references as $rval)
                {
                    $referrerList[] = $rval['title'];
                }
                $row['referrer'] = implode(',',$referrerList);
            }else{
                $row['referrer'] = "内部知识库.docx";
            }
            $row['content'] = str_ireplace("autoplay","",$row['content']);
        }
        // $consoleRow = \app\admin\model\ai\Console::where('FIND_IN_SET('.$this->auth->id.',user_id)')->find();
        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
        
    }
    /**
     * 导出
     */
    public function export(){
        
        $limit = $this->request->post('limit') ?? 10000;
        $page = $this->request->post('page') ?? 1;
        $this->request->get([config('paginate.var_page') => $page]);
        $sort = 'id';
        $order = 'DESC';
        $where = [];
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $where['robot'] = $project_id;
        if($this->request->post('msg')){
            $where['msg'] = ['like',"%{$this->request->post('msg')}%"];
        }

        if($this->request->post('content')){
            $where['content'] = ['like',"%{$this->request->post('content')}%"];
        }

        if($this->request->post('ip')){
            $where['ip'] = ['like',"%{$this->request->post('ip')}%"];
        }

        if($this->request->post('city')){
            $where['city'] = ['like',"%{$this->request->post('city')}%"];
        }

        $start_date = $this->request->post('start_date') ?? date("Y-m-d", strtotime('-3month'));
        $end_date = $this->request->post('end_date') ?? date("Y-m-d", strtotime('+1 day'));
        if($this->request->post('start_date') && $this->request->post('end_date')){
            $where['time'] = ['between',[strtotime($start_date),strtotime($end_date)]];
        }
        $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

        $arr = [
          ['问题','回复答案', '提问时间','来源文件', '城市', 'ip'],
        ];
        $tmp = [];
        foreach ($list as $row) {
            $row->visible(['id','chatId','time','msg','content','ip','city','referrer']);
            if($row['referrer'])
            {
                $references = json_decode(base64_decode($row['referrer']),true);
                $referrerList = [];
                foreach($references as $rval)
                {
                    $referrerList[] = $rval['title'];
                }
                $row['referrer'] = implode(',',$referrerList);
            }else{
                $row['referrer'] = "内部知识库.docx";
            }
            $row['content'] = str_ireplace("autoplay","",$row['content']);
            $tmp = [$row['msg'],$row['content'],date('Y-m-d H:i:s',$row['time']),$row['referrer'],$row['city'],$row['ip']];
            array_push($arr,$tmp);
        }

        $filename = '问答记录-' . substr(md5(time().uniqid()),0,6);
        try {
            $cache = Cache::init();
            // 缓存数据
            $cache->set($filename, json_encode($arr), 60);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('导出成功',['url' => request()->domain() . '/api/master/console_file_log/downloadFile?filename='.$filename]);
    }

    /**
     * 地区统计
     */
    public function area(){
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $start_date = $this->request->post('start_date') ?? date("Y-m-d", strtotime('-3 month'));
        $end_date = $this->request->post('end_date') ?? date("Y-m-d", strtotime('+1 day'));
        $group_list = collection(\app\admin\model\ai\Logs::where('robot',$project_id)->where(['createtime'=>['between',[strtotime($start_date),strtotime($end_date)]]])->field('area,count(1) num')->group('area')->select())->toArray();
        return json($group_list);
    }

    /**
     * 日期统计
     */
    public function date(){
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $start_date = $this->request->post('start_date') ?? date("Y-m-d", strtotime('-3 month'));
        $end_date = $this->request->post('end_date') ?? date("Y-m-d", strtotime('+1 day'));
        $group_list = collection(\app\admin\model\ai\Logs::where('robot',$project_id)->where(['createtime'=>['between',[strtotime($start_date),strtotime($end_date)]]])->field("DATE_FORMAT(FROM_UNIXTIME(createtime), '%Y-%m-%d') date,count(1) num")->group("DATE_FORMAT(FROM_UNIXTIME(createtime), '%Y-%m-%d')")->select())->toArray();
        return json($group_list);
    }

    /**
     * 统计分析
     */
    public function statistical_analysis(){
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $start_date = $this->request->post('start_date') && $this->request->post('start_date')!='undefined' ? $this->request->post('start_date') : date("Y-m-d", strtotime('-3 month'));
        $end_date = $this->request->post('end_date') && $this->request->post('end_date')!='undefined' ? $this->request->post('end_date') : date("Y-m-d", strtotime('+1 day'));

        /** 问答次数 */
        $qa_num = $this->model->where('robot',$project_id)->where(['time'=>['between',[strtotime($start_date),strtotime($end_date)]]])->count();
        /** 回答形式分析 */
        $card_num = $this->model->where(['robot'=>$project_id,'card'=>1])->where(['time'=>['between',[strtotime($start_date),strtotime($end_date)]]])->count();
        $card_type = $card_num>0?intval(($card_num/$qa_num)*100):0;
        $font_type = 100-$card_type;
        /** 问答准确度评分 */
        $qa_accuracy = 80;
        /** 生成临时excel交友大模型分析汇总数据 */
        // $this->createExcel($project_id, $start_date, $end_date);
        /** 问题类型分布 */
        $question_typearr = collection(\app\admin\model\ai\Tagextraction::where(['robot'=>$project_id,'name'=>['not like','%问%']])->whereRaw('CHAR_LENGTH(`name`)>2')->order('num','desc')->limit(6)->select())->toArray();
        $question_typelist = [];
        $question_typenum = 0;
        foreach($question_typearr as $item){
            $question_typenum+=$item['num'];
        }
        foreach($question_typearr as $item){
            $key = str_replace("\r","",$item['name']);
            $question_typelist[$key] = $item['num']>0?intval(($item['num']/$question_typenum)*100):0;
        }
        // $question_typelist = [
        //     '结构原理'=>30,
        //     '实验操作'=>12,
        //     '仪器维护'=>18,
        //     '故障处理'=>15,
        //     '数据分析'=>25
        // ];
        /** 高频问题排行 */
        $question_listarr = collection(\app\admin\model\ai\Tagextraction::where(['robot'=>$project_id,'msg_id'=>['<>',''],'name'=>['not like','%问%']])->order('num','desc')->limit(100)->select())->toArray();
        $top_questiondata = $top_questionlist = [];
        $num = 0;
        foreach($question_listarr as $key=>$item){
            if(in_array($item['msg_id'],$top_questiondata)){
                continue;
            }
            $top_questiondata[] = $item['msg_id'];
            $msg = $this->model->where(['robot'=>$project_id,'id'=>$item['msg_id']])->find();
            if(empty($msg)){
                continue;
            }
            $msg['content'] = str_ireplace("autoplay","",$msg['content']);
            if(count($top_questionlist)>=20)break;
            $num++;
            if($msg['referrer'])
            {
                $refererList = json_decode(base64_decode($msg['referrer']),true);
            }else{
                $refererList = [
                    [
                        'title' => '高频问题内部知识库.docx',
                        'content' => $msg['content'],
                    ]
                ];
            }
            $type = (stripos($msg['content'],'.mp4') || stripos($msg['content'],'.jpg') || stripos($msg['content'],'.png'))?'多模态':'纯文本';
            if(strpos($msg['content'],'^')===false){
                $msg['content'] .= '^';
                foreach($refererList as $rkey1=>$rval1){
                    $msg['content'] .= '['.($rkey1+1).']';
                }
                $msg['content'] .= '^';
            }
            $top_questionlist[] = 
                [
                    'no'=>$num,
                    'name'=>$msg['msg'],
                    'num'=>$item['num'],
                    'content'=>$msg['content'],
                    'type'=>$type,
                    'referer'=>$refererList,
                    'tag' => $item['name']
                ];
        }
        /** 高频引用知识点 */
        
        $top_consolearr = collection($this->model->where(['robot'=>$project_id,'time'=>['between',[strtotime($start_date),strtotime($end_date)]],'referrer'=>['<>','']])->select())->toArray();
        $top_consolelist = [];
        foreach($top_consolearr as $item){
            $references = json_decode(base64_decode($item['referrer']),true);
            foreach($references as $rval)
            {
                $key = md5($rval['content']);
                if(isset($top_consolelist[$key]))
                {
                    $top_consolelist[$key]['num'] += 1;
                }else{
                    $top_consolelist[$key] = [
                        'title' => $rval['title'],
                        'content' => $rval['content'],
                        'num'=>1
                    ];
                }
            }
        }
        usort($top_consolelist, function($a, $b) {
            return $b['num'] - $a['num'];
        });
        $top_consolelist = array_slice($top_consolelist, 0, 6);
        
        $result = [
            "qa_num"=>$qa_num,
            "answer_category"=>['card_type'=>$card_type,'font_type'=>$font_type],
            'qa_accuracy'=>$qa_accuracy,
            'question_typelist'=>$question_typelist,
            'top_questionlist'=>$top_questionlist,
            'top_consolelist'=>$top_consolelist,
        ];

        return json($result);
    }

    public function test(){
        $project_id = 11;
        $start_date = $this->request->post('start_date') ?? date("Y-m-d", strtotime('-15day'));
        $end_date = $this->request->post('end_date') ?? date("Y-m-d", strtotime('+1 day'));
        $this->createExcel($project_id, $start_date, $end_date);
    }

    /**
     * 创建excel
     */
    function createExcel($project_id, $start_date, $end_date){
        $list = $this->model->where('robot',$project_id)->where(['time'=>['between',[strtotime($start_date),strtotime($end_date)]]])->select();

        $data = [];
        foreach($list as $item){
            $references = base64_decode($item['referrer']);
            $data[] = [
                'time' => date("Y-m-d",$item['time']),
                'question' => $item['msg'],
                'content' => $item['content'],
                'ip' => $item['ip'],
                'referrer' => $references
            ];
        }
        $spreadsheet = new Spreadsheet();

        $this->opsheet_one($spreadsheet,0,$data);

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="test.xlsx"');
        header('Cache-Control: max-age=0');
        $writer = new Xlsx1($spreadsheet);
        $writer->save('php://output');
        //删除清空：
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);
        exit;
    }

    // 处理表格
    public function opsheet_one($spreadsheet,$n, $data){
        $spreadsheet->createSheet();//创建sheet
        $objActSheet = $spreadsheet->setActiveSheetIndex($n);//设置当前的活动sheet
        

        $sheet = $spreadsheet->getActiveSheet($n)->setTitle('sheet');//设置sheet的名称

        //$spreadsheet->getActiveSheet($n)->getStyle('A1')->getFont()->setSize(20); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('A')->getFont()->setSize(10); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('B')->getFont()->setSize(10); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('C')->getFont()->setSize(10); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('D')->getFont()->setSize(10); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('E')->getFont()->setSize(10); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('F')->getFont()->setSize(10); //设置title的字体大小

        $spreadsheet->getActiveSheet($n)->getStyle('A')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('A')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('B')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('B')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('C')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('C')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('D')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('D')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('E')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('E')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('F')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('F')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中

        $spreadsheet->getActiveSheet($n)->getColumnDimension('A')->setWidth(15); //固定列宽
        $spreadsheet->getActiveSheet($n)->getColumnDimension('B')->setWidth(25); //固定列宽
        $spreadsheet->getActiveSheet($n)->getColumnDimension('C')->setWidth(15); //固定列宽
        $spreadsheet->getActiveSheet($n)->getColumnDimension('D')->setWidth(30); //固定列宽
        $spreadsheet->getActiveSheet($n)->getColumnDimension('E')->setWidth(30); //固定列宽
        $spreadsheet->getActiveSheet($n)->getColumnDimension('F')->setWidth(15); //固定列宽
        $spreadsheet->getActiveSheet($n)->getDefaultRowDimension()->setRowHeight(20);

        $styleArray1 = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' =>  \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN //细边框
                ]
            ]
        ];


        //表头
        $objActSheet->setCellValue('A1', '日期');
        $objActSheet->setCellValue('B1', '问题');
        $objActSheet->setCellValue('C1', '答案');
        $objActSheet->setCellValue('D1', 'IP地址');
        $objActSheet->setCellValue('E1', '知识来源');

        //数据
        if(!empty($data)){
            $i = 2;
            foreach ($data as $k => $v)
            {
                $objActSheet->setCellValue('A'.$i, $v['time']);
                $objActSheet->setCellValue('B'.$i, $v['question']);
                $objActSheet->setCellValue('C'.$i, $v['content']);
                $objActSheet->setCellValue('D'.$i, $v['ip']);
                $objActSheet->setCellValue('E'.$i, $v['referrer']);
                $i++;
            }
            $spreadsheet->getActiveSheet($n)->getStyle('A1'.':'.'E'.($i-1))->applyFromArray($styleArray1);
        }
    }
}
