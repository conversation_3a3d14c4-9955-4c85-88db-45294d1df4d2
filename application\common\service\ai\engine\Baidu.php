<?php

namespace app\common\service\ai\engine;

use app\common\service\ai\BaseEngine;
use app\common\service\IpService;

use think\Queue;
use think\Db;


class Baidu extends BaseEngine
{


    private $apiKey = 'bce-v3/ALTAK-Fbeva8yQdkU5DxiKXKymQ/864c383f3aac6294f59e64f510502945ab52ae31';
	private $lastMsgId;
    private $model = 'baidu';
	private $save_references = [];


    public function getChatId($console_key)
    {
        // $return['chatId'] = $chatId;
        $path = '/mnt/sdc/wwwroot/ai-master/app.py "'.$console_key.'" 1';
        $c = $this->pythonPath . ' ' . $path;
        $chatId = exec(sprintf($c));

        return $chatId;
    }


    public function chat($console_key,$chatId,$question,$robot,$card,$lang = 'cn')
    {

        // $conversation_id = db::name('ai_msg')->where(['id'=>$robot,'chatId'=>$chatId])->value('conversation_id');
        // if(empty($conversation_id))
        // {
        //     $conversation_id = exec('/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/app.py "'.$console_key.'" 1');
        // }
        $descriptorspec = [
            0 => ["pipe", "r"],  // 标准输入
            1 => ["pipe", "w"],  // 标准输出
            2 => ["pipe", "w"]   // 标准错误输出
        ];
        $systemPrompt = $question;
        if ($lang && $lang != 'cn') {
            $systemPrompt = "说明：请用英文回答，不能使用汉字。不要复述问题。直接的回答。用户问题:" . $question;
        }
        $process = proc_open('/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/app.py "'.$console_key.'" 2 "'.$chatId.'" "'.$systemPrompt.'" ', $descriptorspec, $pipes);
        // dump('/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/app.py "'.$console_key.'" "'.$question.'" ');
		$ip = request()->ip();
		$area = IpService::getArea($ip);
		$answerStr = '';
		$this->saveChatRecord($robot, $chatId, $question, $ip, $area, $card, $lang, $answerStr);
            
        if (is_resource($process)) {
            $answerStr = '';
            $referencesListArr = [];
            while ($s = fgets($pipes[1])) {
                if(strpos($s, '"coord": "')!==false)
                {
                    $s = str_replace(["'", '"box"', '"page_num"'],['"', "'box'", "'page_num'"],$s);
                }else{
                    $s = str_replace(["'"],['"'],$s);
                }
                $answer = json_decode($s,true);
                if(isset($answer['text'])){
                    if(is_string($answer['text']))
                    {
                        if($robot==28)
                        {
                            $text = $answer['text'];
                        }else{
                            $text = preg_replace("/\^(.*)\^/", "", $answer['text']);
                        }
                        $referencesList = isset($answer['references'])?$answer['references']:[];
                        if (!empty($referencesList)) {
                            foreach($referencesList as $key=>&$value){
                                if(!empty($value['document_id']))
                                {
                                    $fileRow = \app\admin\model\ai\ConsoleFile::where('file_number',$value['document_id'])->find();
                                    $value['title'] = $fileRow['name'] ?? '内部知识库.xlsx';
					                $value['file'] = !empty($fileRow['file']) ? cdnurl($fileRow['file'], true) : '';
                                }
                                $value['title'] = $value['title']? $value['title'] : ($value['document_name'] ?? '内部知识库.xlsx');
                            }
                            $this->save_references = $referencesList;
                        }
                        /** 翻译 */
                        if ($lang && $lang != 'cn' && $text != '\n') {
                            // $oldText = $text;
                            // $return = $this->trans($text, $referencesList, $lang);
                            // $text = $return['text'];
                            // if(strpos($oldText,"\n") !== false){
                            //     $text .= "\n";
                            // }
                            $Translation = new \app\common\library\Translation();
                            foreach($referencesList as $rkey=>&$rvalue){
                                $referencesList[$rkey]['title'] = $Translation->xfyun($rvalue['title'], 'cn', $lang);
                                $referencesList[$rkey]['content'] = $Translation->xfyun($rvalue['content'], 'cn', $lang);
                            }
                        }
                        $text = str_replace(["    ","~"], ["","—"], $text);
                        // $text = Convert::tag($text);
                        $data = json_encode(["text"=>$text,"references"=>$referencesList, 'msg_id'=>$this->lastMsgId]);
                        $this->sendMessage($chatId,$data);
                    }
                    if(is_string($answer['text']) && !empty($answer['text']))
                    {
                        $answerStr .= $answer['text'];
                    }
                }
            }
            fclose($pipes[0]);
            fclose($pipes[1]);
            fclose($pipes[2]);
            // 终止进程
            proc_close($process);

            /** 问答记录 */
            if($answerStr)
            {
                $this->saveChatRecord($robot, $chatId, $question, $ip, $area, $card, $lang, $answerStr);
                $result = [
                    'msg' => $question,
                    'robot' => $robot,
                    'msg_id' => $this->lastMsgId
                ];
                \fast\Http::sendAsyncRequest(request()->domain() . '/api/ai/card/tagExtractionOne',$result);
        }
            
        } else {
            die("没有找到合适的答案!");
        }
        
    }

    /**
	 * 保存问答记录
	 */
	private function saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr)
	{
		$referencesList = base64_encode(json_encode($this->save_references ?? []));
		$msgShort = mb_substr($msg, 0, 150);
		if($this->lastMsgId)
		{
			Db::name('ai_msg')->where('id', $this->lastMsgId)->update([
				'content' => $answerStr,
				'referrer'=> $referencesList,
			]);
		}else{
			$this->lastMsgId = Db::name('ai_msg')->insertGetId([
				'robot'   => $robot,
				'chatId'  => $contextId,
				'msg'     => $msgShort,
				'time'    => time(),
				'ip'      => $ip,
				'content' => $answerStr,
				'city'    => $area,
				'referrer'=> $referencesList,
				'card'    => $card,
				'lang'    => $lang,
				'model'   => $this->model
			]);
		}
	}

    /** 翻译 */
    public function trans($text, $referencesList , $lang = 'en'){
        $Translation = new \app\common\library\Translation();
        // 定义 Markdown 格式的正则表达式
        $markdownPatterns = [
            '/(\*\*|__)(.*?)\1/' => 'BOLD',       // 加粗
            '/(\*|_)(.*?)\1/' => 'ITALIC',         // 斜体
            '/\[(.*?)\]\((.*?)\)/' => 'LINK',       // 链接
            '/\!\[(.*?)\]\((.*?)\)/' => 'IMAGE',    // 图片
            '/\n(#{1,6}) (.*?)\n/' => 'HEADING',    // 标题
            '/\n(> .*?)\n/' => 'BLOCKQUOTE',       // 引用
            '/\n(\*|\+|-) (.*?)\n/' => 'LIST_ITEM',  // 列表项
            '/\n(1\.) (.*?)\n/' => 'ORDERED_LIST_ITEM', // 有序列表项
            '/\n(```.*?```)\n/' => 'CODE_BLOCK',   // 代码块
            '/\n(`).*?\1\n/' => 'INLINE_CODE',    // 行内代码
            '/\n(---|\*\*\*|\___)\n/' => 'HR',       // 水平线
        ];
        // 提取并替换 Markdown 格式为占位符
        $placeholders = [];
        $placeholderIndex = 0;
        foreach ($markdownPatterns as $pattern => $type) {
            $text = preg_replace_callback($pattern, function($matches) use (&$placeholders, &$placeholderIndex, $type) {
                $placeholder = "%%MD_PLACEHOLDER_$placeholderIndex%%";
                $placeholders[$placeholder] = [
                    'type' => $type,
                    'content' => $matches[0]
                ];
                $placeholderIndex++;
                return $placeholder;
            }, $text);
        }
        preg_match_all('/(《.*》)/isU', $text, $matchResult);
        $text = preg_replace_callback('/《([^》]+)》/', function($matches) {
            return "";
        }, $text);
        $translatedText = $Translation->xfyun($text, 'cn', $lang);
        // 恢复 Markdown 格式
        foreach ($placeholders as $placeholder => $originalMarkdown) {
            $translatedText = str_replace($placeholder, $originalMarkdown['content'], $translatedText);
        }
        if(!empty($matchResult[1]))
        {
            $translatedText .= implode("\n",$matchResult[1]);
        }
        $Translation = new \app\common\library\Translation();
        foreach($referencesList as $rkey=>&$rvalue){
            $referencesList[$rkey]['title'] = $Translation->xfyun($rvalue['title'], 'cn', $lang);
            $referencesList[$rkey]['content'] = $Translation->xfyun($rvalue['content'], 'cn', $lang);
        }
        return ['text' => $translatedText, 'references' => $referencesList];
    }

    public function addConsole($name, $memo)
    {

        $path = ' /mnt/sdc/wwwroot/ai-master/console_dataset.py "%s" 4 "%s"';
        $c = $this->pythonPath . ' ' . $path;
        $console_number = exec(sprintf($c, $name, $memo));

        // if($console_number){
            return $console_number;
        // }else{
        //     exception('执行添加脚本失败');
        // }
    }


    public function deleteConsole($resource_id)
    {
        
    }



    public function addConsoleFile($console_number,$console_file_id,$file_url,$filename) 
    {
        $path = '/mnt/sdc/wwwroot/ai-master/console_dataset.py "%s" 2 "%s"';
        $c = $this->pythonPath . ' ' . $path;
        $url = ROOT_PATH . 'public' . parse_url($file_url)['path'];
        $return = exec(sprintf($c, $console_number,$url));
        $consoleFile = json_decode($return,true);
        if(!empty($consoleFile['document_ids'][0])){
            \app\admin\model\ai\ConsoleFile::where("id", $console_file_id)->update(['file_number'=>$consoleFile['document_ids'][0],'display_status'=>1]);
            return true;
        }else{
            \app\admin\model\ai\ConsoleFile::where("id", $console_file_id)->update(['display_status'=>3]);
            return false;
        }

    }


    public function deleteConsoleFile($console_number,$console_file) 
    {
        $path = '/mnt/sdc/wwwroot/ai-master/console_dataset.py "%s" 3 "%s"';
        $c = $this->pythonPath . ' ' . $path;
        $return = exec(sprintf($c,$console_number,$console_file['file_number']));
    }





    public function freshConsoleFileStatus($console)
    {
        $path = '/mnt/sdc/wwwroot/ai-master/console_dataset.py "%s" 1';
        $c = $this->pythonPath . ' ' . $path;
        $return = exec(sprintf($c,$console['console_number']));
        $result = (new \app\admin\model\ai\ConsoleFile)->mdTOArr($return);
        foreach($result as $rval)
        {
            if($rval['meta']['file_id']=='null' || empty($rval['meta']['file_id']))continue;
            $display_status = 0;
            if($rval['word_count']>0)
            {
                $display_status = 2;
            }
            if($display_status===0)continue;
            $row = \app\admin\model\ai\ConsoleFile::where(['file_number'=>$rval['id']])->find();
            if($row && $row['display_status']===2)continue;
            \app\admin\model\ai\ConsoleFile::where(['file_number'=>$rval['id']])->update(['display_status'=>$display_status,'word_count'=>$rval['word_count']]);
        }

    }




    /*
    * 百度appbuilder按照标题切分文档
    */
    public function splitDocByTitle($filepath)
    {
        $path = '/mnt/sdc/wwwroot/ai-master/python/appbuilder/splitter_by_title.py';
        $c = $this->pythonPath .' '. $path . ' "' . $filepath .'"';

        $return = exec($c);

        return $return;
    }



    /*
    * 直接返回内容的chat
    */
    public function chatAndReturn($modelId,$conversation_id,$msg,$robot)
    {

        $stream = false;
        $params = [
            'app_id' => $modelId,
            'query' => $msg,
            'stream' => $stream,
            'conversation_id' => $conversation_id,
        ];
        $params = json_encode($params);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://qianfan.baidubce.com/v2/app/conversation/runs",
            CURLOPT_TIMEOUT => 30,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER  => false,
            CURLOPT_SSL_VERIFYHOST  => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $params,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey
            ),

        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $res = json_decode($response,true);
        if (!$res) {
            exception('接口响应错误:' . $response);
        }

        $references = [];
        foreach ($res['content'] as $v) {
            if ($v['result_type'] == 'docsearch_result') {
                foreach ($v['outputs']['references'] as $val) {
                    if (count($references) > 2) {
                        break 2;
                    }
                    $references[] = ['title' => $val['title'],'content' => $val['content']];
                }
            }
        }
        return json(['answer' => $res['answer'],'references' => $references]);
    }


    /*
    * 直接返回内容的chat(不带知识库，单模型聊天)
    */
    public function chatAndReturn2($messages)
    {

        $stream = false;
        // $messages = [
        //     ['role' => 'user','content' => '你好']
        // ];
        $params = [
            'model' => 'ernie-4.5-8k-preview',
            'messages' => $messages,
            // 'stream' => $stream,
        ];
        dump($params);
        $params = json_encode($params);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://qianfan.baidubce.com/v2/chat/completions",
            CURLOPT_TIMEOUT => 300,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER  => false,
            CURLOPT_SSL_VERIFYHOST  => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $params,

            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                // 'appid: ' . $this->appid,
                'Authorization: Bearer ' . $this->apiKey
            ),

        ));
        $response = curl_exec($curl);
        dump($response);die;
        $res = json_decode($response,true);
        if (!$res) {
            exception('接口响应错误:' . $response);
        }
        curl_close($curl);

        dump($res);die;

        $references = [];
        foreach ($res['content'] as $v) {
            if ($v['result_type'] == 'docsearch_result') {
                foreach ($v['outputs']['references'] as $val) {
                    if (count($references) > 2) {
                        break 2;
                    }
                    $references[] = ['title' => $val['title'],'content' => $val['content']];
                }
            }
        }
        return json(['answer' => $res['answer'],'references' => $references]);
    }


}