<?php

namespace app\admin\controller\ai;

use app\common\controller\Backend;
use app\common\service\ai\AiService;


use think\Db;

/**
 * 项目管理
 *
 * @icon fa fa-circle-o
 */
class Project extends Backend
{

    /**
     * Project模型对象
     * @var \app\admin\model\ai\Project
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\Project;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            $filter = $this->request->request('filter');
            $filterArr = (array)json_decode($filter, true);
            $project_name = '';
            $create_time = [];
            if (isset($filterArr['name'])) {
                $project_name = $filterArr['name'];
                unset($filterArr['name']);
            }
            if (isset($filterArr['createtime'])) {
                $create_time = explode(" - ",$filterArr['createtime']);
                unset($filterArr['createtime']);
            }
            $this->request->get(['filter' => json_encode($filterArr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $whereNew = [];
            if($project_name){
                $whereNew['project.name'] = ['like', '%' . $project_name . '%'];
            }
            if($create_time){
                $whereNew['project.createtime'] = ['between', [strtotime($create_time[0]), strtotime($create_time[1])]];
            }
            $list = $this->model
                    ->with(['unit'])
                    ->where($where)
                    ->where($whereNew)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','name','createtime','memo','console_key','url']);
                $row->visible(['unit']);
				$row->getRelation('unit')->visible(['name']);
                $row['url'] = 'https://aimaster.jw100.com.cn/font/#/?robot='.$row['id'];
                $row['createtime'] = date("Y-m-d H:i",$row['createtime']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 重写 selectpage 方法，添加 “所有” 选项
     */
    public function selectpage()
    {
        // 调用父类的 selectpage 获取原始数据
        $response = parent::selectpage();
        // 将响应对象转换为原始数组
        $content = json_decode($response->getContent(), true);
        // 添加 "所有" 项
        $keyValue = $this->request->post('keyValue');
        if(!$keyValue)
        {
            array_unshift($content['list'], [
                'id'   => 9999,
                'name' => '所有项目'
            ]);
        }

        return json($content);
    }


    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);

            //豆包应用创建推理接入点
            if ($params['type'] == 'doubao') {
                $aiEngine = AiService::getEngine('doubao');
                $aiEngine->createEndPoint($this->model->id,$params['name']);
            }

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //删除缓存
            $fileKey = md5($ids . "_en");
		    if (\think\Cache::tag('lang_file')->has($fileKey)) {
                \think\Cache::tag('lang_file')->rm($fileKey);
            }
            if (\think\Cache::tag('initialize_data')->has($fileKey)) {
                \think\Cache::tag('initialize_data')->rm($fileKey); 
            }
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }


    
    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
                //豆包应用删除推理接入点
                if ($item['type'] == 'doubao') {
                    $aiEngine = AiService::getEngine('doubao');
                    $aiEngine->deleteEndPoint($item['console_key']);
                }
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }

    /**
     * 知识库统计
     */
    public function stat_console()
    {
        $model = new \app\admin\model\ai\Console;
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            $filter = $this->request->request('filter');
            $filterArr = (array)json_decode($filter, true);
            $project_name = '';
            $create_time = [];
            if (isset($filterArr['name'])) {
                $project_name = $filterArr['name'];
                unset($filterArr['name']);
            }
            if (isset($filterArr['createtime'])) {
                $create_time = explode(" - ",$filterArr['createtime']);
                unset($filterArr['createtime']);
            }
            $this->request->get(['filter' => json_encode($filterArr)]);
            
            
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $whereNew = $whereNew1 = $whereNew2 = [];
            if($project_name){
                $project1 = $this->model->where(['name'=>['like', "%{$project_name}%"]])->find();
                if($project1 && $project1['console_ids'])
                {
                    $whereNew['id'] = ['in', explode(",",$project1['console_ids'])];
                }else{
                    $whereNew['id'] = 0;
                }
            }
            if($create_time){
                $whereNew1['time'] = ['between', [strtotime($create_time[0]), strtotime($create_time[1])]];
                $whereNew2['createtime'] = ['between', [strtotime($create_time[0]), strtotime($create_time[1])]];
            }
            
            $new_sort = $sort;
            $sort = 'id';
            $limit = 500;

            $list = $model
                    ->where($where)
                    ->where($whereNew)
                    ->order($sort, $order)
                    ->paginate($limit);
                    

            foreach ($list as $row) {
                $file_count = 0;
                $row->visible(['id','name', 'question_num','file_num','memory_count','card_num','font_num', 'project_name', 'user_name', 'unit_name', 'size', 'video_num', 'image_num', 'vrhot_num']);
                $project = $this->model->where('FIND_IN_SET('.$row['id'].',console_ids)')->find();
                $projectName = $this->model->where('FIND_IN_SET('.$row['id'].',console_ids)')->column('name');
                
                $row['project_name'] = implode(',', $projectName) ?? '';
                $projectIds = $this->model->where('FIND_IN_SET('.$row['id'].',console_ids)')->column('unit_id');
                $unitName = \app\admin\model\ai\Unit::where(['id'=>['in', $projectIds]])->column('name');
                $row['unit_name'] = implode(',', $unitName) ?? '';
                $userInfo = [];
                if($project && $project['user_ids'])
                {
                    $userInfo = \app\admin\model\User::where(['id'=>['in', explode(',', $project['user_ids'])]])->limit(1)->column('username');
                }
                $row['user_name'] = $userInfo ? implode(',', $userInfo) : '';
                $cardCount = $totalCount = 0;
                if($project)
                {
                    $msgQuery = \app\admin\model\ai\Msg::where('robot', $project['id'])->where($whereNew1);
                    $totalCount = $msgQuery->count();
                    $cardCount = $msgQuery->where('robot', $project['id'])->where($whereNew1)->where('card', 1)->count();
                }

                $row['question_num'] = $totalCount;
                $row['card_num'] = $cardCount;
                $row['font_num'] = $row['question_num']-$row['card_num'];

                // 查询所有符合条件的记录并计算数量与总大小
                $fileQuery = \app\admin\model\ai\ConsoleFile::where('console_id', $row['id'])->where($whereNew2);
                $fileList = $fileQuery->field('file_size')->select();
                $file_count = count($fileList);
                $memory_count = array_sum(array_column($fileList, 'file_size'));

                $memory_count = $memory_count ?? 0;
                $master_upload = 0;
                /** 多模态数据 */
                $group_list1 = collection(\app\admin\model\ai\UserCard::where('console_id','in',$row['id'])->where($whereNew2)->field('type,count(1) num,sum(file_size) file_size,sum(duration) duration')->group('type')->select())->toArray();
                $card_list = $group_arr1 = [];
                foreach($group_list1 as $item){
                    $group_arr1[$item['type']??0] = $item;
                }
                $master_upload_image_num = $master_upload_video_num = 0;
                for($i=1; $i<4; $i++)
                {
                    $num = $duration_count_frist = $file_size_frist = 0;
                    if($i==1){
                        $master_upload_image_num = \app\admin\model\ai\Card::where(['console_id'=>$row['id'],'a'=>['not like',"%mp4%"]])->where($whereNew2)->count();
                        $file_size_frist = $master_upload_image_num*55600;
                        $memory_count += $file_size_frist;
                        $master_upload += $master_upload_image_num;
                        $num += $master_upload_image_num;
                    }else if($i==2){
                        $master_upload_video_num = \app\admin\model\ai\Card::where(['console_id'=>$row['id'],'a'=>['like',"%mp4%"]])->where($whereNew2)->count();
                        $file_size_frist = $master_upload_video_num*55600000;
                        $duration_count_frist = $master_upload_video_num*123;
                        $memory_count += $file_size_frist;
                        $master_upload += $master_upload_video_num;
                        $num += $master_upload_video_num;
                    }

                    $num += $group_arr1[$i]['num']??0;
                    $duration_count = $group_arr1[$i]['duration']??0;
                    $file_size_frist += $group_arr1[$i]['file_size']??0;
                    $memory_count += $group_arr1[$i]['file_size']??0;
                    $file_size = !empty($file_size_frist)?format_bytes($file_size_frist):'0B';
                    switch ($i) {
                        case 1:
                            $type = 'image';
                            break;
                        case 2:
                            $type = 'video';
                            break;
                        default:
                            $type = '未知';
                            break;
                    }
                    $file_count += $num;
                    if($type!='未知')
                    {
                        $card_list[] = [
                            'name' => $type,
                            'num' => $num,
                            'duration_count' => ($duration_count_frist+$duration_count),
                            'file_size' => $file_size
                        ];
                    }
                }
                /** 热点数据 */
                $vrhot_num = 0;
                if($project){
                    $vrhot_num = \app\admin\model\ai\Vrhot::where('robot', $project['id'])->where($whereNew2)->count();
                    if($vrhot_num)
                    {
                        $vrhot_num_size = $vrhot_num*55600000;
                        $memory_count += $vrhot_num_size;
                    }
                }
                $row['size'] = $memory_count;
                $row['memory_count'] = $memory_count ? format_bytes($memory_count) : '0K';
                $row['file_num'] = $file_count+$vrhot_num;

                $row['video_num'] = $master_upload_video_num + ($group_arr1[2]['num']??0);
                $row['image_num'] = $master_upload_image_num + ($group_arr1[1]['num']??0);
                $row['vrhot_num'] = $vrhot_num;
            }   
            $rows = collection($list->items())->toArray();
            if($new_sort != 'id'){
                if($new_sort=='memory_count'){
                    $new_sort = 'size';
                }
                usort($rows, function($a, $b) use ($new_sort, $order) {
                    if($order == 'asc')
                        return intval($a[$new_sort]) - intval($b[$new_sort]);
                    else
                        return intval($b[$new_sort]) - intval($a[$new_sort]);
                });
            }
            $result = array("total" => $list->total(), "rows" => $rows);

            return json($result);
        }
        return $this->view->fetch();
    }
}
