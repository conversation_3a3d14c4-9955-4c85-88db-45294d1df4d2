<?php

namespace app\common\service\ai\engine;

use app\common\service\ai\BaseEngine;
use think\Db;
use app\admin\model\ai\ConsoleFile;
use app\admin\model\ai\UrlData;

/** Rag模型 */
class Rag extends BaseEngine
{
	public $debug = false;
	protected $references = [];
	private $pgTable = 'public.vector_data_upgrades';
    private $msg = '';

    /**
	 * 构造引用内容
	 */
	public function buildReferenceContent($project, $msg)
	{
        $this->msg = $msg;
		$vectorId = 1;
		$vectorList = $this->queryVector($project, $msg);
		if($this->debug){
			dump($vectorList);exit;
		}
		$vectorScore = $project['vector_score']>0 ? $project['vector_score'] : 0.3;
		$list = [];
		foreach ($vectorList as &$item) {
			if ((isset($item['cosine_similarity']) && $item['cosine_similarity'] > $vectorScore) || (isset($item['relevance_score']) && $item['relevance_score'] > $vectorScore)) {
				$consoleFile = ConsoleFile::where('id', $item['file_id'])->find();
				if($consoleFile)
				{
					$file_number = $consoleFile['file_number'] ?? 0;
					if(!empty($consoleFile['type']) && $consoleFile['type'] == 5){
						$urlData = UrlData::where('file_number', $file_number)->find();
						$fileName = !empty($urlData['title']) ? $urlData['title'] : $consoleFile['name'];
						$filePath = !empty($urlData['url']) ? $urlData['url'] : cdnurl($consoleFile['file'], true);
					}else{
						$fileName = !empty($consoleFile['name']) ? $consoleFile['name'] : '';
						$filePath = !empty($consoleFile['file'])? cdnurl($consoleFile['file'], true) : '';
					}
					$item['title'] = $fileName;
					$item['file'] = $filePath;
					$item['file_id'] = $item['file_id'];
					$item['file_number'] = $file_number;
					$list[] = $item;
				}
			}
		}
		if($list)
		{
			$list = $this->reorderReferences($list);
			$vectorNum = $project['vector_num']>0 ? $project['vector_num'] : 6;
			$list = array_slice($list, 0, $vectorNum);
			foreach ($list as $item) {
				$slice = $item['content'];
				$this->references[] = [
					'title'   => $item['title'],
					'content' => $slice,
					'file'    => $item['file'],
					'cosine_similarity' => $item['cosine_similarity'] ?? 0,
					'relevance_score' => $item['relevance_score'] ?? 0,
					'document_id' => $item['file_number'],
					'file_id' => $item['file_id']
				];
				$vectorId++;
			}
		}
		return $this->references;
	}

    /** 查询向量库 */
	private function queryVector($project, $text)
	{
		$query = $this->pg_connect();
		$text = mb_strcut($text, 0, 384);
		if($this->debug){
			dump($text);
		}
		$vectorArr = $this->getEmbeddingsNew([$text]);
		$vectorContent = json_encode(['content'=>$vectorArr[0]['embedding']]);

        $vectorList = json_decode($vectorContent,true);
		$list = [];
        if(!empty($vectorList['content']))
        {
            $queryVector = json_encode($vectorList['content']);
			$query->query("SET hnsw.ef_search = 100;");
			$sql = "SELECT id, file_id, content, 1 - (embedding <=> '{$queryVector}') AS cosine_similarity FROM {$this->pgTable} where console_id  in ({$project['console_ids']}) ORDER BY embedding <=> '{$queryVector}' LIMIT 20";
			if($this->debug){
				dump($sql);
			}
            $list = $query->query($sql);
        }

		return $list;
    }

    /** 链接PGsql数据库 */
	private function pg_connect()
	{
		$request = Db::connect(
			[
				'type' => 'pgsql',
				'hostname' => '127.0.0.1',
				'database' => config('postgres.database'),
				'username' => 'postgres',
				'password' => 'DRrTmhCKrLWs2b34',
				'hostport' => '5432'
			]
		);
		return $request;
	}

    /** 
	 * 调用生成向量数组匹配对应关系
	 */
	function getEmbeddingsNew($arr) {
		$input = json_encode(['input' => $arr]);

		$embeddings = new \app\common\library\Embeddings;
		$embeddingsStr = $embeddings->run($input);
		$embeddingsArr = json_decode($embeddingsStr, true);

		// 检查 data 键是否存在
		if (!isset($embeddingsArr['data'])) {
			usleep(500000); // 停顿 0.5 秒
			$embeddingsStr = $embeddings->run($input); // 重试
			$embeddingsArr = json_decode($embeddingsStr, true);
		}

		return $embeddingsArr['data'] ?? []; // 如果 data 不存在，返回空数组或其他默认值
	}

    /**
	 * 重排引用内容
	 */
	private function reorderReferences($references, $msg = '')
	{
		$batchSize = 40;
		$path = '/mnt/sdc/wwwroot/ai-master/python/qianwen/rerank.py';
		$msgEscaped = str_replace(['"', "'"], ['“', "’"], $msg?$msg:$this->msg);

		$sortedReferences = []; // 存储排序结果（引用键 => relevance_score）
		$unusedKeys = [];       // 未参与排序的 keys（没有出现在 rerank 结果中）

		$batches = array_chunk($references, $batchSize, true);
		foreach ($batches as $batch) {
			$vectorArr = [];
			$keyMap = [];

			foreach ($batch as $originalKey => $v) {
				$content = mb_substr((isset($v['title'])?$v['title'].":":'').$v['content'], 0, 2000);
				$escapedContent = str_replace(['"', "'", "(", ")"], ['“', "’", "（", "）"], $content);
				$vectorArr[] = $escapedContent;
				$keyMap[] = $originalKey; // 映射 index => 原始 key
			}
			if($this->debug){
				dump($keyMap);
			}

			$vectorStr = implode('@@', $vectorArr);
			$cmd = $this->pythonPath . ' ' . $path . ' "%s" "%s" ';
			$return = [];
			exec(sprintf($cmd, $msgEscaped, $vectorStr), $return, $returnCode);
			$usedKeys = [];

			if (!empty($return[0])) {
				$arr = json_decode($return[0], true);
				if (!empty($arr['output']['results'])) {
					foreach ($arr['output']['results'] as $result) {
						$indexInBatch = $result['index'];
						if(isset($keyMap[$indexInBatch]))
						{
							$key = $keyMap[$indexInBatch];
							$sortedReferences[] = [
								'key' => $key,
								'relevance_score' => $result['relevance_score']
							];
							$usedKeys[] = $key;
						}
					}
				}
			}

			// 剩下没被返回的 key，加入 unused 列表
			$unusedKeys = array_merge($unusedKeys, array_diff($keyMap, $usedKeys));
		}

		// 根据排序结果重建 references 顺序
		$final = [];

		if($this->debug){
			dump($sortedReferences);
		}
		foreach ($sortedReferences as $item) {
			$key = $item['key'];
			$references[$key]['relevance_score'] = $item['relevance_score'];
			$final[] = $references[$key];
		}

		// 追加未排序的
		foreach ($unusedKeys as $key) {
			$references[$key]['relevance_score'] = 0;
			$final[] = $references[$key];
		}
		// 按 relevance_score 倒序排序
		usort($final, function ($a, $b) {
			return ($b['relevance_score'] ?? 0) <=> ($a['relevance_score'] ?? 0);
		});

		return $final;
	}
}