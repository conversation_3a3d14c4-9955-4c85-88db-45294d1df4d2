<?php
namespace app\api\controller\ai;

use app\admin\model\ai\ConsoleFile;
use app\common\controller\Api;
use think\Db;
use PHPExcel;
use PHPExcel_IOFactory;
use app\common\service\IpService;
use app\common\service\TagExtraction;
use app\common\service\Convert;
use app\common\service\ai\AiService;


class Index extends Api
{
	// 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];
    
	protected $lang = 'cn';
	
	// 实现调用AppBuilder接口并进行流式输出
    public function callStreamApi()
    {
        header("Access-Control-Allow-Origin: *");
		header('Content-Type: text/event-stream');
      	header('Cache-Control: no-cache');
      	header('Connection: keep-alive');
      	header('X-Accel-Buffering: no');
        
		$postData = file_get_contents("php://input");
        $postDataArr = json_decode($postData);
        $robot = $postDataArr->robot;
        $this->lang = isset($postDataArr->lang) ? $postDataArr->lang : '';
		$chatId = $postDataArr->chatId;
		$card = $postDataArr->card;
        $question = isset($postDataArr->prompt) ? $postDataArr->prompt : "";
        $project = db::name('ai_project')->where('id',$robot)->field('id,type,console_key')->find();
        $console_key = $project['console_key'];

        if(empty($console_key)){
            $console_key = "8e36c365-f419-488c-8002-9789d4dcbaee";
        }


        $aiEngine = AiService::getEngine($project['type']);
        $aiEngine->chat($console_key,$chatId,$question,$robot,$card,$this->lang);
        exit;
    }
    

    public function getAssistant()
    {

        $robot_id = input('robot_id');

        if (empty($robot_id)) {
            $this->error('参数错误');
        }

        $row = Db::name('ai_project')->where('id',$robot_id)->field('id,assistant,voice')->find();

        if (empty($row)) {
            $this->error('记录不存在');
        }

        $assistant = Db::name('ai_assistant')->where('id',$row['assistant'])->find();
        if (!empty($assistant)) {
            $assistant['image'] = cdnurl($assistant['image'],true);
        }

        $voice = Db::name('ai_voice')->where('id',$row['voice'])->find();
        if (!empty($voice)) {
            $voice['voice_path'] = cdnurl($voice['voice_path'],true);
        }

        $this->success('success',['assistant' => $assistant,'voice' => $voice]);
    }


    
}