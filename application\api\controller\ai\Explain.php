<?php
namespace app\api\controller\ai;

use app\common\controller\Api;
use think\Db;
use app\common\service\IpService;
use app\common\service\ai\AiService;
use app\common\service\Convert;
use app\common\service\TagExtraction;



class Explain extends Api
{
	// 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];

	/** 读取对话ID */
	function get_chatid(){
		$return = [];
		$robot = input('robot');
        $console_key = db::name('ai_project')->where('id',$robot)->value('console_key');
        if(empty($console_key)){
            $console_key = "8e36c365-f419-488c-8002-9789d4dcbaee";
        }
		$chatId = exec('/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/app.py "'.$console_key.'" 1');

		$return['chatId'] = $chatId;
		$this->success('success',$return);
	}

	/** 读取解析内容 */
	// function get_explain(){
	// 	$return = [];
	// 	$robot = input('robot');
	// 	$chatId = input('chatId');
	// 	$question = base64_decode(input('question'));
    //     $console_key = db::name('ai_project')->where('id',$robot)->value('console_key');
    //     if(empty($console_key)){
    //         $console_key = "8e36c365-f419-488c-8002-9789d4dcbaee";
    //     }
	// 	$explain = exec('/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/explain.py "'.$console_key.'" 2 "'.$chatId.'" "'.$question.'" ');
	// 	$return['content'] = $explain;
	// 	$this->success('success',$return);
	// }


	public function get_explain()
	{
		$robot = input('robot');
		$chatId = input('chatId');
		$question = input('question');

		$project = db::name('ai_project')->where('id',$robot)->field('id,type,console_key')->find();
        $console_key = $project['console_key'];
        
        // dump($project);
        if(empty($console_key)){
            $console_key = "8e36c365-f419-488c-8002-9789d4dcbaee";
        }

        // dump($question);
        $aiEngine = AiService::getEngine($project['type']);
        return $aiEngine->chatAndReturn($console_key,$chatId,$question,$robot);

	}


}