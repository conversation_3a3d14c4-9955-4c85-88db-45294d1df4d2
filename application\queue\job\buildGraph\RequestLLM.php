<?php

namespace app\queue\job\buildGraph;

use app\common\service\ai\AiService;
use app\common\service\DocParsingService;
use app\common\util\KnowledgeGraphUtil;


use think\Queue\Job;
use think\Queue;


class RequestLLM
{


    /**
     *
     * 向大模型请求获取图谱数据
     * 
     **/
    public function fire(Job $job, $data)
    {

        set_time_limit(0);
        //重试超过3次
        if ($job->attempts() > 3) {
            //通过这个方法可以检查这个任务已经重试了几次了
            $job->delete();
        }
        $content = $data['content'];
        $console_file_id = $data['console_file_id'];
        
        //第二步调用大模型生成目录和图谱数据(较耗时)
        $aiEngine = AiService::getEngine('doubao');
        $res = $aiEngine->buildGraphChat($content);
        $res = json_decode($res,true);

        if (!isset($res['choices']) && !isset($res['choices'][0]['message'])) {
            echo '======请求出错======' . "\n";
            echo '执行结果：' . json_encode($res,JSON_UNESCAPED_UNICODE) . "\n";
            echo date('Y-m-d H:i:s') . "\n";

            // return;
        } else {
            $message = $res['choices'][0]['message']['content'];
            echo '======请求大模型成功======'."\n";

            Queue::push('app\queue\job\buildGraph\SaveToDatabase', [
                'message' => $message,
                'content' => $content,
                'console_file_id' => $console_file_id,
            ]);

        }

        $job->delete();

    }


    public function failed($data){
        // ...任务达到最大重试次数后，失败了
    }
}