<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use app\common\service\ai\AiService;



/**
 * API-知识图谱
 */
class KnowledgeGraph extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     * FileSlice模型对象
     * @var \app\admin\model\ai\FileSlice
     */
    protected $model = null;
    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\FileSlice;
    }
    

    /**
     * 列表
     */
    public function file_graph()
    {
        $id = input('id');
        if($id)
        {

            $console_file = Db::name('ai_console_file')->where('id',$id)->field('id,name')->find();
            $list = Db::name('ai_file_slice_catalog')
                ->where('file_id',$id)
                ->where('level','<=', 3)
                ->field('id,name,pid,level')
                ->select();

            if (count($list) < 2) {
                $content = \app\admin\model\ai\Knowledgegraph::where('file_id', $id)->value('content');
                $return = $this->parseMermaid($content);
            } else {
                $nodes = [
                    ['id' => '0','name' => $console_file['name'],'category' => 1]
                ];
                $edges = [];

                foreach ($list as $v) {
                    $nodes[] = [
                        'id' => (string)$v['id'],
                        'name' => $v['name'],
                        'category' => $v['level'] + 1
                    ];

                    if ($v['pid'] == 0) {
                        $edges[] = [
                            'source' => "0",
                            'target' => (string)$v['id'],
                            'value' => '包含'
                        ];
                    }

                    foreach ($list as $v2) {
                        if ($v2['pid'] == $v['id']) {
                            $edges[] = [
                                'source' => (string)$v['id'],
                                'target' => (string)$v2['id'],
                                'value' => '包含'
                            ];
                        }
                    }
                }

                $return = [
                    'nodes' => $nodes,
                    'links' => $edges
                ];
            }

            

            // $content = \app\admin\model\ai\Knowledgegraph::where('file_id', $id)->value('content');
            // if(strpos($content, 'graph')!==false){
                // $return = $this->parseMermaid($content);
            // }else{
                // $return = [
                //     "type" => 1,
                //     "content" => $content,
                // ];
  
            // }

        }else{
            $content = "mindmap\n";
            $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->find();
            $console_ids = $project['console_ids'];
            $console_list = \app\admin\model\ai\Console::where(['id'=>['in', explode(',', $console_ids)]])->select();
            $content .= "  root((".$project['name']."))\n";
            foreach($console_list as $val){
                $content .= "    [".$val['name']."]\n";
                $consolefile_list = \app\admin\model\ai\ConsoleFile::where(['console_id'=>$val['id']])->select();
                foreach($consolefile_list as $cval){
                    $content .= "      (".$cval['name'].")\n";
                }
            }
            $return = [
                "type" => 1,
                "content" => $content,
            ];
            // $card_list = \app\admin\model\ai\Card::where(['robot'=>$project['id'],'a'=>['like',"%mp4%"]])->limit(10)->select();
            // $content .= "    [视频]\n";
            // foreach($card_list as $cval){
            //     $content .= "      (".$cval['q'].")\n";
            // }
            // $card_list = \app\admin\model\ai\Card::where(['robot'=>$project['id'],'a'=>['like',"%jpg%"]])->limit(10)->select();
            // $content .= "    [图片]\n";
            // foreach($card_list as $cval){
            //     $content .= "      (".$cval['q'].")\n";
            // }
        }
        $this->success('', $return);
    }

    // 解析Mermaid语法
    function parseMermaid( $mermaidData ) {
        // dump($mermaidData);
        // 正则表达式匹配节点和链接
        $nodePattern = '/(\w+)\[([^\]]+)\]/';
        $linkPattern = '/(\w+)(\[.*\])?\s+-->\|([^|]+)\|\s+(\w+)/';

        // 初始化节点和边数组，以及字母到数字的映射
        $nodes = [];
        $links = [];
        $categories = [
            [
                'name'=> '一级分类',
            ],
            [
                'name'=> '二级分类',
            ],
            [
                'name'=> '三级分类',
            ],
            [
                'name'=> '四级分类',
            ],
            [
                'name'=> '五级分类',
            ],
        ];
        $idMapping = [];
        $currentId = 0;

        // 查找所有节点
        preg_match_all($nodePattern, $mermaidData, $nodeMatches, PREG_SET_ORDER);
        // 添加节点
        foreach ($nodeMatches as $match) {
            $alphabeticId = $match[1];
            $name = $match[2];
            // 检查是否已经为这个字母分配了数字ID
            if (!isset($idMapping[$alphabeticId])) {
                $idMapping[$alphabeticId] = $currentId++;
            }
            if( $alphabeticId == "A" ){
                $symbolSize = 80;
                $category = 0;
            }else if( strlen($alphabeticId) == 1 ){
                $symbolSize = 50;
                $category = 1;
            }else if( strlen($alphabeticId) == 2 ){
                $symbolSize = 30;
                $category = 2;
            }else if( strlen($alphabeticId) == 3 ){
                $symbolSize = 20;
                $category = 3;
            }else if( strlen($alphabeticId) == 4 ){
                $symbolSize = 10;
                $category = 4;
            } else {
                $category = '';
                $symbolSize = '';
            }
            $nodes[] = [
                'id' => $alphabeticId,
                'name' => $name,
                'category' => $category,
                'symbolSize' => $symbolSize
            ];
        }

        // 查找所有链接
        preg_match_all($linkPattern, $mermaidData, $linkMatches, PREG_SET_ORDER);
        // 添加链接
        foreach ($linkMatches as $match) {
            $sourceAlphabetic = $match[1];
            $relationship = trim($match[3]);
            $targetAlphabetic = $match[4];

            // 确保链接的源和目标节点已被分配了数字ID
            if (!isset($idMapping[$sourceAlphabetic])) {
                $idMapping[$sourceAlphabetic] = $currentId++;
            }
            if (!isset($idMapping[$targetAlphabetic])) {
                $idMapping[$targetAlphabetic] = $currentId++;
            }
            // dump($idMapping);exit;
            if (!isset($nodes[$idMapping[$sourceAlphabetic]]) || !isset($nodes[$idMapping[$targetAlphabetic]])) {
                continue;
            }
            $links[] = [
                "source" => $nodes[$idMapping[$sourceAlphabetic]]['id'],
                "target" => $nodes[$idMapping[$targetAlphabetic]]['id'],
                "value" => $relationship,
            ];
        }
        foreach($nodes as &$item){
            $nodeCount = 0;
            foreach($links as $k=>$v){
                if($item['id'] == $v['source']){
                    $nodeCount++;
                }
            }
            $item['nodeCount'] = $nodeCount?$nodeCount:'';
        }
        // 打印ECharts格式的JSON
        $graph = [
            'nodes' => $nodes,
            'links' => $links,
            'categories' => $categories,
            "type" => 2,
        ];

        return $graph;
    }


    /**
     * 文件图谱
     */
    public function index(){
        $id = input('id');
        if($id)
        {
            $content = \app\admin\model\ai\Knowledgegraph::where('file_id', $id)->value('content');
        }else{
            $content = "mindmap\n";
            $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->find();
            $console_ids = $project['console_ids'];
            $console_list = \app\admin\model\ai\Console::where(['id'=>['in', explode(',', $console_ids)]])->select();
            $content .= "  root((".$project['name']."))\n";
            foreach($console_list as $val){
                $content .= "    [".$val['name']."]\n";
                $consolefile_list = \app\admin\model\ai\ConsoleFile::where(['console_id'=>$val['id']])->select();
                foreach($consolefile_list as $cval){
                    $content .= "      (".$cval['name'].")\n";
                }
            }
            // $card_list = \app\admin\model\ai\Card::where(['robot'=>$project['id'],'a'=>['like',"%mp4%"]])->limit(10)->select();
            // $content .= "    [视频]\n";
            // foreach($card_list as $cval){
            //     $content .= "      (".$cval['q'].")\n";
            // }
            // $card_list = \app\admin\model\ai\Card::where(['robot'=>$project['id'],'a'=>['like',"%jpg%"]])->limit(10)->select();
            // $content .= "    [图片]\n";
            // foreach($card_list as $cval){
            //     $content .= "      (".$cval['q'].")\n";
            // }
        }
        $this->success('', ['content' => $content]);
    }

    /**
     * 自动任务运行生成知识图谱
     */
    public function task(){
        set_time_limit(5*60);
        ignore_user_abort(true);
        $fileList = \app\admin\model\ai\ConsoleFile::alias('a')
        ->field('a.console_id,a.id,a.file,b.content,b.retry')
        ->join('ai_knowledge_graph b','a.id=b.file_id','left')
        ->whereRaw('`file` IS NOT NULL AND `file` NOT LIKE "%.xls%"')
        // ->where('content','')
        // ->where(['b.retry'=>['<',2]])
        ->order('a.id asc')
        ->select();

        dump(\app\admin\model\ai\ConsoleFile::getLastSql());
        // dump($fileList);exit;
        $count = 0;
        foreach($fileList as $val){
            if($val['retry']>=2 || $count>9 || !empty($val['content']) || strpos($val['file'], 'xls')!==false)continue;
            $content = $this->create($val['file']);
            $row = \app\admin\model\ai\Knowledgegraph::where(['file_id'=>$val['id']])->find();
            if($row)
            {
                \app\admin\model\ai\Knowledgegraph::where(['id'=>$row['id']])->update(['updatetime'=>time(),'content'=>$content,'retry'=>$row['retry']+1]);
            }else{
                \app\admin\model\ai\Knowledgegraph::insert(['console_id'=> $val['console_id'],'file_id'=>$val['id'],'content'=>$content,'createtime'=>time(),'updatetime'=>time(),'retry'=>1]);
            }
            error_log(date("Y-m-d H:i:s")."id:".$val['id']."|sql:".\app\admin\model\ai\Knowledgegraph::getLastSql()."\r\n",3,ROOT_PATH."/runtime/log/".date("Ym")."/taskInfo.log");
            $count++;
            // dump(\app\admin\model\ai\Knowledgegraph::getLastSql());exit;
            // dump($content);
            // exit;
        }
        $this->success($count.'条数据生成成功');
    }
    /**
     * 请求Moonshot生成知识图谱
     */
    function create($file){
        $path_file = ROOT_PATH."public".$file;
        $python = '/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/python/knowledge_graph_moonshot.py "%s"';
        // dump(sprintf($python, $path_file));exit;
        $return = exec(sprintf($python, $path_file));
        // dump($return);
        // dump($return);
        preg_match('/```(.*)```/isU', $return, $match);
        if(!empty($match[1])){
            $result = str_replace(["mermaid\\n","\\n"],["","\n"], $match[1]);
            
            // dump($result);exit;
            return $result;
        }
        // trace(date("Y-m-d H:i:s")."|fileName:".$path_file."|return：".print_r($return,1), 'debug');
        error_log(date("Y-m-d H:i:s")."|python:".sprintf($python, $path_file)."|return：".print_r($return,1)."\r\n",3,ROOT_PATH."/runtime/log/".date("Ym")."/taskInfo.log");
        return '';
    }



    /*
    * 获取单个知识库的知识图谱
    */
    public function console_graph()
    {

        $console_id = input('id');
        $console = Db::name('ai_console')->where('id',$console_id)->field('id,name')->find();
        if (empty($console)) {
            $this->error('知识库不存在');
        }
      
        $nodes = [
            ['id' => '0','name' => $console['name'],'category' => 0]
        ];
        $edges = [];

        $catalog_cate = Db::name('ai_file_slice_catalog_cate')
            ->where('console_id',$console_id)
            ->where('pid',0)
            ->field('id,name')
            ->select();
    
        foreach ($catalog_cate as $v) {

            $nodes[] = ['id' => $v['id'],'name' => $v['name'],'category' => 1];
            $edges[] = [
                'source' => '0',
                'target' => (string)$v['id'],
                'value' => '包含'
            ];

            $child = Db::name('ai_file_slice_catalog_cate')
            ->where('pid',$v['id'])
            ->field('id,name')
            ->select();

            foreach ($child as $c) {
                $nodes[] = ['id' => $c['id'],'name' => $c['name'],'category' => 2];
                $edges[] = [
                    'source' => (string)$v['id'],
                    'target' => (string)$c['id'],
                    'value' => '包含'
                ];

                $child2 = Db::name('ai_file_slice_catalog_cate')
                ->where('pid',$c['id'])
                ->field('id,name')
                ->select();

                foreach ($child2 as $c2) {
                    $nodes[] = ['id' => $c2['id'],'name' => $c2['name'],'category' => 3];
                    $edges[] = [
                        'source' => (string)$c['id'],
                        'target' => (string)$c2['id'],
                        'value' => '包含'
                    ];
                }

            }

        }    

        $return = [
            'nodes' => $nodes,
            'links' => $edges
        ]; 

        $this->success('success',$return);
        
    }


    public function buildTotalGraphCrontab()
    {
        $console_id =  Db::name('ai_console')
        ->where('build_graph_status',0)
        ->where('deletetime',null)
        ->value('id');
        
        // ->limit(10) 
        // ->column('id');
        // foreach ($consoles as $v) {
            $this->buildWholeConsoleGraph($console_id);
        // }
        $this->success('success',$console_id);
    }
    /**
     * 建立整个知识库的知识图谱 
     **/
    public function buildWholeConsoleGraph($console_id)
    {
        // $console_id = input('id');
        // $console_id =  Db::name('ai_console')
        // ->where('build_graph_status',0)
        // ->where('deletetime',null)
        // ->value('id');

        $files = Db::name('ai_console_file')
        ->where('console_id',$console_id)
        // ->where('build_graph_status',0)
        ->where('deletetime',null)
        // ->limit(100)
        ->field('id,name')
        ->select();

        $names = array_column($files,'name');
        $names = implode(',',$names);

        $engine = AiService::getEngine('doubao');

        $res = $engine->buildFileCate($names);
        $res = json_decode($res,true);

        if (empty($res)) {
            echo '请求错误';
        }

        $content = $res['choices'][0]['message']['content'];
        $arr = json_decode($content,true);
        try {
            Db::startTrans();

            Db::name('ai_file_slice_catalog_cate')->where('console_id',$console_id)->delete();

            $result = [];
            $this->getCateRecursive($arr,$console_id);
            //修改知识库图谱生成为完成状态
            Db::name('ai_console')->where('id',$console_id)->update(['build_graph_status' => 1]);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }

    }

    public function getCateRecursive($list, $console_id) 
    {

        $pidHash = [];
        foreach ($list['categories'] as $k=>$v) {

            if ($v['pid'] == 0) {
                $insert = [
                    'name' => $v['title'],
                    'console_id' => $console_id,
                    'pid' => 0,
                    'createtime' => time()
                ];   
                $id = Db::name('ai_file_slice_catalog_cate')->insertGetId($insert);
                $pidHash[$v['id']] = $id;
            } else {
                $insert = [
                    'name' => $v['title'],
                    'console_id' => $console_id,
                    'pid' => $pidHash[$v['pid']],
                    'createtime' => time()
                ];   
                $id = Db::name('ai_file_slice_catalog_cate')->insertGetId($insert);
                $pidHash[$v['id']] = $id;
            }
            
        }
    }


    // public function getCateRecursive($list,$pid,&$result,$console_id)
    // {

    //     if (!is_array($list) || !is_object($list)) {
    //         return;
    //     }
    //     foreach ($list as $k => $v) {
    //         if (is_object($v)) {
    //             $insert = [
    //                 'name' => $k,
    //                 'console_id' => $console_id,
    //                 // 'level' => $level,
    //                 'pid' => $pid,
    //                 'createtime' => time()
    //             ];   
    //             $id = Db::name('ai_file_slice_catalog_cate')->insertGetId($insert);
    //             $result[] = [
    //                 'name' => $k,
    //                 'pid' => $pid,
    //                 'id' => $id,
    //             ];
    //             $this->getCateRecursive($v,$id,$result,$console_id);   
    //         } else if (is_array($v)) {
    //             $insert = [
    //                 'name' => $k,
    //                 // 'level' => $level,
    //                 'console_id' => $console_id,
    //                 'pid' => $pid,
    //                 'createtime' => time()
    //             ];
    //             $id = Db::name('ai_file_slice_catalog_cate')->insertGetId($insert);
    //             foreach ($v as $value) {
    //                 $insert = [
    //                     'name' => $value,
    //                     // 'level' => $level,
    //                     'console_id' => $console_id,
    //                     'pid' => $id,
    //                     'createtime' => time()
    //                 ];
    //                 Db::name('ai_file_slice_catalog_cate')->insertGetId($insert);
    //                 $result[] = [
    //                     'name' => $value,
    //                     'pid' => $pid,
    //                     'id' => $id,
    //                 ];
    //             }
                
    //         }
    //     }

    //     return $result;

    // }

    // public function buildWholeConsoleGraph()
    // {

    //     $console_list = \app\admin\model\ai\Console::select();

    //     $char = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'];
    //     $charArr = $char;

    //     for($i=0;$i<count($char);$i++){
    //         for($j=0;$j<count($char);$j++){
    //             $charArr[] = $char[$i].$char[$j];
    //         }
    //     }

    //     foreach($console_list as $v) {
    //         $files = \app\admin\model\ai\ConsoleFile::where('console_id',$v['id'])->select();
    //         // $word_count = array_sum(array_column($files,'word_count'));
    //         // $file_names = array_column($files,'name');
    //         // $file_paths = array_column($files,'file');
    //         $str = '';
    //         $result = [
    //             'name' => $v['name'],
    //             'son' => []
    //         ];
    //         foreach ($files as $f) {
    //             $graph = \app\admin\model\ai\Knowledgegraph::where('file_id', $f['id'])->value('content');
    //             if (empty($graph)) {
    //                 continue;
    //             }
    //             $json = $this->parseMermaid($graph);
    //             if (!$json) {
    //                 continue;
    //             }
    //             $tmp = [
    //                 'name' => '',
    //                 'son' => []
    //             ];
    //             foreach ($json['nodes'] as $n) {
    //                 $t = [
    //                     'name' => '',
    //                     'relation' => ''
    //                 ];
    //                 if ($n['category'] == 0) {
    //                     $tmp['name'] = $n['name'];
    //                 } else if ($n['category'] == 1) {
    //                     $t['name'] = $n['name'];
    //                     foreach ($json['links'] as $l) {
    //                         if ($l['target'] == $n['id']) {
    //                             $t['relation'] = $l['value'];
    //                         }
    //                     }
    //                     $tmp['son'][] = $t;
    //                 }
    //             }   

    //             $result['son'][] = $tmp;
    //         }

    //         $nodes = [];
    //         $links = [];
    //         $nowIndex = 0;
    //         $pid = $charArr[$nowIndex];
    //         $nodes[] = [
    //             'category' => 0,
    //             'id' => $pid,
    //             'name' => $v['name'],
    //             'nodeCount' => 0,
    //         ];

    //         foreach ($result['son'] as $r) {
    //             $nowIndex ++;
    //             $secondPid = $charArr[$nowIndex];
    //             $nodes[] = [
    //                 'category' => 1,
    //                 'id' => $secondPid,
    //                 'name' => $r['name'],
    //                 'nodeCount' => 0,
    //             ];
    //             $links[] = [
    //                 'source' => $pid,
    //                 'target' => $secondPid,
    //                 'value' => '包含'
    //             ];
    //             foreach ($r['son'] as $k=>$s) {

    //                 $nodes[] = [
    //                     'category' => 2,
    //                     'id' => $secondPid . ($k+1),
    //                     'name' => $s['name'],
    //                     'nodeCount' => 0,
    //                 ];
    //                 $links[] = [
    //                     'source' => $secondPid,
    //                     'target' => $secondPid . ($k+1),
    //                     'value' => $s['relation']
    //                 ];
    //             }
 
    //         }

    //         foreach($nodes as &$item){
    //             $nodeCount = 0;
    //             foreach($links as $lk=>$lv){
    //                 if($item['id'] == $lv['source']){
    //                     $nodeCount++;
    //                 }
    //             }
    //             $item['nodeCount'] = $nodeCount?$nodeCount:'';
    //         }

    //         $save = [
    //             'console_id' => $v['id'],
    //             'graph_data' => json_encode(['nodes' => $nodes,'links' => $links],JSON_UNESCAPED_UNICODE)
    //         ];
    //         $row = Db::name('ai_console_knowledge_graph')->where('console_id',$v['id'])->find();
    //         if ($row) {
    //             Db::name('ai_console_knowledge_graph')->where('console_id',$v['id'])->update($save);
    //         } else {
    //             Db::name('ai_console_knowledge_graph')->insert($save);
    //         }


    //     }


    // }



    // 废弃，节点太多，页面崩溃
    // public function console_graph()
    // {
        // $console_id = input('id');
        // $console = Db::name('ai_console')->where('id',$console_id)->field('id,name')->find();
        // if (empty($console)) {
        //     $this->error('知识库不存在');
        // }

        // $cache = cache('consoleGraph' . $console_id);
        // if ($cache) {
        //     $this->success('success',unserialize($cache));
        // }

        // $nodes = [
        //     ['id' => '0','name' => $console['name'],'category' => 0]
        // ];
        // $edges = [];
        // $files = Db::name('ai_console_file')
        // ->where('console_id',$console_id)
        // ->where('deletetime',null)
        // ->field('id,name')
        // ->select();

        // foreach ($files as $f) {
        //     $nodes[] = ['id' => '0','name' => $f['name'],'category' => 1];
            
        //     $edges[] = [
        //         'source' => "0",
        //         'target' => (string)$f['id'],
        //         'value' => '包含'
        //     ];

        //     $list = Db::name('ai_file_slice_catalog')
        //             ->where('file_id',$f['id'])
        //             ->where('level','<=', 3)
        //             ->field('id,name,pid,level')
        //             ->limit(50)
        //             ->select();
        //     foreach ($list as $v) {
        //         $nodes[] = [
        //             'id' => (string)$v['id'],
        //             'name' => $v['name'],
        //             'category' => $v['level'] + 2
        //         ];

        //         if ($v['pid'] == 0) {
        //             $edges[] = [
        //                 'source' => (string)$f['id'],
        //                 'target' => (string)$v['id'],
        //                 'value' => '包含'
        //             ];
        //         }
        //         foreach ($list as $v2) {
        //             if ($v2['pid'] == $v['id']) {
        //                 $edges[] = [
        //                     'source' => (string)$v['id'],
        //                     'target' => (string)$v2['id'],
        //                     'value' => '包含'
        //                 ];
        //             }
        //         }
        //     }
                
        // }

        // $return = [
        //     'nodes' => $nodes,
        //     'links' => $edges
        // ];

        // cache('consoleGraph' . $console_id,serialize($return),3600);
        // $this->success('success',$return);


    // }
    // 废弃
    // public function console_graph()
    // {
    //     $console_id = input('id');
    //     $console_row = [];
    //     $mergedGraph = ['nodes' => [], 'links' => []];
    //     if($console_id == 9999){
    //         $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->find();
    //         $console_row = explode(',', $project['console_ids']);
    //         $project_name = $project['name'];
    //         // 添加项目名称作为根节点
    //         $mergedGraph['nodes'][] = ["category" => 0, "id" => "project_root", "name" => $project_name, "nodeCount" => count($console_row)];

    //         $graph_list = Db::name('ai_console_knowledge_graph')->where(['console_id'=>['in', $console_row]])->field('console_id,graph_data')->select();
    //         foreach ($graph_list as $k => $v) {
    //             $graphData = json_decode($v['graph_data'], true);
    
    //             // 添加项目根节点到每个子图的链接
    //             $mergedGraph['links'][] = ["source" => "project_root", "target" => $v['console_id'] . $graphData['nodes'][0]['id'], "value" => "包含"];

    //             // 合并节点，确保唯一性
    //             foreach ($graphData['nodes'] as $node) {
    //                 $node['id'] = $v['console_id'].$node['id'];
    //                 $mergedGraph['nodes'][] = $node;
    //             }
    
    //             // 合并链接，确保唯一性
    //             foreach ($graphData['links'] as $link) {
    //                 $link['source'] = $v['console_id'].$link['source'];
    //                 $link['target'] = $v['console_id'].$link['target'];
    //                 $mergedGraph['links'][] = $link;
    //             }
    
    //         }
    //         // 将合并后的图谱数据转换为 JSON 格式
    //         $mergedGraphJson = json_encode($mergedGraph, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        
    //         $this->success('success', json_decode($mergedGraphJson, true));
    //     }else{
    //         $console_row[] = $console_id;
    //         $graph_data = Db::name('ai_console_knowledge_graph')->where('console_id', $console_id)->value('graph_data');
    //         $graph_data = json_decode($graph_data,true);
    //         $this->success('success',$graph_data);
    //     }
    // }



    /*
    * 获取知识图谱切片内容
    */
    public function get_graph_data()
    {
        $id = input('id');
        $fileId = input('fileId');

        // $row = Db::name('ai_file_slice_catalog')->where('id',$id)->find();
        $content = '';
        if ($id == 0) {
            if (empty($fileId)) {
                $this->error('参数错误');
            }

            $file_name = Db::name('ai_console_file')->where('id',$fileId)->value('name');
            $content = "<h1>$file_name</h1>";

            $topPids = Db::name('ai_file_slice_catalog')->where('file_id',$fileId)->where('pid',0)->column('id');

            foreach ($topPids as $v) {
                $this->recursiveGetContent($v,$content);
            }

        } else {
            $this->recursiveGetContent($id,$content);
        }
        $content = str_replace("\n",'<br>',$content);
        $this->success('success',$content);
    }


    public function recursiveGetContent($catalog_id,&$content)
    {
        $row = Db::name('ai_file_slice_catalog')
        ->where('id',$catalog_id)
        ->field('id,name,level')
        ->find();

        $content .= "<h{$row['level']}>".$row['name'] . "</h{$row['level']}>";
        $file_slice_row = Db::name('ai_file_slice')->where('catalog_id',$catalog_id)->column('content');
        $content .= implode("\n",$file_slice_row);

        $children = Db::name('ai_file_slice_catalog')
        ->where('pid',$catalog_id)
        ->field('id,name')
        ->select();

        foreach ($children as &$v) {
            $v['child'] = $this->recursiveGetContent($v['id'],$content);
        }

        return $children;

    }






}
