<?php

namespace app\api\controller\ai;

use app\common\controller\Api;
use think\Db;

/**
 * 文件接口
 */
class File extends Api
{
    protected $noNeedRight = '*';

    /**
     * 下载记录
     */
    public function download_log()
    {
       $robot = input('robot');
       $uid = input('uid');
       $file_id = input('file_id');
       $insert = [
            'robot' => $robot,
           'uid' => $uid,
           'file_id' => $file_id,
           'createtime' => time(),
           'updatetime' => time()
       ];
       $return = Db::name('ai_filedownload_log')->insert($insert);
       if($return){
           $this->success("记录成功!");
       }else{
           $this->error("记录失败!");
       }
    }
}
