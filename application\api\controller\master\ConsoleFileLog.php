<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Cache;

/**
 * API-操作日志
 */
class ConsoleFileLog extends Api
{
    protected $noNeedLogin = ['downloadFile'];
    protected $noNeedRight = ['*'];


    /**
     * ConsoleFileLog模型对象
     * @var \app\admin\model\ai\ConsoleFileLog
     */
    protected $model = null;
    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\ConsoleFileLog;
    }
    

    /**
     * 日志查看
     */
    public function index()
    {
        $limit = $this->request->post('limit') ?? 10;
        $page = $this->request->post('page') ?? 1;
        $this->request->get([config('paginate.var_page') => $page]);
        $sort = 'id';
        $order = 'DESC';
        $where = [];
        $user_ids = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('user_ids');
        $where['user_id'] = ['in', explode(',', $user_ids)];
        if($this->request->post('username')){
            $where['user.username'] = ['like',"%{$this->request->post('username')}%"];
        }
        
        if($this->request->post('keywords')){
            $where['filename'] = ['like',"%{$this->request->post('keywords')}%"];
        }

        $start_date = $this->request->post('start_date') ?? date("Y-m-d", strtotime('-3month'));
        $end_date = $this->request->post('end_date') ?? date("Y-m-d", strtotime('+1 day'));
        if($this->request->post('start_date') && $this->request->post('end_date')){
            $where['console_file_log.createtime'] = ['between',[strtotime($start_date),strtotime($end_date)]];
        }
        $list = $this->model
                ->with(['user'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
        foreach ($list as $row) {
            $row->visible(['id', 'createtime', 'action', 'filename', 'msg']);
            $row->visible(['user']);
            $row->getRelation('user')->visible(['nickname','username']);
            $msg = $row['action_text'].$row['type_text']."《".$row['filename']."》";
            $row['msg'] = $msg;
            $row['createtime'] = date("Y-m-d H:i",$row['createtime']);
        }
        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }

    /**
     * 导出
     */
    public function export(){
        
        $limit = $this->request->post('limit') ?? 10000;
        $page = $this->request->post('page') ?? 1;
        $this->request->get([config('paginate.var_page') => $page]);
        $sort = 'id';
        $order = 'DESC';

        $where = [];
        $user_ids = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('user_ids');
        $where['user_id'] = ['in', explode(',', $user_ids)];
        if($this->request->post('username')){
            $where['user.username'] = ['like',"%{$this->request->post('username')}%"];
        }
        
        if($this->request->post('keywords')){
            $where['filename'] = ['like',"%{$this->request->post('keywords')}%"];
        }

        $start_date = $this->request->post('start_date') ?? date("Y-m-d", strtotime('-3month'));
        $end_date = $this->request->post('end_date') ?? date("Y-m-d", strtotime('+1 day'));
        if($this->request->post('start_date') && $this->request->post('end_date')){
            $where['console_file_log.createtime'] = ['between',[strtotime($start_date),strtotime($end_date)]];
        }

        $list = $this->model
                ->with(['user'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

        $arr = [
          ['账号','姓名','操作时间', '操作内容'],
        ];
        $tmp = [];
        foreach ($list as $row) {
            $row->visible(['id', 'createtime', 'action', 'filename', 'msg']);
            $row->visible(['user']);
            $row->getRelation('user')->visible(['nickname','username']);
            $msg = $row['action_text'].$row['type_text']."《".$row['filename']."》";
            $row['msg'] = $msg;
            $tmp = [$row['user']['username'],$row['user']['nickname'],date('Y-m-d H:i:s',$row['createtime']),$row['msg']];
            array_push($arr,$tmp);
        }

        $filename = '操作日志-' . substr(md5(time().uniqid()),0,6);
        try {
            $cache = Cache::init();
            // 缓存数据
            $cache->set($filename, json_encode($arr), 60);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('导出成功',['url' => request()->domain() . '/api/master/console_file_log/downloadFile?filename='.$filename]);
    }

    public function downloadFile()
    {
        // 1. 获取并解析缓存数据
        $data = cache(input('filename'));
        if (empty($data)) {
            return $this->error('文件已过期');
        }
        $arr = json_decode($data, true);
        $title = input('filename') . '.xlsx';

        // 2.（可选）使用内存缓存减少内存占用
        // \PhpOffice\PhpSpreadsheet\Settings::setCacheStorageMethod(
        //     \PhpOffice\PhpSpreadsheet\CachedObjectStorageFactory::cache_to_phpTemp,
        //     ['memoryCacheSize' => '512MB']
        // );

        // 3. 填充 Spreadsheet
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        foreach ($arr as $row => $cols) {
            foreach ($cols as $col => $val) {
                $sheet->setCellValueExplicitByColumnAndRow(
                    $col + 1,
                    $row + 1,
                    $val,
                    \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING
                );
            }
        }

        // 4. 准备 Writer 并开启兼容设置
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->setOffice2003Compatibility(true);
        $writer->setPreCalculateFormulas(false);

        // 5. 清理输出缓冲区
        if (ob_get_length() > 0) {
            ob_end_clean(); // 确保输出流纯净 :contentReference[oaicite:15]{index=15}
        }

        // 6. 发送响应头
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'); // :contentReference[oaicite:16]{index=16}
        header('Content-Disposition: attachment; filename="'. $title .'"');
        header('Cache-Control: max-age=0');
        header('Pragma: public');

        // 7. 输出并结束
        $writer->save('php://output'); // :contentReference[oaicite:17]{index=17}
        exit;
    }

}
