<?php

namespace app\api\controller\callback;

use app\common\controller\Api;
use app\common\service\BuildGraphService;

use think\Db;

class Alibaba extends Api
{

	protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /*
    * 定时任务获取alibaba解析pdf任务状态
    */
    public function checkAlibabaTask()
    {


    	$list = Db::name('ai_file_slice_task')->where('status',1)->limit(5)->select();

    	foreach ($list as $v) {
    		$r = BuildGraphService::getAliyunAnalyzePdfStatus($v['ali_order_id']);
    		if ($r) {
    			$result = BuildGraphService::getAliyunAnalyzePdfResult($v['ali_order_id']);
    			BuildGraphService::aliSaveRequestData($result['Data']['layouts'],$v['file_id']);
    		}
    	}

    }



}