{"name": "karsonzhang/fastadmin", "description": "the fastest admin framework", "type": "project", "keywords": ["<PERSON><PERSON><PERSON>", "thinkphp"], "homepage": "https://www.fastadmin.net/", "license": "Apache-2.0", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.2.0", "topthink/framework": "dev-master", "topthink/think-captcha": "^1.0", "topthink/think-installer": "^1.0.14", "topthink/think-queue": "1.1.6", "topthink/think-helper": "^1.0.7", "karsonzhang/fastadmin-addons": "~1.3.2", "overtrue/pinyin": "^3.0", "phpoffice/phpspreadsheet": "1.19", "overtrue/wechat": "^4.6", "nelexa/zip": "^3.3", "ext-json": "*", "ext-curl": "*", "ext-pdo": "*", "ext-bcmath": "*", "txthinking/mailer": "^2.0", "phpoffice/phpexcel": "^1.8", "volcengine/volc-sdk-php": "^1.0", "phpoffice/phpword": "^1.3", "alibabacloud/openapi-util": "^0.2.1", "alibabacloud/tea": "^3.2", "alibabacloud/tea-rpc": "^0.1.11", "alibabacloud/tea-fileform": "^0.3.4", "alibabacloud/credentials": "^1.2", "alibabacloud/docmind-api-20220711": "^1.4"}, "config": {"preferred-install": "dist", "allow-plugins": {"topthink/think-installer": true, "easywechat-composer/easywechat-composer": true}}, "repositories": [{"type": "git", "url": "https://gitee.com/fastadminnet/framework.git"}, {"type": "git", "url": "https://gitee.com/fastadminnet/think-captcha.git"}]}