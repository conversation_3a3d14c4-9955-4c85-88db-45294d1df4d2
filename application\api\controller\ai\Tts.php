<?php
namespace app\api\controller\ai;

use app\common\controller\Api;
use think\Db;
use app\common\service\IpService;

class Tts extends Api
{
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];
    
    public function get()
    {
        // 设置必要的头部信息
        // header("Access-Control-Allow-Origin: *");
        // header('Content-type: text/html;charset=UTF-8');
        header('Content-Type: audio/aac');
        setlocale(LC_CTYPE, 'UTF8', 'en_US.UTF-8');
        // header('Cache-Control: no-cache');
        // header('Connection: keep-alive');
        // header('X-Accel-Buffering: no');
        // header('Content-Transfer-Encoding: binary');
        // header('Transfer-Encoding: chunked');

        // 获取输入参数
        $token = "jiweiyuanhui";
        $rq_id = uniqid();
        $uid = $rq_id;
        $text = input('text');
        $detection = $this->detectJapanese($text);
        if ($detection['is_japanese']) {
            $language = 'ja';
        }else{
            $language = 'zh';
        }
        $cluster = input('cluster');
        $voice_type = input('voice_type');
        $cache = input('cache') ?? 0;
        $speed_ratio = input('speed_ratio') ?? 1.0;

        // 设置文件存储目录
        if($cache)
        {
            $destDir = "../runtime/voice/tts/";
        } else {
            $destDir = "../runtime/voice/" . date("Ym/") . "/";
        }
        if (!is_dir($destDir)) {
            mkdir($destDir, 0755, true);
        }

        // 生成文件名
        $md5 = md5($text . $cluster . $voice_type);
        $file_name = $destDir . $md5 . ".wav";

        // 如果文件不存在，则调用 Python 脚本生成
        if (!file_exists($file_name) || filesize($file_name) < 50 * 1024) {
            $command = escapeshellcmd('/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9') . 
                    ' /mnt/sdc/wwwroot/ai-master/python/huoshan/tts_websocket.py ' . 
                    escapeshellarg($token) . ' ' . 
                    escapeshellarg($uid) . ' ' . 
                    escapeshellarg($rq_id) . ' ' . 
                    escapeshellarg($text) . ' ' . 
                    escapeshellarg($file_name) . ' ' . 
                    escapeshellarg($cluster) . ' ' . 
                    escapeshellarg($voice_type) . ' ' . 
                    escapeshellarg($speed_ratio) . ' ' . 
                    escapeshellarg($language) . 
                    ' > /dev/null 2>&1 &';
            exec($command);
        }

        // 设置块大小和超时
        $buffer_size = 4096;
        $offset = 0;
        $last_size = 0; // 记录文件的最后大小
        $timeout = time() + 60; // 设置最大超时为60秒

        // 开始流式输出
        while (time() < $timeout) {
            clearstatcache();
            if (file_exists($file_name)) {
                $current_size = filesize($file_name); // 当前文件大小
                if ($current_size > $last_size) {
                    $file = fopen($file_name, "rb");
                    if ($file) {
                        fseek($file, $offset);
                        
                        while (!feof($file)) {
                            $data = fread($file, $buffer_size);
                            if ($data === false || strlen($data) == 0) {
                                break; // 如果没有新数据，退出内部循环
                            }
                            echo $data;
                            flush();
                            ob_flush();
                            $offset += strlen($data); // 更新文件指针位置
                        }
                        fclose($file);
                    }
                    $last_size = $current_size; // 更新最后大小
                    sleep(1);
                } else {
                    sleep(1); // 没有新数据，等待100毫秒后继续检查
                }
            } else {
                sleep(1); // 文件还未生成，等待100毫秒后继续检查
            }
            clearstatcache();

            // 如果文件最终生成且大小不再变化，则退出循环
            if (file_exists($file_name) && filesize($file_name) === $last_size && filesize($file_name) > 50 * 1024) {
                break; // 文件读取完成，退出循环
            }
        }

        // 如果超时后仍未完成，进行适当处理
        if (!file_exists($file_name) || filesize($file_name) < $last_size) {
            http_response_code(500);
            echo "Error: File generation timed out or is incomplete.";
        }

        exit;
    }

    public function test()
    {
        setlocale(LC_CTYPE, 'UTF8', 'en_US.UTF-8');
        // header('Content-type: text/html;charset=UTF-8');
        // 设置必要的头部信息
        // header("Access-Control-Allow-Origin: *");
        // header('Content-Type: audio/aac');
        // header('Cache-Control: no-cache');
        // header('Connection: keep-alive');
        // header('X-Accel-Buffering: no');
        // header('Content-Transfer-Encoding: binary');
        // header('Transfer-Encoding: chunked');

        // 获取输入参数
        $token = "jiweiyuanhui";
        $rq_id = uniqid();
        $uid = $rq_id;
        $text = input('prompt');
        $cluster = input('cluster');
        $voice_type = input('voice_type');
        $cache = input('cache') ?? 0;

        // 设置文件存储目录
        if($cache)
        {
            $destDir = "../runtime/test/tts/";
        } else {
            $destDir = "../runtime/test/" . date("Ym/") . "/";
        }
        if (!is_dir($destDir)) {
            mkdir($destDir, 0755, true);
        }

        // 生成文件名
        $md5 = md5($text . $cluster . $voice_type);
        $file_name = $destDir . $md5 . ".wav";

        // 如果文件不存在，则调用 Python 脚本生成
        if (!file_exists($file_name) || filesize($file_name) < 70 * 1024) {
            $command = escapeshellcmd('/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9') . 
                    ' /mnt/sdc/wwwroot/ai-master/python/huoshan/tts_websocket.py ' . 
                    escapeshellarg($token) . ' ' . 
                    escapeshellarg($uid) . ' ' . 
                    escapeshellarg($rq_id) . ' ' . 
                    escapeshellarg($text) . ' ' . 
                    escapeshellarg($file_name) . ' ' . 
                    escapeshellarg($cluster) . ' ' . 
                    escapeshellarg($voice_type) . 
                    ' > /dev/null 2>&1 &';
            dump($command);exit;
            exec($command);
        }

        // 设置块大小和超时
        $buffer_size = 4096;
        $offset = 0;
        $last_size = 0; // 记录文件的最后大小
        $timeout = time() + 60; // 设置最大超时为60秒

        // 开始流式输出
        while (time() < $timeout) {
            clearstatcache();
            if (file_exists($file_name)) {
                $current_size = filesize($file_name); // 当前文件大小
                if ($current_size > $last_size) {
                    $file = fopen($file_name, "rb");
                    if ($file) {
                        fseek($file, $offset);
                        
                        while (!feof($file)) {
                            $data = fread($file, $buffer_size);
                            if ($data === false || strlen($data) == 0) {
                                break; // 如果没有新数据，退出内部循环
                            }
                            echo $data;
                            flush();
                            ob_flush();
                            $offset += strlen($data); // 更新文件指针位置
                        }
                        fclose($file);
                    }
                    $last_size = $current_size; // 更新最后大小
                    sleep(1);
                } else {
                    sleep(1); // 没有新数据，等待100毫秒后继续检查
                }
            } else {
                sleep(1); // 文件还未生成，等待100毫秒后继续检查
            }
            clearstatcache();

            // 如果文件最终生成且大小不再变化，则退出循环
            if (file_exists($file_name) && filesize($file_name) === $last_size && filesize($file_name) > 70 * 1024) {
                break; // 文件读取完成，退出循环
            }
        }

        // 如果超时后仍未完成，进行适当处理
        if (!file_exists($file_name) || filesize($file_name) < $last_size) {
            http_response_code(500);
            echo "Error: File generation timed out or is incomplete.";
        }

        exit;
    }

    function detectJapanese($text) {
        // 平假名范围: 3040-309F
        // 片假名范围: 30A0-30FF
        // 中日韩统一汉字: 4E00-9FFF
        // 日文标点符号: 3000-303F
        
        $hiragana = '/[\x{3040}-\x{309F}]/u';  // 平假名
        $katakana = '/[\x{30A0}-\x{30FF}]/u';  // 片假名
        $kanji = '/[\x{4E00}-\x{9FFF}]/u';     // 汉字
        $japanesePunct = '/[\x{3000}-\x{303F}]/u'; // 日文标点
        
        $result = [
            'has_hiragana' => preg_match($hiragana, $text) > 0,
            'has_katakana' => preg_match($katakana, $text) > 0,
            'has_kanji' => preg_match($kanji, $text) > 0,
            'has_japanese_punct' => preg_match($japanesePunct, $text) > 0,
            'is_japanese' => false
        ];
        
        // 如果包含平假名或片假名，则判断为日文
        $result['is_japanese'] = $result['has_hiragana'] || $result['has_katakana'];
        
        return $result;
    }
}