import{_,c as i,r as l,o as d,a as r,b as s,t as n,u as p,d as h,w as m,e as u,f as v,p as b,g as f}from"./index-CS0NphGI.js";const g=""+new URL("404-N4aRkdWY.png",import.meta.url).href,e=""+new URL("404_cloud-CPexjtDj.png",import.meta.url).href,a=t=>(b("data-v-5945313b"),t=t(),f(),t),w={class:"wscn-http404-container"},x={class:"wscn-http404"},k=u('<div class="pic-404" data-v-5945313b><img class="pic-404__parent" src="'+g+'" alt="404" data-v-5945313b><img class="pic-404__child left" src="'+e+'" alt="404" data-v-5945313b><img class="pic-404__child mid" src="'+e+'" alt="404" data-v-5945313b><img class="pic-404__child right" src="'+e+'" alt="404" data-v-5945313b></div>',1),N={class:"bullshit"},S=a(()=>s("div",{class:"bullshit__oops"}," 404错误! ",-1)),I={class:"bullshit__headline"},R=a(()=>s("div",{class:"bullshit__info"}," 对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。 ",-1)),V={__name:"404",setup(t){let c=i(()=>"找不到网页！");return(B,C)=>{const o=l("router-link");return d(),r("div",w,[s("div",x,[k,s("div",N,[S,s("div",I,n(p(c)),1),R,h(o,{to:"/index",class:"bullshit__return-home"},{default:m(()=>[v(" 返回首页 ")]),_:1})])])])}}},U=_(V,[["__scopeId","data-v-5945313b"]]);export{U as default};
