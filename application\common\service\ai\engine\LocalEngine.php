<?php

namespace app\common\service\ai\engine;

use app\common\service\ai\engine\strategy\BasicStrategy;
use app\common\service\ai\engine\strategy\EnhanceStrategy;
use app\common\service\ai\engine\strategy\ProfessionalStrategy;
use app\common\service\IpService;
use app\common\library\aliyun\VisionModel;
use app\common\service\BaiduSearchService;
use think\Db;

/**
 * 统一的本地引擎类
 * 通过策略模式支持不同的增强功能
 */
class LocalEngine extends BaseLocal
{
    /** @var \app\common\service\ai\engine\strategy\StrategyInterface */
    private $strategy;
    
    /** 策略类型常量 */
    const STRATEGY_BASIC = 'basic';
    const STRATEGY_ENHANCE = 'enhance';
    const STRATEGY_PROFESSIONAL = 'professional';
    
    /**
     * 构造函数
     * @param string $strategyType 策略类型
     */
    public function __construct($strategyType = self::STRATEGY_BASIC)
    {
        $this->setStrategy($strategyType);
    }
    
    /**
     * 设置策略
     * @param string $strategyType
     */
    public function setStrategy($strategyType)
    {
        switch ($strategyType) {
            case self::STRATEGY_ENHANCE:
                $this->strategy = new EnhanceStrategy();
                break;
            case self::STRATEGY_PROFESSIONAL:
                $this->strategy = new ProfessionalStrategy();
                break;
            default:
                $this->strategy = new BasicStrategy();
                break;
        }
        
        // 将当前实例传递给策略，以便策略可以访问基础方法
        $this->strategy->setEngine($this);
    }
    
    /**
     * 聊天方法
     */
    public function chat($console_type, $contextId, $msg, $robot, $card, $lang = 'cn', $model = null, $debug = false, $ext = [])
    {
        $this->msg = $msg;
        $this->debug = $debug;
        $ip = request()->ip();
        $area = IpService::getArea($ip);

        // 获取项目与模型信息
        $project = $this->getProjectInfo($robot);
        $bigModel = $this->getModelInfo($project, $model);
        $this->setBigModelConfig($bigModel);

        $messages = [];

        // 处理系统提示词
        if ($project['system_prompt']) {
            $messages[] = [
                "role" => "system",
                "content" => $project['system_prompt']
            ];
        }

        // 获取上下文消息
        $old_messages = $this->buildContextMessages($contextId);
        $messages = array_merge($messages, $old_messages);

        // 存在图片信息时调用视觉理解接口
        if(!empty($ext['image'])){
            $vision = new VisionModel();
            $result = $vision->analyze($ext['image'], $msg);
            if(!empty($result['content']))
            {
                $answerStr = $result['content'];
                // 获取引用内容
                if($console_type == 2){
                    $vectorStr = $this->buildReferenceHtml($answerStr);
                }else{
                    $vectorStr = $this->buildReferenceContent($project, $answerStr);
                }
                if ($this->references) {
                    $this->save_references = $this->references;
                }
                $this->saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr, $ext['image']);
                // 发送消息到前端
                $result = json_encode([
                    'text' => $answerStr,
                    'reasoning_content' => '',
                    'references' => $this->references,
                    'msg_id' => $this->lastMsgId
                ], JSON_UNESCAPED_UNICODE);
                $this->sendMessage($contextId, $result);
            }

            exit;
        }

        // 获取引用内容
        if($console_type == 2){
            $vectorStr = $this->buildReferenceHtml($msg);
        }else{
            $vectorStr = $this->buildReferenceContent($project, $msg);
        }
        $userPrompt = $this->buildSystemPrompt($msg ?? '', $lang, $vectorStr);
        
        // 用户输入
        $messages[] = [
            "role" => "user",
            "content" => $userPrompt
        ];
        
        if($this->debug){
            dump($messages);exit;
        }

        // 构造请求参数
        $payload = $this->strategy->buildPayload($messages, $this);
        $headers = $this->buildHeaders();

        $params = json_encode($payload);

        // 发起CURL请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $answerStr = '';
        $this->saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr);

        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) use (
            $lang, $contextId, $robot, $msg, $ip, $area, $card, &$answerStr
        ) {
            return $this->handleStreamResponse($data, $lang, $contextId, $robot, $msg, $ip, $area, $card, $answerStr);
        });

        curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Curl error: ' . curl_error($ch);
        }
        curl_close($ch);
    }
    
    /**
     * 构造引用内容 - 委托给策略处理
     */
    protected function buildReferenceContent($project, $msg)
    {
        return $this->strategy->buildReferenceContent($project, $msg, $this);
    }
    
    /**
     * 构造引用网页
     */
    protected function buildReferenceHtml($query)
    {
        $baiduSearch = new BaiduSearchService();
        try {
            $result = $baiduSearch->search($query, [
                'site' => [],
                'time_range' => 'year'
            ]);
            $vectorStr = '';
            $vectorId = 1;
            foreach($result['references'] as $item)
            {
                $vectorStr .= "[{$vectorId}] " . $item['title'] . "：" . $item['content'] . "\n";
                $this->references[] = [
                    'title'   => $item['title'],
                    'content' => $item['content'],
                    'file'    => $item['url'],
                    'cosine_similarity' => 0,
                    'relevance_score' => 0
                ];
                $vectorId++;
            }
            return $vectorStr;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }
    
    /**
     * 处理流式响应
     */
    private function handleStreamResponse($data, $lang, $contextId, $robot, $msg, $ip, $area, $card, &$answerStr)
    {
        // 检测 [DONE] 标识
        if (strpos($data, '[DONE]') !== false) {
            $data = str_replace('[DONE]', '', $data);
            ob_end_flush();
            if ($answerStr) {
                $this->saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, 'cn', $answerStr);
                // 异步请求卡片标签处理
                \fast\Http::sendAsyncRequest(request()->domain() . '/api/ai/card/tagExtractionOne', [
                    'msg' => $msg,
                    'robot' => $robot,
                    'msg_id' => $this->lastMsgId ?? 0
                ]);
            }
            exit;
        }

        $lines = explode("\n", trim(str_replace('data:', '', $data)));
        foreach ($lines as $line) {
            if (empty($line)) continue;
            $v = json_decode($line, true);
            if (empty($v)) continue;

            // 判断结束标识（针对部分模型返回）
            if ($this->paramsType && !empty($v['output']['choices'][0]['finish_reason'])
                && $v['output']['choices'][0]['finish_reason'] === 'stop'
            ) {
                ob_end_flush();
                if ($answerStr) {
                    $this->saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, 'cn', $answerStr);
                    \fast\Http::sendAsyncRequest(request()->domain() . '/api/ai/card/tagExtractionOne', [
                        'msg' => $msg,
                        'robot' => $robot,
                        'msg_id' => $this->lastMsgId ?? 0
                    ]);
                }
                exit;
            }

            $reasoningContent = '';
            $text = $this->paramsType
                ? ($v['output']['choices'][0]['message']['content'] ?? '')
                : ($v['choices'][0]['delta']['content'] ?? '');
            $reasoningContent = $this->paramsType ? '' : (empty($text) ? ($v['choices'][0]['delta']['reasoning_content'] ?? '') : '');

            // 翻译处理（若非中文）
            if ($lang && $lang != 'cn' && trim($text) !== '\n') {
                $this->translateReferences($lang);
            }

            $text = str_replace(["    ", "~"], ["", "—"], $text);
            if ($this->references) {
                $this->save_references = $this->references;
            }

            // 记录累计返回的内容
            $answerStr .= $text;
            // 发送消息到前端
            $result = json_encode([
                'text' => $text,
                'reasoning_content' => $reasoningContent,
                'references' => $this->references,
                'msg_id' => $this->lastMsgId
            ], JSON_UNESCAPED_UNICODE);
            $this->references = [];
            $this->sendMessage($contextId, $result);
        }
        return strlen($data);
    }
    
    /**
     * 保存问答记录
     */
    protected function saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr, $image = '')
    {
        $referencesList = base64_encode(json_encode($this->save_references ?? []));
        $msgShort = mb_substr($msg, 0, 150);
        if($this->lastMsgId)
        {
            Db::name('ai_msg')->where('id', $this->lastMsgId)->update([
                'content' => $answerStr,
                'referrer'=> $referencesList,
            ]);
        }else{
            $this->lastMsgId = Db::name('ai_msg')->insertGetId([
                'robot'   => $robot,
                'chatId'  => $contextId,
                'msg'     => $msgShort,
                'time'    => time(),
                'ip'      => $ip,
                'content' => $answerStr,
                'city'    => $area,
                'referrer'=> $referencesList,
                'card'    => $card,
                'lang'    => $lang,
                'model'   => $this->model,
                'images'  => $image
            ]);
        }
    }
    
    // 公开一些方法供策略使用
    public function getReferences() { return $this->references; }
    public function setReferences($references) { $this->references = $references; }
    public function getMsg() { return $this->msg; }
    public function getDebug() { return $this->debug; }
    public function getGroupId() { return $this->group_id; }
    public function getPgTable() { return $this->pgTable; }
    public function getPythonPath() { return $this->pythonPath; }

    // 暴露数据库连接方法给策略使用
    public function getPgConnect() { return $this->pg_connect(); }

    // 暴露其他需要的方法给策略使用
    public function getModelInfo($project, $model = null) { return parent::getModelInfo($project, $model); }
    public function setBigModelConfigPublic($bigModel) { return $this->setBigModelConfig($bigModel); }
    public function buildContextMessagesPublic($contextId) { return $this->buildContextMessages($contextId); }
    public function buildSystemPromptPublic($msg, $lang, $vectorStr) { return $this->buildSystemPrompt($msg, $lang, $vectorStr); }
    public function buildHeadersPublic() { return $this->buildHeaders(); }
    public function translateReferencesPublic($lang) { return $this->translateReferences($lang); }
    public function sendMessagePublic($contextId, $result) { return $this->sendMessage($contextId, $result); }

    /**
     * 清理合并内容
     */
    public function cleanupCombinedContent($content)
    {
        // 移除重复的换行符
        $content = preg_replace('/\n{3,}/', "\n\n", $content);

        // 移除首尾空白
        $content = trim($content);

        // 移除重复的句子（简单的重复检测）
        $lines = explode("\n", $content);
        $uniqueLines = [];
        $lastLine = '';

        foreach ($lines as $line) {
            $line = trim($line);
            if ($line !== $lastLine && !empty($line)) {
                $uniqueLines[] = $line;
                $lastLine = $line;
            }
        }

        return implode("\n", $uniqueLines);
    }

    /**
     * 非流式调用模型对话，直接返回结果
     */
    public function chatAndReturn($msg,$robot)
    {
        # 获取项目信息
        $project = Db::name('ai_project')->where('id',$robot)->find();

        # 获取模型信息
        $bigmodel = Db::name('ai_bigmodel')->where('id', $project['model_id'])->find();
        $this->url = $bigmodel['api_url'];
        $this->token = $bigmodel['api_key'];
        $this->model = $bigmodel['model'];
        $this->paramsType = in_array($this->model, $this->paramsTypeArr);

        $messages[] = [
            "role" => "user",
            "content" => $msg
        ];

        $systemPrompt = $project['system_prompt'] ?? '';
        $systemPrompt = "首先需要分析用户问题是否和上文相关，如果无相关联，请不要引用上文内容，直接回答用户问题。" . $systemPrompt;

        $stream = false;
        $maxTokens = 2096;
        $stop = ["null"];
        $temperature = 0.3;
        $topP = 0.3;
        $topK = 50;
        $frequencyPenalty = 1;
        $n = 1;
        $responseFormat = ["type" => "text"];
        $tools = [
            [
                "type" => "function",
                "function" => [
                    "description" => "<string>",
                    "name" => "<string>",
                    "parameters" => [],
                    "strict" => false
                ]
            ]
        ];

        $payload = [
            "model" => $this->model,
            "messages" => $messages,
            "stream" => $stream,
            "max_tokens" => $maxTokens,
            "stop" => $stop,
            "temperature" => $temperature,
            "top_p" => $topP,
            "top_k" => $topK,
            "frequency_penalty" => $frequencyPenalty,
            "n" => $n,
            "response_format" => $responseFormat,
            "tools" => $tools,
            "usage" => [
                "prompt_tokens" => 3019,
                "completion_tokens" => 104,
                "total_tokens" => 3123,
                "prompt_tokens_details" => [
                    "cached_tokens" => 2048
                ]
            ]
        ];

        if($this->paramsType){
            $payload = array_merge($payload,[
                "parameters" => [
                    "result_format" => "message",
                    "incremental_output" => true
                ],
                "input" => [
                    "messages" => $messages
                ]
            ]);
        }

        $headers = [
            'Content-Type:application/json',
            'Authorization:Bearer ' . $this->token
        ];

        if($this->paramsType){
            $headers = array_merge($headers,[
                'X-DashScope-SSE:enable'
            ]);
        }

        $params = json_encode($payload);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Curl error: ' . curl_error($ch);
        }
        curl_close($ch);

        $res = json_decode($response,true);
        if (!$res) {
            exception('接口响应错误:' . $response);
        }

        $text = $res['choices'][0]['message']['content'] ?? '';
        return json(['answer' => $text,'references' => $this->references]);
    }
}
