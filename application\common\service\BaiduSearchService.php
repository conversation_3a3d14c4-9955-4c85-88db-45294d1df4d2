<?php

namespace app\common\service;

use think\Exception;
use GuzzleHttp\Client;

class BaiduSearchService
{
    protected $apiKey;
    protected $baseUrl = 'https://qianfan.baidubce.com/v2/ai_search/chat/completions';

    public function __construct($apiKey = null)
    {
        $this->apiKey = "bce-v3/ALTAK-Fbeva8yQdkU5DxiKXKymQ/864c383f3aac6294f59e64f510502945ab52ae31"; // 可以从配置文件读取
    }

    /**
     * 执行百度搜索API请求
     *
     * @param string $query 搜索关键词
     * @param array $options 额外参数（如 site 过滤、时间范围等）
     * @return array
     * @throws Exception
     */
    public function search($query, $options = [])
    {
        $client = new Client();

        $messages = [
            ['role' => 'user', 'content' => $query]
        ];

        $body = [
            'messages' => $messages,
            'search_source' => 'baidu_search_v2',
            'resource_type_filter' => [
                ['type' => 'web', 'top_k' => 10]
            ],
        ];

        // 添加 site 过滤
        if (!empty($options['site'])) {
            $body['search_filter'] = [
                'match' => [
                    'site' => (array)$options['site']
                ]
            ];
        }

        // 添加时间过滤
        if (!empty($options['time_range'])) {
            $body['search_recency_filter'] = $options['time_range'];
        }

        try {
            $response = $client->post($this->baseUrl, [
                'headers' => [
                    'Authorization' => "Bearer ".$this->apiKey,
                    'Content-Type' => 'application/json'
                ],
                'json' => $body
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            return $result;
        } catch (\Exception $e) {
            throw new Exception('百度搜索接口调用失败: ' . $e->getMessage());
        }
    }
}