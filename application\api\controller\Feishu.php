<?php

namespace app\api\controller;

use app\common\controller\Api;

use app\common\service\ai\AiService;
use Smalot\PdfParser\Parser;
use think\Db;

use app\common\service\DocParsingService;
use app\common\util\KnowledgeGraphUtil;
use app\common\service\BuildGraphService;


use think\Queue;



/**
 * 示例接口
 */
class Feishu extends Api
{

    // 无需登录的接口,*表示全部
    protected $noNeedLogin = '*';
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = '*';



    //获取知识库列表
    public function getKnowledgeList()
    {

        $token = $this->getToken();

        $url = 'https://open.feishu.cn/open-apis/wiki/v2/spaces';

        $headers = [];
        $headers['Authorization'] = 'Bearer ' . $token;

        $res = $this->requestClient($url,'GET',[],$headers);

        if ($res['code'] == 0) {
            $this->success('success',$res['data']['items']);
        } else {
            $this->error('获取失败');
        }
    }

    //获取知识库中的文档列表
    public function getDocList()
    {
        $space_id = input('space_id');

        if (empty($space_id)) {
            $this->error('参数错误');
        }
        $token = $this->getToken();
        $url = 'https://open.feishu.cn/open-apis/wiki/v2/spaces/'.$space_id.'/nodes';
        $headers = [];
        $headers['Authorization'] = 'Bearer ' . $token;

        $res = $this->requestClient($url,'GET',[],$headers);

        if ($res['code'] == 0) {

            $list = $res['data']['items'];

            foreach ($list as &$v) {
                $v['obj_create_time'] = date('Y-m-d H:i:s',$v['obj_create_time']);
            }

            $this->success('success',$list);
        } else {
            $this->error('获取失败');
        }


    }



    public function getToken()
    {
        $token = cache('feishuToken');
        if ($token) {
            return $token;
        }
        $url = 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal';
        $params = [
            'json' => [
                'app_id' => 'cli_a7dd18c9a338500d',
                'app_secret' => 'Rebl1KnRfbWkSEUiK8RNzhJv3oQWgme4',
            ]
        ];

        $headers = [];
        $headers['Content-Type'] = 'application/json; charset=utf-8';
        $res = $this->requestClient($url,'POST',$params,$headers);

        if ($res['code'] == 0) {
            cache('feishuToken',$res['tenant_access_token'],7200);

            return $res['tenant_access_token'];
            // $this->success('success',['token' => $res['tenant_access_token']]);
        }


    }



    protected function requestClient(string $url, string $method, array $data = [], array $clientHeader = [], int $timeout = 10)
    {
        $headers = [];
        foreach ($clientHeader as $key => $item) {
            $headers[] = $key . ':' . $item;
        }
        $curl = curl_init($url);
        //请求方式
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
        //post请求
        if (!empty($data['body'])) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data['body']);
        } else if (!empty($data['json'])) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data['json']));
        } else {
            // $curlFn = $this->curlFn;
            // foreach ($curlFn as $item) {
            //     if ($item instanceof \Closure) {
            //         $curlFn($curl);
            //     }
            // }
        }
        //超时时间
        curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
        //设置header头
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        //返回抓取数据
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        //输出header头信息
        curl_setopt($curl, CURLOPT_HEADER, true);
        //TRUE 时追踪句柄的请求字符串，从 PHP 5.1.3 开始可用。这个很关键，就是允许你查看请求header
        curl_setopt($curl, CURLINFO_HEADER_OUT, true);
        //https请求
        if (1 == strpos("$" . $url, "https://")) {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        }
        [$content, $status] = [curl_exec($curl), curl_getinfo($curl)];
        $content = trim(substr($content, $status['header_size']));
        
        return json_decode($content, true);
        
    }



    public function testFile()
    {
        $console_id = 2;

        $files = Db::name('ai_console_file')->where('console_id',$console_id)->select();

        $host = 'https://aimaster.jw100.com.cn/';
        foreach ($files as $f) {

            $savePath = RUNTIME_PATH . 'file/' . $f['name'];
            $this->downloadFile($host.$f['file'],$savePath);
        }

        dump($files);die;
    }



    function downloadFile($remoteFile,$localFile)
    {
        if(file_exists($localFile)) {
            return;
        }

      
        $hander = curl_init();
        try {
            $fp = fopen($localFile,'wb');
        } catch (\Exception $e) {
            return false;
        }

        curl_setopt($hander,CURLOPT_URL,$remoteFile);
        curl_setopt($hander,CURLOPT_FILE,$fp);
        curl_setopt($hander,CURLOPT_HEADER,0);
        curl_setopt($hander,CURLOPT_FOLLOWLOCATION,1);
        curl_setopt($hander,CURLOPT_TIMEOUT,60);
        if(strpos($remoteFile,'https://') == 0) {
            curl_setopt($hander, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($hander, CURLOPT_SSL_VERIFYHOST, false);
        }
        $res = curl_exec($hander);
        curl_close($hander);
        fclose($fp);
        return $res;
    }



    public function testDoubao()
    {

        // $s = '{"code":0,"data":{"generated_answer":"","usage":""},"message":"success","request_id":"02173519534461500000000000000000000ffff0a00640f2fa299"} ';
        // dump(json_decode($s,true));die;
        header("Access-Control-Allow-Origin: *");
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');

        // $s = '{"code":0,"data":{"generated_answer":"","usage":""},"message":"success","request_id":"02173519534461500000000000000000000ffff0a00640f2fa299"} ';
        // dump(json_decode($s,true));die;
        // $aiEngine = AiService::getEngine('siliconflow');
        $aiEngine = AiService::getEngine('doubao');

        $console_key = 'ep-20250103164748-gzgwh';
        // $path = ROOT_PATH.'public/uploads/20241227/[硝烟中的Scrum和XP 我们如何实施Scrum].2008.中文版.pdf';
        // // // 创建解析器实例
        // $parser = new Parser();
        // // 加载PDF文件
        // $pdf = $parser->parseFile($path); // 替换为你的PDF文件路径
        // // 获取PDF文本内容
        // $question = $pdf->getText();
        // $question = mb_convert_encoding($question,'utf-8');
        $question = file_get_contents(ROOT_PATH.'public/uploads/20241227/人工治理.txt');

        $robot = 47;
        $card = 0;

        // $chatId = $aiEngine->getChatId($console_key);
        // $aiEngine->botChat($console_key,'',$question,$robot,$card,'cn');
        $aiEngine->botChat($console_key,'',$question,$robot,$card,'cn');
    }

    // 实现调用AppBuilder接口并进行流式输出
    public function testDoubaoPy()
    {
        header("Access-Control-Allow-Origin: *");
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        $descriptorspec = [
            0 => ["pipe", "r"],  // 标准输入
            1 => ["pipe", "w"],  // 标准输出
            2 => ["pipe", "w"]   // 标准错误输出
        ];
        // $postData = file_get_contents("php://input");
        // $postDataArr = json_decode($postData);
        // $robot = $postDataArr->robot;
        // $this->lang = isset($postDataArr->lang) ? $postDataArr->lang : '';
        // $chatId = $postDataArr->chatId;
        // $card = $postDataArr->card;
        // $question = isset($postDataArr->prompt)?$postDataArr->prompt:"";
        // $console_key = db::name('ai_project')->where('id',$robot)->value('console_key');
        // if(empty($console_key)){
        //     $console_key = "8e36c365-f419-488c-8002-9789d4dcbaee";
        // }
        // $conversation_id = db::name('ai_msg')->where(['id'=>$robot,'chatId'=>$chatId])->value('conversation_id');
        // if(empty($conversation_id))
        // {
        //     $conversation_id = exec('/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/app.py "'.$console_key.'" 1');
        // }


        $process = proc_open('/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/python/huoshan/search_knowledge_and_chat.py ', $descriptorspec, $pipes);
            
        if (is_resource($process)) {
            $answerStr = '';
            $referencesListArr = [];
            while ($s = fgets($pipes[1])) {
                // $json = '\'' . $s . '\'';
                $json = str_replace(["data:","\n","\r"],['','',''],$s);
                $answer = json_decode($json,true);
                if (empty($answer)) {
                    continue;
                }
                $data = json_encode(['text' => $answer['data']['generated_answer'],'references' => '']);
                $this->sendMessage(1,$data);

            }
            fclose($pipes[0]);
            fclose($pipes[1]);
            fclose($pipes[2]);
            // 终止进程
            proc_close($process);
           
        } else {
            die("没有找到合适的答案!");
        }
        
        exit;
    }


    public function testBuildGraph()
    {
        set_time_limit(0);
        // $path = $data['file_path'];
        // $console_file_id = $data['console_file_id'];
        $path = ROOT_PATH . 'public/uploads/20241227/人工智能安全治理框架.pdf';
        $console_file_id = 1258;
        $console_id = 93;
        $filename = '人工智能安全治理框架';

        // Queue::push('app\queue\job\buildGraph\ParsingFile', [
        //     'file_path' => $path,
        //     'console_file_id' => $console_file_id,
        //     'filename' => $filename,
        //     'console_id' => $console_id
        // ]);

        // 回填空格
        // $content = preg_replace('/(\d\.?\d?)/', ' $1', $content);
        
        // $content = '实验一  液体、固体样品红外吸收光谱的测定 实验四 气相色谱法测定乙醇等 4 种溶剂的含量— 内标法';
        // $content = preg_replace('/(\d)\s+(\d)|\s+(\d)\s+|\s+(\d)|(\d)\s+|(—)\s+/', '$1$2$3$4$5$6', $content);
        // dump($content);die;
        //第一步解析文件
        $file_content = DocParsingService::parsing($path);

        //第二步调用大模型生成目录和图谱数据(较耗时)
        // $aiEngine = AiService::getEngine('doubao');
        // $res = $aiEngine->buildGraphChat($file_content);

        // echo $res;die;
        // $res = json_decode($res,true);

        // if (!isset($res['choices']) && !isset($res['choices'][0]['message'])) {
        //     echo '======请求出错======' . "\n";
        //     echo '执行结果：' . json_encode($res,JSON_UNESCAPED_UNICODE) . "\n";
        //     echo date('Y-m-d H:i:s') . "\n";
        //     // return;
        // } else {
            // $content = $res['choices'][0]['message']['content'];
            // echo $content;die;
        $content = '

```目录
1. 人工智能安全治理原则[1][1]
1.1 包容审慎、确保安全[2][2]
1.2 风险导向、敏捷治理[2][3]
1.3 技管结合、协同应对[2][4]
1.4 开放合作、共治共享[2][5]
2. 人工智能安全治理框架构成[1][6]
2.1 安全风险方面[2][7]
2.2 技术应对措施方面[2][8]
2.3 综合治理措施方面[2][9]
2.4 安全开发应用指引方面[2][10]
3. 人工智能安全风险分类[1][11]
3.1 人工智能内生安全风险[2][12]
3.2 人工智能应用安全风险[2][13]
4. 技术应对措施[1][14]
4.1 针对人工智能内生安全风险[2][15]
4.2 针对人工智能应用安全风险[2][16]
5. 综合治理措施[1][17]
6. 人工智能安全开发应用指引[1][18]
6.1 模型算法研发者安全开发指引[2][19]
6.2 人工智能服务提供者安全指引[2][20]
6.3 重点领域使用者安全应用指引[2][21]
6.4 社会公众安全应用指引[2][22]
```

```实体
graph TD
    A[人工智能安全治理框架](1) -->|包含|B[治理原则](1)
    A -->|包含|C[治理框架构成](6)
    A -->|定义|D[安全风险分类](11)
    A -->|实施|E[技术应对措施](14)
    A -->|实施|F[综合治理措施](17)
    A -->|记录|G[安全开发应用指引](18)

    B -->|定义|B1[包容审慎、确保安全](2)
    B -->|包含|B2[风险导向、敏捷治理](3)
    B -->|定义|B3[技管结合、协同应对](4)
    B -->|包含|B4[开放合作、共治共享](5)

    D -->|分类|D1[内生安全风险](12)
    D -->|分类|D2[应用安全风险](13)
    D1 -->|包含|D11[模型算法安全风险](12)
    D1 -->|包含|D12[数据安全风险](12)
    D1 -->|包含|D13[系统安全风险](12)
    D2 -->|涉及|D21[网络域风险](13)
    D2 -->|涉及|D22[现实域风险](13)
    D2 -->|涉及|D23[认知域风险](13)
    D2 -->|涉及|D24[伦理域风险](13)

    E -->|应对|E1[内生风险措施](15)
    E -->|应对|E2[应用风险措施](16)
    E1 -->|包含|E11[可解释性提升](14)
    E1 -->|包含|E12[数据安全管理](14)
    E2 -->|包含|E21[网络防护机制](16)
    E2 -->|包含|E22[现实应用限制](16)

    F -->|建立|F1[分类分级管理](17)
    F -->|完善|F2[数据保护规范](17)
    F -->|构建|F3[责任体系](17)
    F -->|强化|F4[供应链安全](17)

    G -->|指引|G1[研发者规范](19)
    G -->|指引|G2[提供者责任](20)
    G -->|指引|G3[重点领域应用](21)
    G -->|指引|G4[公众使用指南](22)

    C -->|分析|C1[安全风险识别](7)
    C -->|提出|C2[技术应对方案](8)
    C -->|明确|C3[综合治理手段](9)
    C -->|制定|C4[安全开发指南](10)

    F1 -->|要求|F11[系统备案登记](17)
    F2 -->|明确|F21[数据处理要求](17)
    F3 -->|推进|F31[伦理审查制度](17)
    F4 -->|保障|F41[供应链多样性](17)

    E12 -->|包含|E121[数据合规收集](14)
    E12 -->|包含|E122[知识产权保护](14)
    E12 -->|包含|E123[敏感数据过滤](14)
 ```';

            

            // 第三步正则匹配目录和图谱内容
            $catalogReg = '/```目录([^```]*)```/';
            $graphReg = '/```实体([^```]*)```/';
            preg_match($catalogReg,$content,$match);
            $catalogArr = explode("\n",$match[1]);
            $catalog = [];
            foreach ($catalogArr as $v) {
                $v = trim($v);
                if (empty($v)) {
                    continue;
                }
                preg_match('/[^\.]*/',$v,$numberMatch);
                preg_match('/\[(\w*)\]/',$v,$levelMatch);
                $catalog[$numberMatch[0]] = [
                    'id' => $numberMatch[0],
                    'level' => $levelMatch[1],
                    'content' => trim(str_replace([$numberMatch[0] . '.','[' . $levelMatch[1] . ']'],['',''],$v))
                ];

            }

            preg_match($graphReg,$content,$match);
            $graphData = $match[1];
            $graph = KnowledgeGraphUtil::parseMermaid($match[1]);

            $maxId = count($graph['nodes']) + 1;
            $maxChar = 'Z';
            $rootId = 'root';
            $insertNodes = [
                [
                    'name' => $filename,
                    'id' => $rootId,
                    'category' => 0
                ]
            ];  

            $insertLinks = [];
            $lastId = $insertNodes[0]['id'];
            foreach ($catalog as &$v) {
                $tmp = [
                    'name' => $v['content'],
                    'id' => $maxChar . $maxId,
                ];
                if ($v['level'] == 1) {
                    $lastId = $tmp['id'];

                    $insertLinks[] = [
                        'source' => $rootId,
                        'value' => '包含',
                        'target' => $tmp['id'],
                    ];

                    $tmp['category'] = 1;
                } else {
                    $insertLinks[] = [
                        'source' => $lastId,
                        'value' => '包含',
                        'target' => $tmp['id']
                    ];
                    $tmp['category'] = 2;
                }

                $insertNodes[] = $tmp;
                $v['id'] = $tmp['id'];
                $maxId ++;
            }
            unset($v);

            foreach ($graph['nodes'] as &$v) {
                $v['category'] = $catalog[$v['catalog']]['level'] + 1;
                $insertLinks[] = [
                    'source' => $catalog[$v['catalog']]['id'],
                    'value' => '包含',
                    'target' => $v['id']
                ];
            }
            unset($v);

            $graph['nodes'] = array_merge($graph['nodes'],$insertNodes);
            $graph['links'] = array_merge($graph['links'],$insertLinks);

            foreach ($graph['nodes'] as &$v) {
                $nodeCount = 0;
                foreach($graph['links'] as $key => $val){
                    if($v['id'] == $val['source']){
                        $nodeCount++;
                    }
                }
                $v['nodeCount'] = $nodeCount ? $nodeCount : '';
            }   
            unset($v);

            //第四步将原文按照目录切片并入库
            $slices = DocParsingService::sliceDocBycatalog($file_content,array_column($catalog,'content'));

            try {
                Db::startTrans();

                Db::name('ai_file_slice')->where('file_id',$console_file_id)->delete();
                foreach ($catalog as $k => $v) {
                    $insert = [
                        'file_id' => $console_file_id,
                        'catalog' => $v['content'],
                        'sort' => $k,
                        'createtime' => time()
                    ];
                    Db::name('ai_file_slice')->insert($insert);

                }
                //保存图谱数据
                $insert = [
                    'console_id' => $console_id,
                    'file_id' => $console_file_id,
                    'content' => $graphData,
                    'json_data' => json_encode($graph,JSON_UNESCAPED_UNICODE),
                    'createtime' => time()
                ];

                Db::name('ai_knowledge_graph')
                ->where('console_id',$console_id)
                ->where('file_id',$console_file_id)
                ->delete();

                Db::name('ai_knowledge_graph')->insert($insert);

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                dump($e->getMessage());
            }


        // }
    }


    public function parseMermaid($text,$catalogs) 
    {
        $nodes = [];
        $edges = [];
        preg_match_all('/(\w+)\[([^\]]+)\]\((.*?)\)/',$text,$nodeMatches);

        foreach ($nodeMatches[0] as $k=>$v) {
            $nodes[] = [
                'name' => $nodeMatches[2][$k],
                'id' => $nodeMatches[1][$k],
                'catalog' => $nodeMatches[3][$k],
                'level' => $catalogs[$nodeMatches[3][$k]]['level'] + 1
            ];
        }

        preg_match_all('/(\w+)[\[.*\]]*\s*-->\|(.*?)\|(\w+)\[.*\]/',$text,$edgeMatches);

        foreach ($edgeMatches[0] as $k=>$v) {
            $edges[] = [
                'source' => $edgeMatches[1][$k],
                'relation' => $edgeMatches[2][$k],
                'target' => $edgeMatches[3][$k]
            ];
        }

        return ['nodes' => $nodes,'edges' => $edges];
        
    }




    public function testGraph2()
    {
        $content = file_get_contents(ROOT_PATH . 'public/uploads/20241227/test.txt');
        $content = json_decode($content,true);

        $titleHash = [];
        $titleKey = 1;
        $file_id = 1;


        foreach ($content['content']['doc_segments'] as $k => &$v ) {
            $t = '';
            $lastKey = '';
            foreach ($v['title'] as $tk => &$tv) {
                $tv = str_replace("\n",'',trim($tv));
                if ($tk > 0) {
                    $t .= '||' . $tv;
                    if (!isset($titleHash[$t])) {
                        $titleHash[$t] = [
                            'key' => $titleKey,
                            'pid' => $titleHash[$lastKey]['key'],
                            'title' => $tv,
                            'level' => $tk + 1
                        ];
                        $titleKey ++;
                    }
                } else {
                    $t = $tv;
                    if (!isset($titleHash[$t])) {
                        $titleHash[$t] = [
                            'key' => $titleKey,
                            'pid' => 0,
                            'title' => $tv,
                            'level' => 1
                        ];
                        $titleKey ++;
                    }
                }
                $lastKey = $t;
            }

        }   


        // try {
            Db::startTrans();
            $hash = [];
            foreach ($titleHash as $v) {
                $insert = [
                    'name' => $v['title'],
                    'file_id' => $file_id,
                    'level' => $v['level'],
                    'createtime' => time()
                ];

                if ($v['pid'] == 0) {
                    $insert['pid'] = 0;
                }
                $id = Db::name('ai_file_slice_catalog')->insertGetId($insert);
                $hash[$v['key']] = [
                    'id' => $id,
                    'pid' => $v['pid'],
                    'title' => $v['title'],
                ];
            }

            foreach ($hash as $k=>$v) {

                if ($v['pid'] > 0) {
                    $update = [
                        'pid' => $hash[$v['pid']]['id']
                    ];

                    Db::name('ai_file_slice_catalog')->where('id',$v['id'])->update($update);
                }
            }



            foreach ($content['content']['doc_segments'] as $k => $v ) {
                
                if (empty($v['title'])) {
                    dump($v);
                    continue;
                }
                $title = implode('||',$v['title']);
                if (isset($v['content'])) {
                    $insert = [
                        'file_id' => $file_id,
                        'catalog_id' => $hash[$titleHash[$title]['key']]['id'],
                        'content' => $v['content'],
                        'createtime' => time()
                    ];
                    Db::name('ai_file_slice')->insert($insert);
                }

            } 

            // dump($hash);die;

            Db::commit();
        // } catch (\Exception $e) {
        //     Db::rollback();

        //     dump($e->getMessage());
        //     die;
        // }


    }


    public function getFileGraph()
    {

        $file_id = 1;
        $level = 3;

        $list = Db::name('ai_file_slice_catalog')
        ->where('file_id',$file_id)
        ->where('level','<=',$level)
        ->field('id,name,pid,level')
        ->select();

        $nodes = [
            ['id' => '0','name' => '根节点','category' => 1]
        ];
        $edges = [];

        foreach ($list as $v) {
            $nodes[] = [
                'id' => (string)$v['id'],
                'name' => $v['name'],
                'category' => $v['level'] + 1
            ];

            if ($v['pid'] == 0) {
                $edges[] = [
                    'source' => "0",
                    'target' => (string)$v['id'],
                    'value' => '包含'
                ];
            }

            foreach ($list as $v2) {
                if ($v2['pid'] == $v['id']) {
                    $edges[] = [
                        'source' => (string)$v['id'],
                        'target' => (string)$v2['id'],
                        'value' => '包含'
                    ];
                }
            }

        }

        echo json_encode(['nodes' => $nodes,'links' => $edges],JSON_UNESCAPED_UNICODE);

        die;


    }



    public function testFileSlice()
    {
        $filepath = ROOT_PATH . 'public/uploads/20241225/【脚本】螺纹轴类零件的工艺与编程.pdf';

        //推入知识图谱生成队列
        Queue::push('app\queue\job\BuildKnowledgeGraph', [
            'file_path' => ROOT_PATH . "public" . $filepath,
            'console_file_id' => 1
        ]);

        $engine = AiService::getEngine('baidu');

        $res = $engine->splitDocByTitle($filepath);
        $res = json_decode($res,true);
        // dump($res);
        // $r = BuildGraphService::parseContent(1,$res);
        // dump($r);
        // die;

    }


    public function extractCatalog()
    {


        $list = Db::name('ai_console_file')
        ->where('catalog_status',0)
        ->where('deletetime',null)
        ->where('file','not null')
        ->where('type','in',[1,2,3])
        ->field('id,file,catalog_status')
        ->limit(10)
        ->select();
        
        // dump($list);die;
        foreach ($list as $v) {
            //推入知识图谱生成队列
            Queue::push('app\queue\job\BuildKnowledgeGraph', [
                'file_path' => ROOT_PATH . "public" . $v['file'],
                'console_file_id' => $v['id']
            ],'ai_file_buildgraph');
        }
        

        $this->success('success',$list);

    }


    public function teasratsd()
    {
        set_time_limit(0);
        
        $str = '{"content":{"doc_segments":[{"content":" 同学们大家好，本讲主要内容是复杂轴类零件的数控仿真操作。\n 接下来我们进行仿真操作。首先打开数控加工仿真系统软件，为了方便操作，\n我们将视图选项中的左键平移，右键旋转选中，点击确定，打开系统电源，松开\n急停。首先开机以后需要回零，不论是仿真操作还是实践操作都需要回零，点击\nX 轴正向，X 轴回零灯亮，说明 X 轴回零结束，点击 Z 轴正向,Z 轴回零灯亮，说\n明 Z 轴回零结束。\n 回零结束以后，需要定义毛坯，点击零件，定义毛坯，根据零件图最大直径\n26，我们选择毛坯直径 30，长度选择 110，点击确定，放置零件，点击零件，放\n置零件，选中刚才定义好的毛坯，点击安装零件，通过调节它的位置，安装好工\n件。\n 接下来进行选择刀具，点击机床，选择刀具。两把刀具，1 号外圆车刀选择\n35 度，刃长 16，刀尖半径 0.2，外圆左向横柄 93 度。1 号外圆车刀选择完毕。2\n号刀具选择切槽刀，选择 3 毫米刀宽，刀尖半径选 0.2，选外圆，切槽深度，毛\n坯直径为 30，半径 15，我们要选择切槽深度大于毛坯半径，选择 20mm 切槽深\n度。2 号刀具选择完毕，点击确定，两把刀具安装完成。\n 工件和刀具安装完成以后，接下来进行对刀操作。点击 OFFSETTING,我们\n强调对刀一定是在形状模式下，点击形状，而不是在磨耗，点击手动，主轴正转，\n扩大倍率，将刀架快速靠近工件附近，外圆车刀的对刀采取试切法进行对刀。\n 首先用外圆车刀试切右端面时，X 轴方向进，X 轴方向出，注意 Z 轴方向不\n能动。输入 Z0，点击测量，外圆车刀的 Z 轴对刀结束。接下来 X 轴对刀，用外\n圆车刀进行试切工件外圆直径，Z 轴方向进，Z 轴方向出。X 轴方向不能移动，\n点击主轴停止，方便测量，点击测量，剖面图测量，选择刚才我们试切的位置，\n直径为 27.980，退出，输入 X27.980，点击测量，外圆车刀对刀结束，接下来进\n行 2 号刀具槽刀的对刀。\n 将光标移动到 2 号刀补的位置，扩大倍率，将刀架快速移动至安全位置，点\n击手动选刀，选择 2 号切槽刀。进行对刀。槽刀对刀采取的是靠刀法，点击主轴\n正转，选择 Z 轴手摇，打开手轮，左键手轮逆时针旋转进给，右键退出。用槽刀\n的左侧靠近工件的右端面，当发现有切屑时，我们输入 Z0，点击测量。切槽刀\n的 Z 轴对刀结束。\n 点击手动，X 轴对刀，接下来用槽刀的前侧靠近工件外圆，选择 X 轴手摇，\n左键逆时针旋转手轮，当发现有切屑时，我们输入刚才测量直径 X27.980，点击\n[测量]，2 号刀具的对刀结束。\n 手轮收起，点击手动，将刀架快速移到安全位置，点击主轴停止。工件与刀\n具安装完毕，对刀结束以后接下来进行输入程序。\n 输入程序有两种方式，一种是在程序编辑直接输入，一种是直接导入。我们\n采取的是直接导入。点击[编辑],点击[PROG]程序，点击[操作]，点击[右扩展键]，\n输入程序名“O1234”,点击[Read]软键，点击机床，[DNC 传送]，选择我们编辑\n好的程序，纯文本格式，点击[打开]，程序传送完毕，检查程序是否有误，程序\n没有问题，点击复位[REST]，点击[自动]，点击[循环启动]，工件开始加工，工件\n加工完毕，仿真操作结束。\n 以上就是复杂轴类零件的仿真加工操作，本讲结束，感谢观看！\n","title":[]}],"paragraphs":[{"text":" 同学们大家好，本讲主要内容是复杂轴类零件的数控仿真操作。\n 接下来我们进行仿真操作。首先打开数控加工仿真系统软件，为了方便操作，\n我们将视图选项中的左键平移，右键旋转选中，点击确定，打开系统电源，松开\n急停。首先开机以后需要回零，不论是仿真操作还是实践操作都需要回零，点击\nX 轴正向，X 轴回零灯亮，说明 X 轴回零结束，点击 Z 轴正向,Z 轴回零灯亮，说\n明 Z 轴回零结束。\n 回零结束以后，需要定义毛坯，点击零件，定义毛坯，根据零件图最大直径\n26，我们选择毛坯直径 30，长度选择 110，点击确定，放置零件，点击零件，放\n置零件，选中刚才定义好的毛坯，点击安装零件，通过调节它的位置，安装好工\n件。\n 接下来进行选择刀具，点击机床，选择刀具。两把刀具，1 号外圆车刀选择\n35 度，刃长 16，刀尖半径 0.2，外圆左向横柄 93 度。1 号外圆车刀选择完毕。2\n号刀具选择切槽刀，选择 3 毫米刀宽，刀尖半径选 0.2，选外圆，切槽深度，毛\n坯直径为 30，半径 15，我们要选择切槽深度大于毛坯半径，选择 20mm 切槽深\n度。2 号刀具选择完毕，点击确定，两把刀具安装完成。\n 工件和刀具安装完成以后，接下来进行对刀操作。点击 OFFSETTING,我们\n强调对刀一定是在形状模式下，点击形状，而不是在磨耗，点击手动，主轴正转，\n扩大倍率，将刀架快速靠近工件附近，外圆车刀的对刀采取试切法进行对刀。\n 首先用外圆车刀试切右端面时，X 轴方向进，X 轴方向出，注意 Z 轴方向不\n能动。输入 Z0，点击测量，外圆车刀的 Z 轴对刀结束。接下来 X 轴对刀，用外\n圆车刀进行试切工件外圆直径，Z 轴方向进，Z 轴方向出。X 轴方向不能移动，\n点击主轴停止，方便测量，点击测量，剖面图测量，选择刚才我们试切的位置，\n直径为 27.980，退出，输入 X27.980，点击测量，外圆车刀对刀结束，接下来进\n行 2 号刀具槽刀的对刀。\n 将光标移动到 2 号刀补的位置，扩大倍率，将刀架快速移动至安全位置，点\n击手动选刀，选择 2 号切槽刀。进行对刀。槽刀对刀采取的是靠刀法，点击主轴\n正转，选择 Z 轴手摇，打开手轮，左键手轮逆时针旋转进给，右键退出。用槽刀\n的左侧靠近工件的右端面，当发现有切屑时，我们输入 Z0，点击测量。切槽刀\n的 Z 轴对刀结束。\n 点击手动，X 轴对刀，接下来用槽刀的前侧靠近工件外圆，选择 X 轴手摇，\n左键逆时针旋转手轮，当发现有切屑时，我们输入刚才测量直径 X27.980，点击\n[测量]，2 号刀具的对刀结束。\n 手轮收起，点击手动，将刀架快速移到安全位置，点击主轴停止。工件与刀\n具安装完毕，对刀结束以后接下来进行输入程序。\n 输入程序有两种方式，一种是在程序编辑直接输入，一种是直接导入。我们\n采取的是直接导入。点击[编辑],点击[PROG]程序，点击[操作]，点击[右扩展键]，\n输入程序名“O1234”,点击[Read]软键，点击机床，[DNC 传送]，选择我们编辑\n好的程序，纯文本格式，点击[打开]，程序传送完毕，检查程序是否有误，程序\n没有问题，点击复位[REST]，点击[自动]，点击[循环启动]，工件开始加工，工件\n加工完毕，仿真操作结束。\n 以上就是复杂轴类零件的仿真加工操作，本讲结束，感谢观看！\n","node_id":11}]},"name":"msg","mtype":"dict","id":"4461bbed-635b-4740-bfaa-47105aa7be6e"}';
        $res = json_decode($str,true);


        $r = BuildGraphService::parseContent(1,$res);


    }

    function sendMessage($id, $data) {
        echo "id: $id" . PHP_EOL;
        echo "data: $data" . PHP_EOL;
        echo PHP_EOL;
        ob_flush();
        flush();
    }


}
