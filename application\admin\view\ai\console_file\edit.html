<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Console_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-console_id" data-rule="required" data-source="ai/console/index" class="form-control selectpage" name="row[console_id]" type="text" value="{$row.console_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('File_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-file_number" class="form-control" name="row[file_number]" type="text" value="{$row.file_number|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Display_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-display_status" class="form-control" name="row[display_status]" type="number" value="{$row.display_status|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Word_count')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-word_count" class="form-control" name="row[word_count]" type="text" value="{$row.word_count|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
