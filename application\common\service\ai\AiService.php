<?php

namespace app\common\service\ai;


class AiService 
{
	protected static $namespace = '\\app\\common\\service\\ai\\engine\\';

	protected static $AiEnginePool;

	public static function getEngine($type)
	{
		$type = strtolower($type);

		// if ($type == 'doubao') {
		// 	$engine = 'Doubao';
		// } else if ($type == 'baidu') {
		// 	$engine = 'Baidu';
		// } else if ($type == 'siliconflow') {
		// 	$engine = 'Siliconflow';
		// }

		$engine = ucfirst($type);

		$engineName = self::$namespace . $engine;

		return self::$AiEnginePool[$engine] ?? self::$AiEnginePool[$engine] = new $engineName;
	}



}