<?php

namespace app\common\service;

class Convert
{
		
	/*
	* 把定义规则转为标签
	*/
	public static function tag($content)
	{
		if (strpos($content, "http") !== false) {
			$matchResult = [];
			preg_match_all('/(《(.*)》)/isU', $content, $matchResult);
			if(!empty($matchResult[1]))
			{
				foreach($matchResult[1] as $key=>$item)
				{
					$url = $item ?? null;
					if ($url) {
						if (strpos($url, "mp4") !== false) {
							$content = str_replace($url,"<video controls controlsList=\"nodownload\" src=\"{$matchResult[2][$key]}\" style='width:100%;' ></video>",$content);
						} else if (strpos($url, "png") !== false || strpos($url, "jpg") !== false) {
							$content = str_replace($url,"<img onclick=\"showImage('','".$matchResult[2][$key]."')\" src=\"".$matchResult[2][$key]."\" style='width:100%;' />",$content);
						} else if (strpos($url, "http") !== false) {
							if(strpos($url, "||") !== false)
							{
								$matchesArr = explode('||', $matchResult[2][$key]);
								$content = str_replace($url,"<a href=\"{$matchesArr[1]}\" target=\"_blank\" style=\"word-wrap: break-word;color: blue;\">{$matchesArr[0]}</a><br/>",$content);
							}else{
								$content = str_replace($url,"<a href=\"{$matchResult[2][$key]}\" target=\"_blank\" style=\"word-wrap: break-word;color: blue;\">{$matchResult[2][$key]}</a><br/>",$content);
							}
						}
					}
				}
			}
		}
		if(strpos($content, "@@") !== false){
			$content = preg_replace_callback('/@@([^@@]+)@@/', function($matches) {
				return "<div class='common-list-item-content' onclick=\"showOption2('".$matches[1]."')\" style='word-wrap: break-word;'>".$matches[1]."</div>";
			}, $content);
		}
		return $content;
	}

	/*
	* 把定义规则转为样式（不可点击）
	*/
	public static function style($content)
	{
		if (strpos($content, "http") !== false) {
			$matchResult = [];
			preg_match('/《(.*)》/', $content, $matchResult);
			$url = $matchResult[1] ?? null;
			$match = '/《([^》]+)》/u';
			if(empty($url) || strpos($url,'http')===false)
			{
				preg_match('/\*\*(.*)\*\*/isU', $content, $matchResult);
				$url = $matchResult[1] ?? null;
				$match = '/\*\*([^\*]+)\*\*/isU';
			}
			if ($url) {
				if (strpos($url, "mp4") !== false) {
					$content = preg_replace_callback($match, function($matches) {
						return "<video controls controlsList=\"nodownload\" src=\"{$matches[1]}\" style='width:100%;' ></video>";
					}, $content);
				} else if (strpos($url, "png") !== false || strpos($url, "jpg") !== false) {
					$content = preg_replace_callback($match, function($matches) {
						return "<img onclick=\"showImage('".$matches[1]."')\" src=\"".$matches[1]."\" style='width:100%;' />";
					}, $content);
				} else if (strpos($url, "http") !== false) {
					$content = preg_replace_callback($match, function($matches) {
						return "<a href=\"{$matches[1]}\" target=\"_blank\" style=\"word-wrap: break-word;color: blue;\">{$matches[1]}</a>";
					}, $content);
				}
			}
		}
		if(strpos($content, "@@") !== false){
			$content = preg_replace_callback('/@@([^@@]+)@@/', function($matches) {
				return "<div class='common-list-item-content' style='word-wrap: break-word;'>".$matches[1]."</div>";
			}, $content);
		}
		return $content;
	}

}