<?php
namespace app\api\controller\ai;

use app\common\controller\Api;
use think\Db;
use app\common\service\IpService;
class Zhipu extends Api
{
	// 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];
    
	protected $lang = 'cn';
	
	// 实现调用AppBuilder接口并进行流式输出
    public function callStreamApi()
    {
        header("Access-Control-Allow-Origin: *");
		header('Content-Type: text/event-stream');
      	header('Cache-Control: no-cache');
      	header('Connection: keep-alive');
      	header('X-Accel-Buffering: no');
        $descriptorspec = [
            0 => ["pipe", "r"],  // 标准输入
            1 => ["pipe", "w"],  // 标准输出
            2 => ["pipe", "w"]   // 标准错误输出
        ];
		$postData = file_get_contents("php://input");
        $postDataArr = json_decode($postData);
        $robot = $postDataArr->robot;
        $this->lang = isset($postDataArr->lang) ? $postDataArr->lang : '';
		$chatId = $postDataArr->chatId;
		$card = $postDataArr->card;
        $question = isset($postDataArr->prompt)?$postDataArr->prompt:"";
        $file = isset($postDataArr->file[0])? ROOT_PATH . 'public' . $postDataArr->file[0]:"";
        $process = proc_open('/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/python/zhipu/chat.py "'.$question.'" "'.$file.'" ', $descriptorspec, $pipes);
            
        if (is_resource($process)) {
            $answerStr = '';
            $referencesListArr = [];
            while ($s = fgets($pipes[1])) {
                $answer = json_decode($s,true);
                if(isset($answer['content'])){
                    if(is_string($answer['content']))
                    {
                        if($robot==28)
                        {
                            $text = $answer['content'];
                        }else{
                            $text = preg_replace("/\^(.*)\^/", "", $answer['content']);
                        }
                        $referencesList = isset($answer['references'])?$answer['references']:[];
                        if(!empty($referencesList)){
                            foreach($referencesList as $key=>&$value){
                                if(!empty($value['document_id']))
                                {
                                    $value['title'] = \app\admin\model\ai\ConsoleFile::where('file_number',$value['document_id'])->value('name');
                                }
                                $value['title'] = $value['title']? $value['title'] : ($value['document_name'] ?? '内部知识库.docx');
                            }
                            $referencesListArr = $referencesList;
                        }
                        /** 翻译 */
                        if($this->lang && $this->lang != 'cn'){
                            $Translation = new \app\common\library\Translation();
                            $text = $Translation->xfyun($text, 'cn', $this->lang);
                            foreach($referencesList as $rkey=>&$rvalue){
                                $referencesList[$rkey]['title'] = $Translation->xfyun($rvalue['title'], 'cn', $this->lang);
                                $referencesList[$rkey]['content'] = $Translation->xfyun($rvalue['content'], 'cn', $this->lang);
                            }
                        }
                        $text = str_replace("    ", "", $text);
                        $data = json_encode(["text"=>$text,"references"=>$referencesList]);
                        $this->sendMessage($chatId,$data);
                    }
                    if(is_string($answer['content']) && !empty($answer['content']))
                    {
                        $answerStr .= $answer['content'];
                    }
                }
            }
            fclose($pipes[0]);
            fclose($pipes[1]);
            fclose($pipes[2]);
            // 终止进程
            proc_close($process);

            /** 问答记录 */
            if($answerStr)
            {
                $ip = request()->ip();
                $area = IpService::getArea($ip);
                $references = base64_encode(json_encode($referencesListArr));
                $msg_id = Db::name('ai_msg')->insertGetId(['robot'=>$robot, 'chatId'=> $chatId, 'msg'=>$question, 'time'=>time(), 'ip'=> $ip, 'content'=>$answerStr,'city'=>$area, 'referrer'=>$references, 'card'=>$card]);
				$result = [
                    'msg' => $question,
                    'robot' => $robot,
                    'msg_id' => $msg_id
                ];
                \fast\Http::sendAsyncRequest(request()->domain() . '/api/ai/card/tagExtractionOne',$result);
            }
        } else {
            die("没有找到合适的答案!");
        }
        
        exit;
    }
    
    function sendMessage($id, $data) {
        echo "id: $id" . PHP_EOL;
        echo "data: $data" . PHP_EOL;
        echo PHP_EOL;
        ob_flush();
        flush();
    }
}