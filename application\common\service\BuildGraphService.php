<?php

namespace app\common\service;

use app\common\service\ai\AiService;
use AlibabaCloud\Credentials\Credential;
use AlibabaCloud\Credentials\Credential\Config as CredentialConfig;

use AlibabaCloud\SDK\Docmindapi\V20220711\Docmindapi;
use AlibabaCloud\SDK\Docmindapi\V20220711\Models\SubmitDocParserJobRequest;
use AlibabaCloud\SDK\Docmindapi\V20220711\Models\GetDocParserResultRequest;
use AlibabaCloud\SDK\Docmindapi\V20220711\Models\QueryDocParserStatusRequest;

use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use AlibabaCloud\Tea\Exception\TeaUnableRetryError;

use think\Db;

class BuildGraphService
{
    // private static $aliyunAccessKey = 'LTAI5t79eKCRb9qQR2BFoKx1';
    // private static $aliyunAccessKeySecret = '******************************';

    private static $aliyunAccessKey = 'LTAI5tEbjZzriAYmPNw25EU2';
    private static $aliyunAccessKeySecret = '******************************';

    public static function parseContent($file_id,$content)
    {
        $titleHash = [];
        $titleKey = 1;

        foreach ($content['content']['doc_segments'] as $k => &$v ) {
            $t = '';
            $lastKey = '';
            foreach ($v['title'] as $tk => &$tv) {
                $tv = str_replace("\n",'',trim($tv));
                if ($tk > 0) {
                    $t .= '||' . $tv;
                    if (!isset($titleHash[$t])) {
                        $titleHash[$t] = [
                            'key' => $titleKey,
                            'pid' => $titleHash[$lastKey]['key'],
                            'title' => $tv,
                            'level' => $tk + 1
                        ];
                        $titleKey ++;
                    }
                } else {
                    $t = $tv;
                    if (!isset($titleHash[$t])) {
                        $titleHash[$t] = [
                            'key' => $titleKey,
                            'pid' => 0,
                            'title' => $tv,
                            'level' => 1
                        ];
                        $titleKey ++;
                    }
                }
                $lastKey = $t;
            }

        }   

        try {
            Db::startTrans();

            //目录数小于2的使用AI生成
            // if (count($titleHash) < 2) {
            //     self::buildGraphByAi($file_id,$content);
            //     return;
            // }

            $hash = [];
            foreach ($titleHash as $v) {
                $insert = [
                    'name' => $v['title'],
                    'file_id' => $file_id,
                    'level' => $v['level'],
                    'createtime' => time()
                ];

                if ($v['pid'] == 0) {
                    $insert['pid'] = 0;
                }
                $id = Db::name('ai_file_slice_catalog')->insertGetId($insert);
                $hash[$v['key']] = [
                    'id' => $id,
                    'pid' => $v['pid'],
                    'title' => $v['title'],
                ];
            }

            foreach ($hash as $k=>$v) {
                if ($v['pid'] > 0) {
                    $update = [
                        'pid' => $hash[$v['pid']]['id']
                    ];

                    Db::name('ai_file_slice_catalog')->where('id',$v['id'])->update($update);
                }
            }

            foreach ($content['content']['doc_segments'] as $k => $v ) {
                if (empty($v['title']) || !is_array($v['title'])) {
                    // dump($v);
                    continue;
                }
                $title = implode('||',$v['title']);
                if (isset($v['content'])) {
                    $insert = [
                        'file_id' => $file_id,
                        'catalog_id' => $hash[$titleHash[$title]['key']]['id'],
                        'content' => $v['content'],
                        'createtime' => time()
                    ];
                    Db::name('ai_file_slice')->insert($insert);
                }
            } 

            //修改文件目录提取状态完成
            \app\admin\model\ai\ConsoleFile::where("id", $file_id)->update(['catalog_status'=>1]);

            //添加图谱表信息
            $insert = [
                'console_id' => \app\admin\model\ai\ConsoleFile::where("id", $file_id)->value('console_id'),
                'file_id' => $file_id,
                'content' => 'catalog',
                'createtime' => time()
            ];
            Db::name('ai_knowledge_graph')->insert($insert);
            Db::commit();
            
            return ['code' => 1,'msg' => '目录提取成功'];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0,'msg' => $e->getMessage()];
        }


    }



    public static function buildGraphByAi($file_id,$catelogData)
    {   

        $engine = AiService::getEngine('baidu');

        $content = '';
        foreach ($catelogData['content']['doc_segments'] as $k => &$v) {
            $content .= $v['content'];
        } 

        $prompt = '用mermaid的graph LR编写实体关系图，以文档标题为根节点，用这个文档的已有结构，构建完整的知识图谱，实体之间使用包含、定义、问题、分析、措施、常识、管理、制度、技术、知识、处置等关系，删除实体文字中的引号、括号。使用Mermaid流程图语法来简化表示一些关键实体和它们之间的关系，关系类型将用标签的形式附加在箭头旁边。有必要可以使用子图展示。先建立第一级，要保证第一级完整，显示完整的至少3级知识图谱，不要简化。将graph TD改成graph LR。使用子图（subgraph）来组织层级，利用角色（role）来定义实体的类型，以及使用多级链接来表示复杂的关系。
            graph LR
                subgraph 第一层级
                A[实验室安全] -->|包含| B[责任体系]
                A -->|包含| C[规章制度]
                A -->|包含| D[教育培训]
                end
                subgraph 第二层级-责任体系
                B -->|定义| B1[三级体系]
                B1 -->|包含| B1a[学校层面]
                B1a -->|包含| B1a1[安全会议纪要]
                B1a -->|包含| B1a2[安全工作责任人]
                B1 -->|包含| B1b[院系层面]
                B1b -->|包含| B1b1[安全工作队伍]
                B1b -->|包含| B1b2[责任书签订]
                B1 -->|包含| B1c[实验室层面]
                B1c -->|包含| B1c1[各级责任人]
                B1c -->|包含| B1c2[安全责任书]
                end
                B1a1 -.->|支持| C1a
                B1a2 -.->|依据| C1a
                B1b1 -.->|遵循| C1a
                B1b2 -.->|遵循| C2
                B1c1 -.->|执行| C1b
                B1c2 -.->|遵循| C2a
            
                D1a -.->|提升| D2a
                D1b -.->|增强| D2b';
        $prompt = str_replace("\n",'\n',$prompt);
        $prompt = str_replace("\r",'\r',$prompt);

        $messages = [];
        $messages[] = [
            'role' => 'system',
            'content' => $prompt
        ];

        $content = str_replace("\n",'\n',$content);
        $content = str_replace("\r",'\r',$content);

        $messages[] = ['role' => 'user','content' => $content];
        $engine->chatAndReturn2($messages);

    }





    /*
    * 按照标题切割文档(知识图谱)
    */
    public static function splitDocByTitle($filepath,$console_file_id)
    {
        $console_id = \app\admin\model\ai\ConsoleFile::where("id", $console_file_id)->value('console_id');
        $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$console_id.',console_ids)')->field('id,buildgraph_type')->find();
        if ($project && $project['buildgraph_type'] == 1 && pathinfo($filepath,PATHINFO_EXTENSION) == 'pdf') {
            return self::aliyunAnalyzePdfBuildTask($filepath,$console_file_id);
        } else {
            $engine = AiService::getEngine('baidu');
            $res = $engine->splitDocByTitle($filepath);
            $res = json_decode($res,true);
            if (!$res) {
                //修改文件目录提取状态失败
                \app\admin\model\ai\ConsoleFile::where("id", $console_file_id)->update(['catalog_status'=>2]);
            }
            return self::parseContent($console_file_id,$res);
        }
        // if (pathinfo($filepath,PATHINFO_EXTENSION) == 'pdf') {
        //     // return self::mineruSplit($filepath,$console_file_id);
        //     // return self::textinSplit($filepath,$console_file_id);


        // } else if (pathinfo($filepath,PATHINFO_EXTENSION) == 'doc' || pathinfo($filepath,PATHINFO_EXTENSION) == 'docx') {
        //     $engine = AiService::getEngine('baidu');
        //     $res = $engine->splitDocByTitle($filepath);
        //     $res = json_decode($res,true);
        //     if (!$res) {
        //         //修改文件目录提取状态失败
        //         \app\admin\model\ai\ConsoleFile::where("id", $console_file_id)->update(['catalog_status'=>2]);
        //     }
        //     return self::parseContent($console_file_id,$res);
        // }


    }


    /*
    * mineruApi方式解读pdf文档
    */
    public static function mineruSplit($filepath,$console_file_id)
    {

        $key = 'mineru-analyze-task-' . $console_file_id;
        $task = cache($key);

        if ($task == 1) {
            return;
        } else {
            cache($key,1);
        }


        // $host = request()->domain();
        $host = 'https://aimaster.jw100.com.cn';
        
        // $fileUrl = $filepath;
        $arr = explode('/public/',$filepath);
        $fileUrl = $host . '/' . $arr[1];
        
        $pathinfo = pathinfo($filepath);

        $secretToken = 'eyJ0eXBlIjoiSldUIiwiYWxnIjoiSFM1MTIifQ.eyJqdGkiOiI1MzAwMjQ1OCIsInJvbCI6IlJPTEVfUkVHSVNURVIiLCJpc3MiOiJPcGVuWExhYiIsImlhdCI6MTc0Nzk4NzI0MiwicGhvbmUiOm51bGwsImVtYWlsIjpudWxsLCJleHAiOjE3NDg1OTIwNDJ9.3vYCeuK79gbZ1loO9hAn3Vqt3yktzoioZ_C0-5O_WLdWk9vKAGZX3SSyiAJuffWIB5bf4uJxH8HChzGnWKWBfQ';

        $url = 'https://mineru.org.cn/api/v4/extract/task/batch';
        $callbackUrl = $host . '/api/callback/mineru/index';
        $seed = \fast\Random::alnum(16);

        $params = [
            'files' => [
                ['name' => $pathinfo['basename'],'is_ocr' => true,'url' => $fileUrl]
            ],
            'callback' => $callbackUrl,
            'seed' => $seed,
            // 'extra_formats' => 'json',
        ];

        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $secretToken
        ];

        $res = self::requestClient($url,'POST',['json' => $params],$headers);
        cache('mineru-analyze-' . $pathinfo['filename'],$console_file_id);

        return $res;
    }


    




    public function mineruSaveData($list,$file_id)
    {
    
        // dump($list);die;
        // dump($file_id);
        try {
            Db::startTrans();
            Db::name('ai_file_slice')->where('file_id',$file_id)->delete();
            Db::name('ai_file_slice_catalog')->where('file_id',$file_id)->delete();
            $cate_id = '';
            foreach ($list as $v) {
                if (isset($v['text_level']) && $v['text_level'] == 1 && $v['type'] == 'text') {
                    $insert = [
                        'name' => $v['text'],
                        'file_id' => $file_id,
                        'level' => 1,
                        'createtime' => time(),
                        'pid' => 0
                    ];
                    $cate_id = Db::name('ai_file_slice_catalog')->insertGetId($insert);
                } else {
                    if ($cate_id && $v['type'] == 'text' && !empty($v['text'])) {
                        $file_slice_insert = [
                            'file_id' => $file_id,
                            'catalog_id' => $cate_id,
                            'content' => $v['text'],
                            'createtime' => time()
                        ];
                        // dump($file_slice_insert);
                        // dump($v);
                        Db::name('ai_file_slice')->insert($file_slice_insert);
                    }
                }
                
            }

            //修改文件目录提取状态完成
            \app\admin\model\ai\ConsoleFile::where("id", $file_id)->update(['catalog_status'=>1]);

            //添加图谱表信息
            $insert = [
                'console_id' => \app\admin\model\ai\ConsoleFile::where("id", $file_id)->value('console_id'),
                'file_id' => $file_id,
                'content' => 'catalog',
                'createtime' => time()
            ];
            Db::name('ai_knowledge_graph')->insert($insert);
            Db::commit();
            
            return ['code' => 1,'msg' => '目录提取成功'];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0,'msg' => $e->getMessage()];
        }
    }


    /*
    * textin方式解读pdf文档
    */
    public static function textinSplit($filepath,$console_file_id)
    {

        $host = 'https://aimaster.jw100.com.cn';
        $arr = explode('/public/',$filepath);
        $fileUrl = $host . '/' . $arr[1];

        // $url = 'https://api.textin.com/ai/service/v1/file-convert/pdf-to-word';
        $url = 'https://api.textin.com/ai/service/v1/pdf_to_markdown';
        $params = [
            'page_details' => 0, //不生成每一页的详情
            'get_image' => 'objects', //返回页面内的图像对象
            // 'catalog_details' => 1 //返回目录详情
            'formula_level' => 2,  //公式识别 0全识别 2不识别
        ];

        $url .= '?' . http_build_query($params);

        $headers = [
            'Content-Type' => 'text/plain',
            'x-ti-app-id' => '04b258a4f6198a76e3ab7a425a9b90f6',
            'x-ti-secret-code' => 'e980f6cd8831eaa67c598419c48c875a'
        ];

        $requestParams = ['body' => $fileUrl];

        $res = self::requestClient($url,'POST',$requestParams,$headers);
        // file_put_contents(ROOT_PATH . 'public/requestData.txt',date('Y-m-d H:i:s') . "\r\n",FILE_APPEND);

        // $str = file_get_contents(ROOT_PATH . 'public/requestData.txt');
        // $res = json_decode($str,true);
        if (!$res['code'] || $res['code'] != 200) {
            return ['code' => 0,'msg' => '请求解析接口出错：' . json_encode($res,JSON_UNESCAPED_UNICODE)];
        }

        $detail = $res['result']['detail'];

        try {
            Db::startTrans();
            Db::name('ai_file_slice')->where('file_id',$console_file_id)->delete();
            Db::name('ai_file_slice_catalog')->where('file_id',$console_file_id)->delete();
            $cate_id = '';
            $lastNode = [];

            foreach ($detail as $v) {

                if ($v['type'] == 'paragraph') {
                    if ($v['sub_type'] == 'text_title') {
                        $insert = [
                            'name' => $v['text'],
                            'file_id' => $console_file_id,
                            'level' => $v['outline_level'] + 1,
                            'createtime' => time(),
                            // 'pid' => 0
                        ];
                        if ($v['outline_level'] == 0) {
                            $insert['pid'] = 0;
                        } else {
                            $insert['pid'] = $lastNode[$v['outline_level'] - 1];
                        }
                        $cate_id = Db::name('ai_file_slice_catalog')->insertGetId($insert);
                        $lastNode[$v['outline_level']] = $cate_id;

                    } else {
                        if ($cate_id) {
                            $file_slice_insert = [
                                'file_id' => $console_file_id,
                                'catalog_id' => $cate_id,
                                'content' => $v['text'],
                                'createtime' => time()
                            ];
                            Db::name('ai_file_slice')->insert($file_slice_insert);
                        }
                    }
                }

                if (!$cate_id) {
                    continue;
                }

                if ($v['type'] == 'table') {
                    $file_slice_insert = [
                        'file_id' => $console_file_id,
                        'catalog_id' => $cate_id,
                        'content' => $v['text'],
                        'createtime' => time()
                    ];
                    Db::name('ai_file_slice')->insert($file_slice_insert);
                }

                if ($v['type'] == 'image') {
                    $savepath = '/uploads/' . date('Ymd') . '/' . md5(uniqid() . time() . mt_rand(1,999999) . time()) . '.' . pathinfo($v['image_url'],PATHINFO_EXTENSION);
                    $r = self::downloadFile($v['image_url'],ROOT_PATH . 'public' . $savepath);
                    
                    if ($r) {
                        $file_slice_insert = [
                            'file_id' => $console_file_id,
                            'catalog_id' => $cate_id,
                            'content' => $host . $savepath,
                            'createtime' => time()
                        ];

                        Db::name('ai_file_slice')->insert($file_slice_insert);
                    }
                }
                
            }

            // die;
            //修改文件目录提取状态完成
            \app\admin\model\ai\ConsoleFile::where("id", $console_file_id)->update(['catalog_status'=>1]);

            //添加图谱表信息
            $insert = [
                'console_id' => \app\admin\model\ai\ConsoleFile::where("id", $console_file_id)->value('console_id'),
                'file_id' => $console_file_id,
                'content' => 'catalog',
                'createtime' => time()
            ];
            Db::name('ai_knowledge_graph')->insert($insert);
            Db::commit();
            
            return ['code' => 1,'msg' => '目录提取成功'];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0,'msg' => $e->getMessage()];
        }

    }




    /*
    * 阿里云解析pdf任务创建
    */
    public static function aliyunAnalyzePdfBuildTask($filepath,$console_file_id)
    {

        // putenv("ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5tEbjZzriAYmPNw25EU2");
        // putenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET=******************************");
        putenv("ALIBABA_CLOUD_ACCESS_KEY_ID=" . self::$aliyunAccessKey);
        putenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET=" . self::$aliyunAccessKeySecret);

        $credConfig = new CredentialConfig([
            'type' => 'access_key',
            'accessKeyId' => getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),
            'accessKeySecret' => getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),
        ]);


        $host = 'https://aimaster.jw100.com.cn';
        $arr = explode('/public/',$filepath);
        $fileUrl = $host . '/' . $arr[1];

        $bearerToken = new Credential();    
        $config = new Config();
        // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
        $config->endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
        // 通过credentials获取配置中的AccessKey ID
        $config->accessKeyId = $bearerToken->getCredential()->getAccessKeyId();
        // 通过credentials获取配置中的AccessKey Secret
        $config->accessKeySecret = $bearerToken->getCredential()->getAccessKeySecret();
        // $config->protocol = "HTTPS";

        $config->type = "access_key";
        $config->regionId = "cn-hangzhou";
        $client = new Docmindapi($config);
        $request = new SubmitDocParserJobRequest();

        $runtime = new RuntimeOptions();
        $runtime->maxIdleConns = 3;
        $runtime->connectTimeout = 10000;
        $runtime->readTimeout = 10000;
        $runtime->ignoreSSL = true;

        $request->fileName = pathinfo($fileUrl,PATHINFO_BASENAME);
        $request->fileUrl = $fileUrl;
        $request->LlmEnhancement = true; //大模型增强

        try {
            $response = $client->submitDocParserJob($request, $runtime);
            $response = $response->toMap();
            if ($response['statusCode'] != 200) {
                throw new \Exception($response['message']);
            }
            $order_id = $response['body']['Data']['Id'];
            $insert = [
                'file_id' => $console_file_id,
                'ali_order_id' => $order_id,
                'status' => 1,
                'createtime' => time()
            ];


            Db::name('ai_file_slice_task')->where('file_id',$console_file_id)->delete();
            Db::name('ai_file_slice_task')->insert($insert);

            return ['code' => 1,'msg' => $response];
        } catch (TeaUnableRetryError $e) {
            return ['code' => 0,'msg' => $e->getMessage()];
        }
                


    }

    /*
    * 阿里云解析pdf状态查询
    */
    public static function getAliyunAnalyzePdfStatus($order_id)
    {
        // putenv("ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5tEbjZzriAYmPNw25EU2");
        // putenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET=******************************");
        putenv("ALIBABA_CLOUD_ACCESS_KEY_ID=" . self::$aliyunAccessKey);
        putenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET=" . self::$aliyunAccessKeySecret);

        $credConfig = new CredentialConfig([
            'type' => 'access_key',
            'accessKeyId' => getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),
            'accessKeySecret' => getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),
        ]);
        // 使用默认凭证初始化Credentials Client。
        $bearerToken = new Credential();    
        $config = new Config();
        // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
        $config->endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
        // 通过credentials获取配置中的AccessKey ID
        $config->accessKeyId = $bearerToken->getCredential()->getAccessKeyId();
        // 通过credentials获取配置中的AccessKey Secret
        $config->accessKeySecret = $bearerToken->getCredential()->getAccessKeySecret();
        $config->type = "access_key";
        $config->regionId = "cn-hangzhou";
        $client = new Docmindapi($config);
        $request = new QueryDocParserStatusRequest();   

        $request->id = $order_id;

        $runtime = new RuntimeOptions();
        $runtime->maxIdleConns = 3;
        $runtime->connectTimeout = 10000;
        $runtime->readTimeout = 10000; 
        try {
            $response = $client->queryDocParserStatus($request, $runtime);
            $response = $response->toMap();
            if ($response['statusCode'] != 200) {
                throw new \Exception($response['message']);
            }
            if (isset($response['body']['Data']) && $response['body']['Data']['Status'] == 'success') {
                return true;
            }
            return false;

        } catch (TeaUnableRetryError $e) {
            dump($e->getMessage());
            dump($e->getErrorInfo());
            dump($e->getLastException());
            dump($e->getLastRequest());
        }

    }

    /*
    * 阿里云解析pdf结果查询
    */
    public static function getAliyunAnalyzePdfResult($order_id)
    {
        // putenv("ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5tEbjZzriAYmPNw25EU2");
        // putenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET=******************************");
        putenv("ALIBABA_CLOUD_ACCESS_KEY_ID=" . self::$aliyunAccessKey);
        putenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET=" . self::$aliyunAccessKeySecret);



        $credConfig = new CredentialConfig([
            'type' => 'access_key',
            'accessKeyId' => getenv('ALIBABA_CLOUD_ACCESS_KEY_ID'),
            'accessKeySecret' => getenv('ALIBABA_CLOUD_ACCESS_KEY_SECRET'),
        ]);
        // 使用默认凭证初始化Credentials Client。
        $bearerToken = new Credential();    
        $config = new Config();
        // 访问的域名，支持ipv4和ipv6两种方式，ipv6请使用docmind-api-dualstack.cn-hangzhou.aliyuncs.com
        $config->endpoint = "docmind-api.cn-hangzhou.aliyuncs.com";
        // 通过credentials获取配置中的AccessKey ID
        $config->accessKeyId = $bearerToken->getCredential()->getAccessKeyId();
        // 通过credentials获取配置中的AccessKey Secret
        $config->accessKeySecret = $bearerToken->getCredential()->getAccessKeySecret();
        $config->type = "access_key";
        $config->regionId = "cn-hangzhou";
        $client = new Docmindapi($config);
        $request = new GetDocParserResultRequest();   

        $request->id = $order_id;
        $request->layoutStepSize = 3000;
        $request->layoutNum = 0;

        $runtime = new RuntimeOptions();
        $runtime->maxIdleConns = 3;
        $runtime->connectTimeout = 10000;
        $runtime->readTimeout = 10000; 

        try {
            $response = $client->getDocParserResult($request, $runtime);
            $response = $response->toMap();
            if ($response['statusCode'] != 200) {
                throw new \Exception($response['message']);
            }

            return $response['body'];

        } catch (TeaUnableRetryError $e) {
            dump($e->getMessage());
            dump($e->getErrorInfo());
            dump($e->getLastException());
            dump($e->getLastRequest());
        }

    }



    public static function aliSaveRequestData($list, $console_file_id)
    {
        try {
            Db::startTrans();
            Db::name('ai_file_slice')->where('file_id',$console_file_id)->delete();
            Db::name('ai_file_slice_catalog')->where('file_id',$console_file_id)->delete();
            $cate_id = '';
            $lastNode = [];
            foreach ($list as $v) {

                //不存在level的说明和文本框架内容关系不大
                if ($v['type'] == 'title' && isset($v['level'])) {
                    // $level = ($v['level'] ?? 0) + 1;
                    $level = $v['level'] + 1;
                    $insert = [
                        'name' => $v['text'],
                        'file_id' => $console_file_id,
                        'level' => $level,
                        'createtime' => time(),
                    ];

                    if ($level == 1) {
                        $insert['pid'] = 0;
                    } else {
                        $insert['pid'] = isset($lastNode[$level - 1]) ? $lastNode[$level - 1] : 0;
                    }
                    $cate_id = Db::name('ai_file_slice_catalog')->insertGetId($insert);
                    $lastNode[$level] = $cate_id;

                } else {

                    if ($cate_id) {
                        $file_slice_insert = [
                            'file_id' => $console_file_id,
                            'catalog_id' => $cate_id,
                            'content' => $v['text'],
                            'createtime' => time()
                        ];
                        Db::name('ai_file_slice')->insert($file_slice_insert);
                    }
                }
            }

            //修改文件目录提取状态完成
            \app\admin\model\ai\ConsoleFile::where("id", $console_file_id)->update(['catalog_status'=>1]);
            //修改alibaba任务进程状态完成
            Db::name('ai_file_slice_task')->where('file_id',$console_file_id)->update([
                'status'=>2,
                'origin_data' => json_encode($list,JSON_UNESCAPED_UNICODE)
            ]);

            //添加图谱表信息
            $insert = [
                'console_id' => \app\admin\model\ai\ConsoleFile::where("id", $console_file_id)->value('console_id'),
                'file_id' => $console_file_id,
                'content' => 'catalog',
                'createtime' => time()
            ];
            Db::name('ai_knowledge_graph')->insert($insert);
            Db::commit();
            
            echo 'success';
        } catch (\Exception $e) {
            echo 'failed';
            Db::rollback();
            error_log(date("Y-m-d H:i:s")."|文件ID: {$console_file_id} {$e->getMessage()}\r\n", 3,
                         ROOT_PATH."/runtime/log/".date("Ym")."/aliBuildGraph.log");
        }
    }



    public static function parseMarkdownImages($markdown) {
        $pattern = '/!\[(.*?)\]\((.*?)\)/';
        preg_match_all($pattern, $markdown, $matches, PREG_SET_ORDER);
        
        $images = [];
        foreach ($matches as $match) {
            $images[] = [
                'alt' => $match[1],
                'url' => $match[2]
            ];
        }
        
        return $images;
    }


    protected static function requestClient(string $url, string $method, array $data = [], array $clientHeader = [], int $timeout = 10)
    {
        $headers = [];
        foreach ($clientHeader as $key => $item) {
            $headers[] = $key . ':' . $item;
        }
        $curl = curl_init($url);
        //请求方式
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
        //post请求
        if (!empty($data['body'])) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data['body']);
        } else if (!empty($data['json'])) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data['json']));
        } else {
            // $curlFn = $this->curlFn;
            // foreach ($curlFn as $item) {
            //     if ($item instanceof \Closure) {
            //         $curlFn($curl);
            //     }
            // }
        }
        //超时时间
        curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
        //设置header头
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        //返回抓取数据
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        //输出header头信息
        curl_setopt($curl, CURLOPT_HEADER, true);
        //TRUE 时追踪句柄的请求字符串，从 PHP 5.1.3 开始可用。这个很关键，就是允许你查看请求header
        curl_setopt($curl, CURLINFO_HEADER_OUT, true);
        //https请求
        if (1 == strpos("$" . $url, "https://")) {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        }
        [$content, $status] = [curl_exec($curl), curl_getinfo($curl)];
        $content = trim(substr($content, $status['header_size']));
        
        return json_decode($content, true);
        
    }


    /*
    * 下载文件到本地
    */
    public static function downloadFile($remoteFile,$localFile)
    {
        
        if(file_exists($localFile)) {
            return;
        }
        
        $localDir = dirname($localFile);

        if(!is_dir($localDir)) {
            mkdir($localDir,0755,true);
        }

        $hander = curl_init();
        $fp = fopen($localFile,'wb');
        curl_setopt($hander,CURLOPT_URL,$remoteFile);
        curl_setopt($hander,CURLOPT_FILE,$fp);
        curl_setopt($hander,CURLOPT_HEADER,0);
        curl_setopt($hander,CURLOPT_FOLLOWLOCATION,1);
        curl_setopt($hander,CURLOPT_TIMEOUT,60);
        if(strpos($remoteFile,'https://') == 0) {
            curl_setopt($hander, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($hander, CURLOPT_SSL_VERIFYHOST, false);
        }
        $res = curl_exec($hander);
        curl_close($hander);
        fclose($fp);
        return $res;
    }
    







}