<?php

namespace app\common\library\aliyun;

use think\Exception;

class Embeddings
{
    private $apiKey = "sk-613ccddd0b0948179c815a0e11cd8ebf";
    private $baseUrl = "https://dashscope.aliyuncs.com/compatible-mode/v1";
    private $client;

    public function __construct()
    {
    }

    /**
     * 调用 DashScope embeddings 接口生成向量
     *
     * @param array $input 输入文本数组
     * @param string $model 模型名称，默认为 text-embedding-v3
     * @param int $dimensions 向量维度，默认为 1024
     * @param string $encodingFormat 编码格式，默认 float
     * @return array
     * @throws Exception
     */
    public function create(array $input, $model = 'text-embedding-v3', $dimensions = 1024, $encodingFormat = 'float')
    {
        $url = $this->baseUrl . '/embeddings';

        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json'
        ];

        $data = [
            'model' => $model,
            'input' => $input,
            'dimensions' => $dimensions,
            'encoding_format' => $encodingFormat
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 测试环境可临时关闭SSL验证

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            throw new Exception('Curl error: ' . curl_error($ch));
        }
        curl_close($ch);

        if ($httpCode != 200) {
            throw new Exception("API 请求失败，HTTP 状态码：{$httpCode}，响应内容：{$response}");
        }

        return json_decode($response, true);
    }

    /**
     * 供外部调用的运行方法
     *
     * @param string|array $input 输入文本或数组
     * @return string JSON 格式的模型输出结果
     * @throws Exception
     */
    public function run($input)
    {
        if (!is_array($input)) {
            $input = [$input];
        }

        $result = $this->create($input);
        return json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
}