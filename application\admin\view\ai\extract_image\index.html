<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('ai/extract_image/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('ai/extract_image/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('ai/extract_image/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>

                        

                        <a class="btn btn-success btn-recyclebin btn-dialog {:$auth->check('ai/extract_image/recyclebin')?'':'hide'}" href="ai/extract_image/recyclebin" title="{:__('Recycle bin')}"><i class="fa fa-recycle"></i> {:__('Recycle bin')}</a>
                        
                        <a class="btn btn-info btn-disabled disabled btn-selected import_card" href="javascript:;"><i class="fa fa-download"></i> 导入卡片</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('ai/extract_image/edit')}"
                           data-operate-del="{:$auth->check('ai/extract_image/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
<script id="typetpl" type="text/html">
    <div class="row">
        <div class="col-xs-12">
            <select name="robot" class="form-control">
                <option value="">{:__('请选择')}</option>
                {foreach name="projectList" id="item"}
                <option value="{$item.id}">{$item.name}</option>
                {/foreach}
            </select>
        </div>
    </div>
</script>
