<?php

namespace app\common\service\ai\engine\strategy;

use app\admin\model\ai\ConsoleFile;
use app\admin\model\ai\UrlData;

/**
 * 增强策略类 - 对应原 Localenhance.php 的功能
 */
class EnhanceStrategy extends BasicStrategy
{
    /**
     * 构造引用内容 - 增强版本
     */
    public function buildReferenceContent($project, $msg, $engine)
    {
        $vectorStr = '';
        $vectorId = 1;
        $vectorList = $this->queryVector($project, $msg);
        $vectorScore = $project['vector_score']>0 ? $project['vector_score'] : 0.3;
        $list = [];
        
        foreach ($vectorList as &$item) {
            if ((isset($item['cosine_similarity']) && $item['cosine_similarity'] > $vectorScore) || 
                (isset($item['relevance_score']) && $item['relevance_score'] > $vectorScore)) {
                $consoleFile = ConsoleFile::where('id', $item['file_id'])->find();
                if($consoleFile)
                {
                    $file_number = $consoleFile['file_number'] ?? 0;
                    if(!empty($consoleFile['type']) && $consoleFile['type'] == 5){
                        $urlData = UrlData::where('file_number', $file_number)->find();
                        $fileName = !empty($urlData['title']) ? $urlData['title'] : $consoleFile['name'];
                        $filePath = !empty($urlData['url']) ? $urlData['url'] : cdnurl($consoleFile['file'], true);
                    }else{
                        $fileName = !empty($consoleFile['name']) ? $consoleFile['name'] : '';
                        $filePath = !empty($consoleFile['file'])? cdnurl($consoleFile['file'], true) : '';
                    }
                    $item['title'] = $fileName;
                    $item['file'] = $filePath;
                    $item['file_id'] = $item['file_id'];
                    $item['file_number'] = $file_number;
                    $list[] = $item;
                }
            }
        }
        
        $query = $engine->pg_connect();
        // 尝试获取相邻切片以恢复完整内容
        if($list)
        {
            $list = $this->retrieveAdjacentSlices($list, $query, $project);
        
            // 按相似度排序
            $list = $this->reorderReferences($list);
            $vectorNum = $project['vector_num'] > 0 ? $project['vector_num'] : 6;
            $list = array_slice($list, 0, $vectorNum);

            $mergedReferences = [];

            foreach ($list as $reference) {
                $fileId = $reference['file_id'];

                if (isset($mergedReferences[$fileId])) {
                    // 如果已存在相同的 file_id，则合并 content
                    $mergedReferences[$fileId]['content'] .= "\n" . $reference['content'];
                } else {
                    // 否则直接存储
                    $mergedReferences[$fileId] = $reference;
                }
            }

            // 转换为索引数组（去除键名）
            $list = array_values($mergedReferences);
            $references = [];

            foreach ($list as $item) {
                $vectorStr .= "[{$vectorId}] " . $item['title'] . "：" . $item['content'] . "\n";
                $slice = $item['content'];
                $references[] = [
                    'title'   => $item['title'],
                    'content' => $slice,
                    'file'    => $item['file'],
                    'cosine_similarity' => $item['cosine_similarity'] ?? 0,
                    'relevance_score' => $item['relevance_score'] ?? 0,
                    'document_id' => $item['file_number'],
                    'file_id' => $item['file_id']
                ];
                $vectorId++;
            }
            
            $engine->setReferences($references);
        }
        
        return $vectorStr;
    }
    
    /**
     * 构造请求payload - 增强版本
     */
    public function buildPayload($messages, $engine)
    {
        return [
            "model"              => $engine->model,
            "messages"           => $messages,
            "stream"             => true,
            "max_tokens"         => 2096,
            "stop"               => ["null"],
            "temperature"        => 0.2,
            "top_p"              => 0.2,
            "top_k"              => 20,
            "frequency_penalty"  => 1,
            "n"                  => 1,
            "response_format"    => ["type" => "text"],
        ];
    }
    
    /**
     * 查询向量库 - 增强版本，支持QA增强
     */
    protected function queryVector($project, $text)
    {
        $vectorNum = 100;
        $query = $this->engine->pg_connect();
        $text = mb_strcut($text, 0, 384);
        $vectorArr = $this->getEmbeddingsNew([$text]);

        $vectorContent = json_encode(['content'=>$vectorArr[0]['embedding']]);
        
        $vectorList = json_decode($vectorContent, true);
        $list = [];
        
        if (!empty($vectorList['content'])) {
            $queryVector = json_encode($vectorList['content']);
            
            // 先从slice_qa表查询相关切片
            $query->query("SET hnsw.ef_search = 50;");
            $qaSlices = $query->query("
                SELECT 
                    vdu.id,
                    vdu.file_id,
                    vdu.content,
                    1 - (slice_qa.embedding <=> '{$queryVector}') AS cosine_similarity,
                    slice_qa.extracted_question,
                    slice_qa.extracted_answer
                FROM slice_qa
                JOIN vector_data_upgrades vdu ON vdu.id = slice_qa.slice_id
                WHERE vdu.console_id IN ({$project['console_ids']})
                AND vdu.delete_time IS NULL
                ORDER BY slice_qa.embedding <=> '{$queryVector}'
                LIMIT 50;
            ");

            // 如果qa结果不足，补充原始切片
            if (count($qaSlices) < $vectorNum) {
                $remainingNum = $vectorNum - count($qaSlices);
                $existingIds = array_column($qaSlices, 'id');
                $excludeIds = empty($existingIds) ? "0" : implode(',', $existingIds);
                $query->query("SET hnsw.ef_search = 100;");
                $regularSlices = $query->query("
                    SELECT 
                        v.id,
                        v.file_id,
                        v.content,
                        1 - (v.embedding <=> '{$queryVector}') AS cosine_similarity,
                        NULL AS extracted_question,
                        NULL AS extracted_answer
                    FROM vector_data_upgrades v
                    LEFT JOIN (
                        SELECT unnest(array[{$excludeIds}]) AS id_to_exclude
                    ) ex ON v.id = ex.id_to_exclude
                    WHERE v.console_id IN ({$project['console_ids']})
                        AND ex.id_to_exclude IS NULL
                        AND v.delete_time IS NULL
                    ORDER BY v.embedding <=> '{$queryVector}'
                    LIMIT {$remainingNum};
                ");

                // 合并结果
                $list = array_merge($qaSlices, $regularSlices);
            } else {
                $list = $qaSlices;
            }
            
            // 记录日志
            if ($this->engine->getDebug()) {
                $logData = [
                    'query' => $text,
                    'vector' => $queryVector,
                    'results' => $list,
                    'time' => date('Y-m-d H:i:s')
                ];
                error_log(
                    date("Y-m-d H:i:s") . "|vector_query:" . print_r($logData, true) . "\r\n",
                    3,
                    ROOT_PATH . "/runtime/log/" . date("Ym") . "/vector_query.log"
                );
            }
        }

        return $this->processResults($list);
    }
    
    /**
     * 获取相邻切片以恢复完整内容
     */
    protected function retrieveAdjacentSlices($slices, $query, $project)
    {
        $enhancedSlices = [];
        $processedIds = [];
        
        foreach ($slices as $slice) {
            // 如果已处理过该切片，跳过
            if (in_array($slice['id'], $processedIds)) {
                continue;
            }
            
            $processedIds[] = $slice['id'];
            
            // 检查是否包含关键词，表明内容可能被截断
            $isIncomplete = $this->isIncompleteContent($slice['content']);
            
            // 如果内容看起来不完整或包含步骤关键词，尝试获取相邻切片
            if ($isIncomplete || $this->containsStepKeywords($slice['content'])) {
                // 直接查询相邻ID的切片（前后各2个）
                $adjacentSlices = $query->query("
                    SELECT id, content, file_id
                    FROM vector_data_upgrades
                    WHERE file_id = {$slice['file_id']}
                    AND console_id IN ({$project['console_ids']})
                    AND delete_time IS NULL
                    AND id BETWEEN {$slice['id']} - 1 AND {$slice['id']} + 1
                    ORDER BY id ASC
                ");
                
                // 如果找不到相邻切片，使用原始切片
                if (empty($adjacentSlices)) {
                    $enhancedSlices[] = $slice;
                    continue;
                }
                
                // 合并相邻切片内容
                $combinedContent = '';
                foreach ($adjacentSlices as $adjacentSlice) {
                    $combinedContent .= $adjacentSlice['content'] . "\n";
                    // 将处理过的切片ID添加到已处理列表
                    $processedIds[] = $adjacentSlice['id'];
                }
                
                // 创建新的合并切片
                $newSlice = $slice;
                $newSlice['content'] = $this->engine->cleanupCombinedContent($combinedContent);
                $newSlice['is_combined'] = true;
                
                $enhancedSlices[] = $newSlice;
            } else {
                $enhancedSlices[] = $slice;
            }
        }
        
        return $enhancedSlices;
    }
    
    /**
     * 判断内容是否不完整
     */
    protected function isIncompleteContent($content)
    {
        // 检查内容是否以数字+括号结尾，表示可能被截断的步骤
        if (preg_match('/\d+[\)）]\s*$/', $content)) {
            return true;
        }
        
        // 检查内容是否以不完整的句子结尾
        if (preg_match('/[,;，；]\s*$/', $content)) {
            return true;
        }
        
        // 检查是否以数字结尾，可能是被截断的编号
        if (preg_match('/\d+\s*$/', $content)) {
            return true;
        }
        
        // 检查是否包含"步骤"、"开车"、"停车"等关键词，但内容较短
        if ($this->containsStepKeywords($content) && mb_strlen($content) < 300) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查内容是否包含步骤相关关键词
     */
    protected function containsStepKeywords($content)
    {
        $keywords = ['步骤', '开车', '停车', '操作', '流程', '顺序', '方法', '程序', '启动', '关闭'];
        foreach ($keywords as $keyword) {
            if (strpos($content, $keyword) !== false) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 处理查询结果
     */
    protected function processResults($results)
    {
        foreach ($results as &$result) {
            // 如果存在QA信息，增强内容
            if (!empty($result['extracted_question']) && !empty($result['extracted_answer'])) {
                $result['content'] = sprintf(
                    "问题：%s\n回答：%s\n原文：%s",
                    $result['extracted_question'],
                    $result['extracted_answer'],
                    $result['content']
                );
            }
            
            // 清理不需要的字段
            unset($result['extracted_question'], $result['extracted_answer']);
        }
        return $results;
    }
}
