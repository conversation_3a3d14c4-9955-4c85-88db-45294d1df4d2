<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use app\common\service\ai\AiService;
use think\Queue;

/**
 * API-用户卡片管理
 */
class Card extends Api
{
    // protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     * UserCard模型对象
     * @var \app\admin\model\ai\UserCard
     */
    protected $model = null;
    protected $embeddings = null;

    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\UserCard;
        $this->embeddings = new \app\common\library\Embeddings;
    }
    

    /**
     * 列表
     */
    public function index()
    {
        $limit = $this->request->post('limit') ?? 20;
        $page = $this->request->post('page') ?? 1;
        $this->request->get([config('paginate.var_page') => $page]);
        $sort = 'id';
        $order = 'DESC';
        // $consoleList = \app\admin\model\ai\Console::where('FIND_IN_SET('.$this->auth->id.',user_id)')->column('id');
        // $project_id = \app\admin\model\ai\Project::where(['console_id'=>['in',$consoleList]])->value('id');
        $console_id = $this->request->post('console_id');
        $where = [];
        if($console_id>0){
            $where = ['console_id'=>['in',[$console_id]]];
        }else{
            $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->find();
            $consoleArr = isset($project['console_ids']) ? explode(',', $project['console_ids']) : [];
            $where = ['console_id'=>['in', $consoleArr]];
        }
        $where['user_card.type'] = $this->request->post('type')==1?1:2;
        if($this->request->post('name')){
            $where['user_card.name'] = ['like',"%{$this->request->post('name')}%"];
        }
        $list = $this->model
                ->with(['user','project'/*,'usercardext'*/])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
        foreach ($list as $row) {
            $row->visible(['id','type','name','file','cover','status','updatetime','tag']);
            $row->visible(['user']);
            $row->getRelation('user')->visible(['nickname']);
            $row->visible(['project']);
            $row->getRelation('project')->visible(['name']);
            $row['file'] = cdnurl($row['file'], true);
            $row['updatetime'] = date("Y-m-d H:i",$row['updatetime']);
            $row['tag'] = \app\admin\model\ai\UserCardExt::where('card_id', $row['id'])->column('name');
            
            if(strpos($row['cover'], 'http')===false){
                $row['cover'] = cdnurl($row['cover'], true);
            }
        }

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
        
    }

    
    /**
     * 公司上传-列表
     */
    public function company_index()
    {
        $sort = 'id';
        $order = 'DESC';
        $where = [];
        $page = $this->request->post('page') ?? 1;
        $limit = $this->request->post('limit') ?? 20;

        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $where['robot'] = $project_id;
        $console_id = $this->request->post('console_id');
        if($console_id>0){
            $where['console_id'] = $console_id;
        }
        $type = $this->request->post('type') == 1 ? 1 : 2;
        if($type==2){

            //重庆供销员项目视频搜索单独处理
            if ($console_id == 9) {
                $where['a'] = ['like',['%task_info%','%mp4%'],'or'];
            } else {
                $where['a'] = ['like',"%mp4%"];
            }

        }else{
            //重庆供销员项目图片搜索单独处理
            if ($console_id == 9) {
                $where['a'] = ['not like',['%task_info%','%mp4%','%vrmice%','%trainingadmin%','%com.cn/tour%']];
            } else {
                $where['a'] = ['not like',"%mp4%"];
            }
            
            // $where['a'] = ['like',["%jpg%",'%png%'],'or'];
        }
        if($this->request->post('name')){
            $where['q'] = ['like',"%{$this->request->post('name')}%"];
        }
        $Card = new \app\admin\model\ai\Card;



        $list = $Card
                // ->with(['project'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
        foreach ($list as &$row) {
            $row->visible(['id','status','updatetime','file','image','name']);
            // $row->visible(['project']);
            // $row->getRelation('project')->visible(['name']);
            preg_match('/\[\!\[(.*?)\]\((.*?)\)\]\((.*?)\)/',$row['a'],$match);
            $row['file'] = $match[3]??'';
            $row['image'] = $match[2]??'';
            $row['cover'] = $row['image'];
            $row['name'] = $row['q']??'';
            $row['updatetime'] = $row['updatetime']?date("Y-m-d H:i",$row['updatetime']):date("Y-m-d H:i",$row['createtime']);
        }

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
        
    }

    /**
     * 创建
     */
    public function create(){
        $console_id = $this->request->post('console_id');
        $type = $this->request->post('type');
        $name = $this->request->post('name');
        $tag = $this->request->post('tag/a');
        $file = $this->request->post('file');
        $duration = '';
        if(strpos($file,'.mp4')!==false)
        {
            $fileNew = str_replace(request()->domain(),'',$file);
            $cover = str_replace('.mp4','.jpg',$fileNew);
            $this->extractVideoCover(ROOT_PATH ."public" . $fileNew, ROOT_PATH ."public" . $cover);
            $duration = $this->getVideoDuration(ROOT_PATH ."public" . $fileNew);
        }else{
            $cover = $file;
        }
        $result = false;
        Db::startTrans();
        try {
            $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
            $params['robot'] = $project_id;
            $params['console_id'] = $console_id;
            $params['type'] = $type;
            $params['cover'] = $cover;
            $params['file'] = $file;
            $params['name'] = $name;
            $params['createtime'] = time();
            $params['updatetime'] = time();
            $params['status'] = 1;
            $params['user_id'] = $this->auth->id;
            $file_path = str_replace(request()->domain(),'',$file);
            $params['file_size'] = filesize(ROOT_PATH ."public" . $file_path);
            $params['duration'] = $duration;
            $result = $this->model->allowField(true)->insertGetId($params);
            $insert = [];
            foreach($tag as $item){
                $newData = $this->embeddings->getEmbeddingsNew([$item]);
                $vectorization = base64_encode(json_encode($newData[0]));
                $insert[] = [
                    'card_id' => $result,
                    'name' => $item,
                    'vectorization' => $vectorization
                ];
            }
            \app\admin\model\ai\UserCardExt::insertAll($insert);
            Db::commit();

            //推入训练队列中
            Queue::push('app\queue\job\CardToPGsql', [
                'type' => 3
            ],'card_to_pgsql');
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        
        $this->success("创建成功!");
    }
    
    /**
     * 提取视频第一帧并保存为图片
     */
    function extractVideoCover($videoPath, $outputImagePath) {
        // FFmpeg命令，提取视频第一帧并保存为图片
        $command = "ffmpeg -i '{$videoPath}' -ss 00:00:01.000 -vframes 1 '{$outputImagePath}'";
        // 使用shell_exec执行命令
        $output = shell_exec($command);
    
        // 检查命令执行是否成功
        // if ($output === null) {
        //     throw new Exception("Failed to extract video cover.");
        // }
    
        return true; // 成功
    }

    function getVideoDuration($videoFilePath) {
        // 构建FFmpeg命令，用于获取视频时长
        $command = "ffmpeg -i {$videoFilePath} 2>&1 | grep 'Duration' | cut -d ' ' -f 4 | sed s/,//";
        
        // 使用shell_exec执行命令并捕获输出
        $output = shell_exec($command);
        
        // 解析输出的时长字符串，格式通常是 00:00:00.00，转换为秒
        if ($output) {
            list($hours, $minutes, $seconds) = explode(":", $output);
            $durationInSeconds = intval($hours * 3600) + intval($minutes * 60) + intval($seconds);
            return $durationInSeconds;
        } else {
            // 如果无法获取时长，返回null或其他错误处理
            return null;
        }
    }
    

    /**
     * 修改
     */
    public function edit(){
        $id = $this->request->post('id');
        $name = $this->request->post('name');
        $console_id = $this->request->post('console_id');
        $tag = $this->request->post('tag/a');
        $file = $this->request->post('file');

        $row = $this->model->get($id);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $result = false;
        Db::startTrans();
        try {
            $duration = '';
            if(strpos($file,'.mp4')!==false)
            {
                $fileNew = str_replace(request()->domain(),'',$file);
                $cover = str_replace('.mp4','.jpg',$fileNew);
                $this->extractVideoCover(ROOT_PATH ."public" . $fileNew, ROOT_PATH ."public" . $cover);
                $duration = $this->getVideoDuration(ROOT_PATH ."public" . $fileNew);
            }else{
                $cover = $file;
            }
            $file_path = str_replace(request()->domain(),'',$file);
            $file_size = filesize(ROOT_PATH ."public" . $file_path);
            $result = $row->allowField(true)->save(['name'=>$name,'cover'=>$cover,'file'=>$file,'duration'=>$duration,'file_size'=>$file_size]);

            $insert = [];
            foreach($tag as $item){
                $newData = $this->embeddings->getEmbeddingsNew([$item]);
                $vectorization = base64_encode(json_encode($newData[0]));
                $insert[] = [
                    'card_id' => $id,
                    'name' => $item,
                    'vectorization' => $vectorization
                ];
            }
            \app\admin\model\ai\UserCardExt::where('card_id', $id)->delete();
            \app\admin\model\ai\UserCardExt::insertAll($insert);

            Db::commit();

            //推入训练队列中
            Queue::push('app\queue\job\CardToPGsql', [
                'type' => 3
            ],'card_to_pgsql');
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            $aiEngine = AiService::getEngine('local');
            $aiEngine->deleteVectorData(3,$ids);
            foreach ($list as $item) {
                $count += $item->delete();
            }
            // \app\admin\model\ai\UserCardExt::where(['card_id'=>['in',explode(',',$ids)]])->delete();
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }
}
