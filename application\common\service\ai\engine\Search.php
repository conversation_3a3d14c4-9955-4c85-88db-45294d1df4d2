<?php 
namespace app\common\service\ai\engine;

use think\Db;
use app\common\service\ai\BaseEngine;
use app\common\service\IpService;

class Search extends BaseEngine {
    private $references = [];
    private $save_references = [];
    private $paramsType = false;
    private $lastMsgId;

    public function chat($model, $prompt, $contextId, $robot, $card, $lang) {
        $url = 'https://qianfan.baidubce.com/v2/ai_search/chat/completions';
        $headers = [
            'X-Appbuilder-Authorization: Bearer bce-v3/ALTAK-pOqGHzwqnBMcNPvDRdnkR/5222812ac06fa97ab2a1f236f08d03d1f85d46b5',
            'Content-Type: application/json'
        ];
        if ($lang && $lang != 'cn') {
			$prompt .= " 请只用英文回答。不要重复问题。提供一个直接的答案。";
		}
		$project = $this->getProjectInfo($robot);
        $prompt .= "系统提示词如下：".$project['system_prompt'] ?? '';
        $data = [
            "messages" => [
                ["content" => $prompt, "role" => "user"]
            ],
            "stream" => true,
            "model" => $model,
            "enable_deep_search" => false,
            "enable_followup_query" => false,
            "resource_type_filter" => [["type" => "web", "top_k" => 10]]
        ];
        
        $ip = request()->ip();
        $area = IpService::getArea($ip);
        $answerStr = '';
		$this->saveChatRecord($robot, $contextId, $prompt, $ip, $area, $card, $lang, $answerStr, $model);
        $jsonBuffer = '';  // 用于缓存数据，可能包含不完整的 JSON 片段

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($curl, $data) use (
            $contextId, $robot, $prompt, $ip, $area, $card, $lang, $model, 
            &$answerStr, &$jsonBuffer
        ) {
            // 可调试输出：dump($data);
            // 将新接收到的数据追加到缓存中
            $jsonBuffer .= $data;

            // 利用 "data:" 分割各个 JSON 对象（前缀可能会重复出现）
            $parts = preg_split('/\bdata:\s*/', $jsonBuffer);

            // 将最后一段（可能是不完整的）保存到缓冲区
            $jsonBuffer = array_pop($parts);

            // 处理所有完整的 JSON 段
            foreach ($parts as $part) {
                $part = trim($part);
                if (empty($part)) {
                    continue;
                }
                // 简单判断是否完整：检查末尾是否为 "}"
                if (substr($part, -1) !== '}') {
                    // 不完整则放回缓冲区，下次继续拼接处理
                    $jsonBuffer = $part . $jsonBuffer;
                    continue;
                }
                $decoded = json_decode($part, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    // 若解析失败，则将该段数据保留在缓冲区，等待后续数据补全
                    $jsonBuffer = $part . $jsonBuffer;
                    continue;
                }
                // 检查是否有结束标志，若有则记录日志并退出
                if (isset($decoded['choices'][0]['finish_reason']) && $decoded['choices'][0]['finish_reason'] === 'stop') {
                    $this->processDecoded($decoded, $contextId, $lang, $answerStr, $robot, $prompt, $ip, $area, $card, $model);
                    // 清空缓存后记录日志
                    $jsonBuffer = '';
                    exit;
                }
                // 正常处理解析出的 JSON 数据
                $this->processDecoded($decoded, $contextId, $lang, $answerStr, $robot, $prompt, $ip, $area, $card, $model);
            }

            // 在每次数据写入后，检查缓冲区是否包含完整的 JSON
            if (!empty($jsonBuffer)) {
                $decoded = json_decode($jsonBuffer, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    // 如果缓冲区数据能正确解析，则处理之
                    if (isset($decoded['choices'][0]['finish_reason']) && $decoded['choices'][0]['finish_reason'] === 'stop') {
                        $this->processDecoded($decoded, $contextId, $lang, $answerStr, $robot, $prompt, $ip, $area, $card, $model);
                        $jsonBuffer = '';
                        exit;
                    }
                    $this->processDecoded($decoded, $contextId, $lang, $answerStr, $robot, $prompt, $ip, $area, $card, $model);
                    $jsonBuffer = '';
                }
            }
            return strlen($data);
        });

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Curl error: ' . curl_error($ch);
        }
        curl_close($ch);

        return json_decode($response, true);
    }

	/**
	 * 获取项目信息
	 */
	private function getProjectInfo($robot)
	{
		return Db::name('ai_project')->where('id', $robot)->find();
	}

    /**
     * 处理解析后的 JSON 数据，提取文本、发送消息，并在 finish_reason 为 stop 时记录日志
     */
    private function processDecoded($decoded, $contextId, $lang, &$answerStr, $robot, $prompt, $ip, $area, $card, $model) {
        if ($this->paramsType) {
            $text = $decoded['output']['choices'][0]['message']['content'] ?? '';
        } else {
            $text = $decoded['choices'][0]['delta']['content'] ?? '';
        }
        $reasoning_content = $decoded['choices'][0]['delta']['reasoning_content'] ?? '';
        $this->references = $decoded['references'] ?? [];

        if ($lang && $lang != 'cn' && trim($text) !== '\n') {
            $this->translateReferences($lang);
        }
        $text = str_replace(["    ", "~"], ["", "—"], $text);
        if (!empty($this->references)) {
            $this->save_references = $this->references;
        }
        $responseJson = json_encode([
            'text' => $text,
            'reasoning_content' => $reasoning_content,
            'references' => $this->references,
            'msg_id' => $this->lastMsgId
        ], JSON_UNESCAPED_UNICODE);
        $answerStr .= $text;
        $this->sendMessage($contextId, $responseJson);

        // 如果 finish_reason 为 stop，则记录问答日志
        if (isset($decoded['choices'][0]['finish_reason']) && $decoded['choices'][0]['finish_reason'] === 'stop') {
            $this->saveChatRecord($robot, $contextId, $prompt, $ip, $area, $card, $lang, $answerStr, $model);
        }
    }

    /**
     * 记录问答日志
     */
    private function saveChatRecord($robot, $contextId, $prompt, $ip, $area, $card, $lang, $answerStr, $model) {
        $referencesList = base64_encode(json_encode($this->save_references ?? []));
        $msgShort = mb_substr($prompt, 0, 150);
        $answerStr = preg_replace('/[\x{1F600}-\x{1F64F}|\x{1F300}-\x{1F5FF}|\x{1F680}-\x{1F6FF}|\x{2600}-\x{26FF}|\x{2700}-\x{27BF}]/u', '', $answerStr);
        
        // 确保 card 是整数
        $cardValue = is_numeric($card) ? intval($card) : 0;

        if($this->lastMsgId) {
            Db::name('ai_msg')->where('id', $this->lastMsgId)->update([
                'content' => $answerStr,
                'referrer'=> $referencesList,
            ]);
            \fast\Http::sendAsyncRequest(request()->domain() . '/api/ai/card/tagExtractionOne', [
                'msg' => $prompt,
                'robot' => $robot,
                'msg_id' => $this->lastMsgId ?? 0
            ]);
        } else {
            $this->lastMsgId = Db::name('ai_msg')->insertGetId([
                'robot'   => $robot,
                'chatId'  => $contextId,
                'msg'     => $msgShort,
                'time'    => time(),
                'ip'      => $ip,
                'content' => $answerStr,
                'city'    => $area,
                'referrer'=> $referencesList,
                'card'    => $cardValue,  // 使用转换后的整数值
                'lang'    => $lang,
                'model'   => $model
            ]);
        }
    }

    /**
     * 翻译引用信息
     */
    private function translateReferences($lang) {
        $Translation = new \app\common\library\Translation();
        foreach ($this->references as $rkey => &$rvalue) {
            $rvalue['title'] = $Translation->xfyun($rvalue['title'], 'cn', $lang);
            $rvalue['content'] = $Translation->xfyun($rvalue['content'], 'cn', $lang);
        }
    }
}
