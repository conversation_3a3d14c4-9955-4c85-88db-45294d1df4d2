# Local引擎整合迁移指南

## 概述

本次重构将原来的三个独立文件（Local.php、Localenhance.php、Localprofessional.php）整合为一个统一的引擎架构，通过策略模式实现不同功能的支持。

## 新架构说明

### 核心文件结构
```
application/common/service/ai/engine/
├── LocalEngine.php                    # 统一的本地引擎类
├── LocalEngineFactory.php             # 工厂类，用于创建不同策略的引擎
└── strategy/
    ├── StrategyInterface.php           # 策略接口
    ├── BasicStrategy.php               # 基础策略（对应原Local.php）
    ├── EnhanceStrategy.php             # 增强策略（对应原Localenhance.php）
    └── ProfessionalStrategy.php        # 专业策略（对应原Localprofessional.php）
```

### 策略模式说明

1. **BasicStrategy（基础策略）**
   - 对应原 `Local.php` 功能
   - 基础的向量检索和引用构建
   - 标准的模型调用参数

2. **EnhanceStrategy（增强策略）**
   - 对应原 `Localenhance.php` 功能
   - 支持QA增强检索
   - 相邻切片内容恢复
   - 更优化的模型参数

3. **ProfessionalStrategy（专业策略）**
   - 对应原 `Localprofessional.php` 功能
   - 查询增强（Query Augmentation）
   - 术语库支持
   - 标题级向量召回
   - 双路召回+融合

## 迁移步骤

### 1. 代码迁移

#### 原来的使用方式：
```php
// 基础版本
$local = new Local();
$local->chat($console_type, $contextId, $msg, $robot, $card, $lang, $model, $debug, $ext);

// 增强版本
$localenhance = new Localenhance();
$localenhance->chat($console_type, $contextId, $msg, $robot, $card, $lang, $model, $debug, $ext);

// 专业版本
$localprofessional = new Localprofessional();
$localprofessional->chat($console_type, $contextId, $msg, $robot, $card, $lang, $model, $debug, $ext);
```

#### 新的使用方式：

**方式一：直接指定策略类型**
```php
use app\common\service\ai\engine\LocalEngineFactory;

// 基础版本
$engine = LocalEngineFactory::create('basic');
$engine->chat($console_type, $contextId, $msg, $robot, $card, $lang, $model, $debug, $ext);

// 增强版本
$engine = LocalEngineFactory::create('enhance');
$engine->chat($console_type, $contextId, $msg, $robot, $card, $lang, $model, $debug, $ext);

// 专业版本
$engine = LocalEngineFactory::create('professional');
$engine->chat($console_type, $contextId, $msg, $robot, $card, $lang, $model, $debug, $ext);
```

**方式二：根据项目配置自动选择**
```php
use app\common\service\ai\engine\LocalEngineFactory;

// 根据项目配置自动选择合适的策略
$project = Db::name('ai_project')->where('id', $robot)->find();
$engine = LocalEngineFactory::createByProject($project);
$engine->chat($console_type, $contextId, $msg, $robot, $card, $lang, $model, $debug, $ext);
```

**方式三：手动创建和切换策略**
```php
use app\common\service\ai\engine\LocalEngine;

$engine = new LocalEngine();

// 运行时切换策略
$engine->setStrategy(LocalEngine::STRATEGY_BASIC);
$engine->chat($console_type, $contextId, $msg, $robot, $card, $lang, $model, $debug, $ext);

$engine->setStrategy(LocalEngine::STRATEGY_PROFESSIONAL);
$engine->chat($console_type, $contextId, $msg, $robot, $card, $lang, $model, $debug, $ext);
```

### 2. 控制器修改示例

#### 原来的控制器代码：
```php
public function chat()
{
    // ... 参数获取 ...
    
    if ($enhanceType == 'professional') {
        $ai = new \app\common\service\ai\engine\Localprofessional();
    } elseif ($enhanceType == 'enhance') {
        $ai = new \app\common\service\ai\engine\Localenhance();
    } else {
        $ai = new \app\common\service\ai\engine\Local();
    }
    
    $ai->chat($console_type, $contextId, $msg, $robot, $card, $lang, $model, $debug, $ext);
}
```

#### 新的控制器代码：
```php
public function chat()
{
    // ... 参数获取 ...
    
    $ai = \app\common\service\ai\engine\LocalEngineFactory::create($enhanceType);
    $ai->chat($console_type, $contextId, $msg, $robot, $card, $lang, $model, $debug, $ext);
}
```

### 3. 配置文件修改

如果你的项目配置中有引擎类型的设置，需要更新为新的类型名称：

```php
// 原来的配置
'engine_type' => 'Localprofessional'

// 新的配置
'engine_type' => 'professional'
```

### 4. 数据库字段更新（如果需要）

如果数据库中存储了引擎类型信息，需要更新：

```sql
-- 更新引擎类型字段
UPDATE ai_project SET engine_type = 'basic' WHERE engine_type = 'Local';
UPDATE ai_project SET engine_type = 'enhance' WHERE engine_type = 'Localenhance';
UPDATE ai_project SET engine_type = 'professional' WHERE engine_type = 'Localprofessional';
```

## 优势

### 1. 代码复用
- 消除了大量重复代码
- 共同的基础功能在基类中实现
- 策略类只需关注差异化功能

### 2. 易于维护
- 修改基础功能只需修改一处
- 新增功能可以通过继承现有策略实现
- 清晰的职责分离

### 3. 易于扩展
- 新增策略只需实现 `StrategyInterface` 接口
- 可以在运行时动态切换策略
- 支持组合不同策略的功能

### 4. 向后兼容
- 保持了原有的 `chat()` 方法签名
- 可以逐步迁移，不需要一次性修改所有代码

## 注意事项

1. **测试充分**：迁移后需要充分测试各种场景，确保功能正常
2. **逐步迁移**：建议先在测试环境验证，然后逐步迁移生产环境
3. **保留备份**：迁移前备份原有文件，以防需要回滚
4. **监控日志**：迁移后密切关注错误日志，及时发现问题

## 扩展示例

### 添加新策略

如果需要添加新的策略，只需：

1. 创建新的策略类：
```php
class CustomStrategy implements StrategyInterface
{
    // 实现接口方法
}
```

2. 在工厂类中添加创建逻辑：
```php
case 'custom':
    return new LocalEngine(LocalEngine::STRATEGY_CUSTOM);
```

3. 在 LocalEngine 中添加策略常量和创建逻辑：
```php
const STRATEGY_CUSTOM = 'custom';

case self::STRATEGY_CUSTOM:
    $this->strategy = new CustomStrategy();
    break;
```

这样就可以无缝添加新功能，而不影响现有代码。
