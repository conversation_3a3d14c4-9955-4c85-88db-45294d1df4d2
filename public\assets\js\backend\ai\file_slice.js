define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'ai/file_slice/index' + location.search,
                    add_url: 'ai/file_slice/add',
                    edit_url: 'ai/file_slice/edit',
                    del_url: 'ai/file_slice/del',
                    multi_url: 'ai/file_slice/multi',
                    import_url: 'ai/file_slice/import',
                    table: 'ai_file_slice',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'slice_id', title: __('Slice_id'), operate: 'LIKE'},
                        {field: 'type', title: __('Type'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'knowledgeBaseId', title: __('Knowledgebaseid'), operate: 'LIKE'},
                        {field: 'documentId', title: __('Documentid'), operate: 'LIKE'},
                        {field: 'content', title: __('Content'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'enabled', title: __('Enabled'), searchList: {"1":__('Enabled 1'),"0":__('Enabled 0')}, formatter: Table.api.formatter.normal},
                        {field: 'wordCount', title: __('Wordcount')},
                        {field: 'tokenCount', title: __('Tokencount')},
                        {field: 'status', title: __('Status'), searchList: {"50":__('Status 50')}, formatter: Table.api.formatter.status},
                        {field: 'statusMessage', title: __('Statusmessage'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'createTime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updateTime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
