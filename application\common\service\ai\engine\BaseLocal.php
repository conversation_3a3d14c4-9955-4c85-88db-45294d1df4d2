<?php

namespace app\common\service\ai\engine;

use app\common\service\ai\BaseEngine;
use app\common\service\IpService;
use think\Db;
use app\admin\model\ai\Msg;
use app\admin\model\ai\ConsoleFile;
use app\admin\model\ai\UrlData;

/** 本地模型基础类 */
abstract class BaseLocal extends BaseEngine
{
    protected $url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
    protected $token = "sk-613ccddd0b0948179c815a0e11cd8ebf";
    protected $model = 'deepseek-r1';
    protected $references = [];
    protected $save_references = [];
    protected $lastMsgId;
    protected $paramsType = false;
    protected $paramsTypeArr = ["farui-plus","chatglm3-6b"];
    public $debug = false;
    protected $msg = '';
    public $group_id = 2;
    protected $pgTable = 'vector_data_upgrades';
    protected $pythonPath = '/usr/bin/python3';

    /** 生成UUID */
    protected function generateUUIDv4() {
        $data = random_bytes(16);
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80);
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }

    /** 链接PGsql数据库 */
    protected function pg_connect()
    {
        return Db::connect([
            'type' => 'pgsql',
            'hostname' => '127.0.0.1',
            'database' => config('postgres.database'),
            'username' => 'postgres',
            'password' => 'DRrTmhCKrLWs2b34',
            'hostport' => '5432'
        ]);
    }

    /** 获取对话ID */
    public function getChatId()
    {
        return $this->generateUUIDv4();
    }

    /** 获取项目信息 */
    protected function getProjectInfo($robot)
    {
        $project = Db::name('ai_project')->where('id', $robot)->find();
        if(!empty($project['console_ids']) && $this->group_id == 3)
        {
            $consoleArr = Db::name('ai_console')->where(['id'=>['in', explode(',', $project['console_ids'])], 'invoking_status'=>1])->column('id');
            $project['console_ids'] = implode(',', $consoleArr);
        }
        return $project;
    }

    /** 获取模型信息 */
    protected function getModelInfo($project, $model)
    {
        $where = $model ? ['model' => $model] : ['id' => $project['model_id']];
        return Db::name('ai_bigmodel')->where($where)->find();
    }

    /** 设置大模型配置信息 */
    protected function setBigModelConfig($bigModel)
    {
        $this->url = $bigModel['api_url'];
        $this->token = $bigModel['api_key'];
        $this->model = $bigModel['model'];
        $this->paramsType = in_array($this->model, $this->paramsTypeArr);
    }

    /** 构造对话上下文 */
    protected function buildContextMessages($contextId)
    {
        $messages = [];
        $msgList = Msg::where('chatId', $contextId)->order('id', 'desc')->limit(3)->select();
        foreach ($msgList as $item) {
            $messages[] = ["role" => "user", "content" => $item['msg']];
            $content = mb_substr($item['content'], 0, 50);
            $messages[] = ["role" => "assistant", "content" => $content];
        }
        return $messages;
    }

    /** 构造系统提示词 */
    protected function buildSystemPrompt($basePrompt, $lang, $vectorStr)
    {
        if ($lang && $lang != 'cn') {
            $basePrompt .= " 请只用英文回答。不要重复问题。提供一个直接的答案。";
        }

        if ($vectorStr) {
            $rules = [
                "只使用参考资料中的信息来回答。",
                "不要直接引用或重复原文内容。",
                "不要在答案中包含任何推理或指导文本。",
                "忽略参考内容中的图片、视频或链接。"
            ];
            
            if ($this->group_id != 3) {
                $rules[] = "根据下面的编号参考，回答用户的问题。";
                $rules[] = "在你的答案中，用带数字的方括号指出来源，例如，^[1]^,^[2]^。";
            }
            
            $rulesStr = implode("\n", array_map(function($rule) {
                return "					" . $rule;
            }, $rules));

            return <<<PROMPT
                任务和规则:
                    {$rulesStr}
                以下是检索到的相关文档内容:
                    {$vectorStr}
                提问问题：
                {$basePrompt}
            PROMPT;
        }
        return $basePrompt ?: '';
    }

    /** 构造请求payload */
    protected function buildPayload($messages)
    {
        $payload = [
            "model" => $this->model,
            "messages" => $messages,
            "stream" => true,
            "max_tokens" => 2096,
            "stop" => ["null"],
            "temperature" => 0.2,
            "top_p" => 0.2,
            "top_k" => 20,
            "frequency_penalty" => 1,
            "n" => 1,
            "response_format" => ["type" => "text"],
        ];
        
        if ($this->paramsType) {
            $payload = array_merge($payload, [
                "parameters" => [
                    "result_format" => "message",
                    "incremental_output" => true
                ],
                "input" => ["messages" => $messages]
            ]);
        }
        return $payload;
    }

    /** 构造请求头 */
    protected function buildHeaders()
    {
        $headers = [
            'Content-Type:application/json',
            'Authorization:Bearer ' . $this->token
        ];
        if ($this->paramsType) {
            $headers[] = 'X-DashScope-SSE:enable';
        }
        return $headers;
    }

    /** 翻译引用内容 */
    protected function translateReferences($targetLang)
    {
        $Translation = new \app\common\library\Translation();
        foreach ($this->references as $rKey => &$rValue) {
            $rValue['title'] = $Translation->xfyun($rValue['title'], 'cn', $targetLang);
            $rValue['content'] = $Translation->xfyun($rValue['content'], 'cn', $targetLang);
        }
    }

    /** 清理合并后的内容 */
    protected function cleanupCombinedContent($content)
    {
        $lines = explode("\n", $content);
        $uniqueLines = [];
        $seenLines = [];
        
        foreach ($lines as $line) {
            $trimmedLine = trim($line);
            if (empty($trimmedLine)) continue;
            
            $lineHash = md5($trimmedLine);
            if (!isset($seenLines[$lineHash])) {
                $uniqueLines[] = $trimmedLine;
                $seenLines[$lineHash] = true;
            }
        }
        
        return implode("\n", $uniqueLines);
    }

    /** 抽象方法 - 子类必须实现 */
    abstract protected function buildReferenceContent($project, $msg);
    abstract public function chat($console_type, $contextId, $msg, $robot, $card, $lang = 'cn', $model = null, $debug = false, $ext = []);
}