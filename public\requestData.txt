{"image_process": [], "code": 200, "result": {"markdown": "版权所有·禁止翻制、电子传阅、发售\n\n<!-- SN -->\n![](https://web-api.textin.com/ocr_image/external/ecb2779d5694a807.jpg)\n\n# 中华人民共和国出入境检验检疫行业标准\n\nSN/T 4049-2014\n\n# 出口食品中氯酸盐的测定 离子色谱法\n\n## Determination of chlorate in food for export-Ion chromatography method\n\n2014-11-19发布 2015-05-01实施\n\n中华人民共和国 发布\n\n国家质量监督检验检疫总局\n\n版权所有·禁止翻制、电子传阅、发售\n\nSN/T 4049-2014\n\n# 前 言\n\n本标准按照GB/T 1.1-2009给出的规则起草。\n\n请注意本文件的某些内容可能涉及专利。本文件的发布机构不承担识别这些专利的责任。\n\n本标准由国家认证认可监督管理委员会提出并归口。\n\n本标准起草单位：中华人民共和国广东出入境检验检疫局。\n\n本标准主要起草人：潘丙珍、奚星林、欧阳少伦、庞世琦、刘青、李敏、李荀、陈秀明、梁瑞婷、李双。\n\n<!-- I -->\n\n版权所有·禁止翻制、电子传阅、发售\n\nSN/T 4049-2014\n\n# 出口食品中氯酸盐的测定 离子色谱法\n\n## 1 范围\n\n本标准规定了出口食品中氯酸盐的离子色谱测定方法。\n\n本标准适用于鲜龙眼、龙眼罐头、龙眼干、芒果、芒果汁、苹果汁、混合果蔬汁、土豆、葡萄酒、啤酒、小麦粉、大米、牛奶、猪肉和鱼等食品中氯酸盐的测定。\n\n## 2 方法提要\n\n试样中的氯酸根离子（ $\\mathrm {ClO}_{3}^{-}$ ）用纯水提取，经固相萃取柱净化，采用阴离子交换色谱柱分离，离子色谱-电导检测器测定，外标法定量。\n\n## 3 试剂和材料\n\n除另有规定外，所用试剂均为分析纯，实验用水为高纯水，电阻率为 $18.2\\mathrm {M}Ω·\\mathrm {\\sim cm}$ 。\n\n3.1 石油醚：沸程30 $^{\\circ }C\\sim 60^{\\circ }C。$ \n\n3.2 冰乙酸：优级纯。\n\n3.3 $3\\%$ （体积分数）冰乙酸溶液：取冰乙酸 $(3.2)3mL$ ，加水并定容至 $100mL$ 。\n\n3.4 氯酸钾标准物质：纯度大于 $99\\%$ 。\n\n3.5 氯酸根离子（ $\\mathrm {ClO}_{3}^{-}$ ）标准储备液 $(1.0mg/mL)$ ：准确称取 $0.1467g$ 氯酸钾（3.4)，用水溶解并定容至 $100mL$ ，配成含 $\\mathrm {ClO}_{3}^{-}$ $1.0mg/mL$ 标准储备液，0 $^{\\circ }C\\sim 4^{\\circ }$ 保存。此溶液也可采用有证标准物质溶液。\n\n3.6 氯酸根离子（ $\\mathrm {ClO}_{3}^{-}$ ）标准中间液：准确吸取 $2.0mL$ 的氯酸根标准储备液（3.5）到 $100mL$ 容量瓶，用水定容，摇匀备用， $\\mathrm {ClO}_{3}^{-}$ 浓度为20mg/L,0 $^{\\circ }C\\sim 4^{\\circ }C$ 保存。\n\n3.7 氯酸根离子（ $\\mathrm {ClO}_{3}^{-}$ ）标准工作溶液：根据需要用水将标准应用液（3.6）稀释成 $0.025mg/L$ 、 $0.05mg/L$ 、 $0.10mg/L、0.20mg/L、0.40mg/L、1.00mg/L$  和 $2.00mg/L$ 标准工作曲线溶液。现配现用。\n\n## 4 仪器和设备\n\n4.1 离子色谱仪：电导检测器，配梯度泵，自动淋洗液发生器。\n\n4.2 组织捣碎机。\n\n4.3 搅拌器。\n\n4.4 分析天平：感量为 $0.2mg$ 。\n\n4.5 超声波提取器。\n\n**4.6** 离心机： $4500r/\\min$ ，适配 $50mL$ 离心管。\n\n**4.7** 固相萃取 $\\mathrm {C}_{18}$ 柱： $200mg/3mL$ mL或相当者。\n\n4.8 微孔滤膜： $0.45μm$ ，水系。\n\n<!-- 1 -->\n\n<!-- SN/T 4049-2014 -->\n\n## 版权所有·禁止翻制、电子传阅、发售\n\n## 5 试样制备和保存\n\n### 5.1 一般要求\n\n在抽样和制样过程中，应防止样品受到污染或发生残留量的变化。\n\n### 5.2 制备与保存\n\n#### 5.2.1 鲜龙眼、龙眼罐头、龙眼干、芒果、土豆、小麦粉、大米、猪肉和鱼\n\n从所取全部样品中取出有代表性样品约 $500g$ ，取可食用部分经捣碎机（4.2）充分捣碎均匀，均分成两份，分别装入洁净容器内作为试样，密封并标明标记。试样在C0 $^{\\circ }C\\sim 4^{\\circ }C$ 冷藏保存。\n\n#### 5.2.2 芒果汁、苹果汁、混合果蔬汁、葡萄酒\n\n从所取全部样品中取出有代表性样品约 $500g$ ，均分成两份，分别装入洁净容器内作为试样，密封并标明标记。试样在 $0^{\\circ }C\\sim 4^{\\circ }C$ 冷藏保存。\n\n#### 5.2.3 啤酒\n\n从所取全部样品中取出有代表性样品约 $500g$ ，以搅拌器（4.3）充分搅拌，除去二氧化碳，均分成两份，分别装入洁净容器内作为试样，密封并标明标记。试样在0 $^{\\circ }C\\sim 4^{\\circ }C$ 冷藏保存。\n\n## 6 测定步骤\n\n### 6.1 试样的处理\n\n#### 6.1.1 鲜龙眼、龙眼罐头、龙眼干、芒果、土豆、芒果汁、苹果汁、混合果蔬汁、葡萄酒、啤酒、小麦粉和大米\n\n称取试样 $5.0g$ （龙眼干 $1.0g)$ ，精确至 $0.0002g$ ，置于 $50mL$ 的容量瓶中，加入约 $30mL$ 水，摇匀。超声提取 $30min$ ，用水定容。转移试液至 $50mL$ 离心管，以 $4500r/\\min$ 速度离心 $10\\min$ 。取上清液备用。\n\n##### 6.1.2 牛奶\n\n称取试样（牛奶： $5.0g$ ，精确至 $0.0002g)$  ，置于 $50mL$ 容量瓶中，加入约 $30mL$ 水，摇匀，超声提取 $30min$ 。加入 $3\\%$  （体积分数）乙酸溶液 $(3.3)4mL$ ，摇匀，于 $4^{\\circ }C$ 放置 $20min$ 。取出放置至室温，用水定容。转移试液至 $50mL$ 离心管中，以 $4500r/\\min$ 速度离心 $10min$ 。取上清液备用。\n\n##### 6.1.3 猪肉和鱼\n\n称取试样 $5.0g$ （精确至 $0.0002g)$  于烧杯中，加入 $25mL$ 石油醚（3.1)，搅拌，倾出上层石油醚。再次加 $25mL$  石油醚（3.1)，搅拌，倾出上层石油醚，水浴上微微加热挥干石油醚。将试样转移至 $50mL$  容量瓶中，加入约 $30mL$ 水，摇匀，超声提取 $30min$ ，用水定容。转移试液至 $50mL$ 离心管中，以 $4500r/\\min$ 速度离心 $10min$ 。取上清液备用。\n\n### 6.2 净化\n\n固相萃取 $\\mathrm {C}_{18}$ 柱（4.7）使用前依次用 $10mL$ 甲醇、 $15mL$ 水通过，活化 $30\\min$ 。将6.1中上清液经 $\\mathrm {C}_{18}$ 柱净化，再经 $0.45\\mu m$ 微孔滤膜（4.8）过滤，收集滤液待测。\n\n<!-- 2 -->\n\n### 版权所有·禁止翻制、电子传阅、发售\n\nSN/T 4049-2014\n\n#### 6.3 测定\n\n##### 6.3.1 离子色谱参考条件\n\n离子色谱参考条件如下：\n\na）色谱柱：阴离子交换色谱柱［分析柱：DIONEX IonPacAS19(4 $\\mathrm {mm}x250$ mm)，保护柱：DI-ONEX IonPac AG19(4 $mm\\times 50mm)$ ]，或效能相当者；\n\nb）淋洗液：由自动淋洗液发生器产生氢氧化钾，梯度如表1；\n\nc）流速： $0.8mL/\\min$ ；\n\nd）抑制电流： $100mA$ ；\n\ne）检测池温度： $30^{\\circ }C$ ；\n\nf）进样量 $100\\mu L$ 。\n\n**表1 淋洗液梯度淋洗程序**\n\n<table border=\"1\" ><tr>\n<td colspan=\"1\" rowspan=\"1\">时间／$min$</td>\n<td colspan=\"1\" rowspan=\"1\">淋洗液浓度／($(mmol/L)$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">0 </td>\n<td colspan=\"1\" rowspan=\"1\">5 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">25 </td>\n<td colspan=\"1\" rowspan=\"1\">5 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">30 </td>\n<td colspan=\"1\" rowspan=\"1\">50 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">40 </td>\n<td colspan=\"1\" rowspan=\"1\">50 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">45 </td>\n<td colspan=\"1\" rowspan=\"1\">5 </td>\n</tr></table>\n\n##### 6.3.2 离子色谱测定\n\n根据样液中 $\\mathrm {ClO}_{3}^{-}$ 浓度的情况选定峰面积相近的标准溶液系列。标准溶液和样液中 $\\mathrm {ClO}_{3}^{-}$ 的响应值均应在仪器检测的线性范围内。以峰面积为纵坐标， $\\mathrm {ClO}_{3}^{-}$ 标准溶液浓度为横坐标绘制标准工作曲线。用保留时间定性，外标法定量。在上述色谱条件下 $ClO_{3}^{-}$  的参考保留时间约为 $18min$ ，色谱图参见图A.1。\n\n##### 6.3.3 空白试验\n\n除不加试样外，均按上述操作步骤进行。\n\n## 7 结果计算\n\n试样中 $\\mathrm {ClO}_{3}^{-}$ 的测定结果可按式（1）计算，计算结果需扣除空白值：\n\n$$X=\\frac {CxV}{m}\\tag{1}$$\n\n式中：\n\n$X$ -试样中（ $\\mathrm {ClO}_{3}^{-}$  的含量，单位为毫克每千克 $(mg/kg)$ \n\n$C$ -从标准曲线上查得的试样溶液（ $\\mathrm {ClO}_{3}^{-}$ 浓度，单位为毫克每升 $(mg/L)$ ;\n\n$A$ -试样溶液最终定容体积，单位为毫升（ $(mL)$ ；\n\n$m$ -样液所代表的试样质量，单位为克 $(g)$ 。\n\n计算结果保留3位有效数字。\n\n<!-- 3 -->\n\n<!-- SN/T 4049-2014 -->\n\n### 版权所有·禁止翻制、电子传阅、发售\n\n## 8 测定低限和回收率\n\n### 8.1 测定低限\n\n本标准标准曲线线性范围为 $0.025mg/L\\sim 2.0mg/L$ 。本标准对鲜龙眼、龙眼罐头、芒果、芒果汁、苹果汁、混合果蔬汁、土豆、啤酒、小麦粉、大米、牛奶、猪肉和鱼试样中 $\\mathrm {ClO}_{3}^{-}$  的测定低限为 $0.5mg/kg$ ；对葡萄酒试样中 $\\mathrm {ClO}_{3}^{-}$  的测定低限为 $1.0mg/kg$ ；；对龙眼干试样中C $\\mathrm {ClO}_{3}^{-}$  的测定低限为 $2.5mg/kg$ 。\n\n### 8.2 回收率\n\n食品中氯酸盐不同添加水平的添加回收率范围见表2。\n\n表2 **食品中氯酸盐的添加回收率范围**\n\n<table border=\"1\" ><tr>\n<td colspan=\"1\" rowspan=\"1\">基质</td>\n<td colspan=\"1\" rowspan=\"1\">加标浓度／$(mg/kg)$</td>\n<td colspan=\"1\" rowspan=\"1\">回收率／$\\%$</td>\n<td colspan=\"1\" rowspan=\"1\">基质</td>\n<td colspan=\"1\" rowspan=\"1\">加标浓度／$(mg/kg)$</td>\n<td colspan=\"1\" rowspan=\"1\">回收率／$\\%$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">鲜龙眼</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">88.6\\~100.8</td>\n<td colspan=\"1\" rowspan=\"3\">葡萄酒</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$81.9\\sim 92.1$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.6\\~103.4</td>\n<td colspan=\"1\" rowspan=\"1\">2.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$84.2\\sim 95.8$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$91.2\\sim 101.1$</td>\n<td colspan=\"1\" rowspan=\"1\">10.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$88.4\\sim 100.1$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">龙眼罐头</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">89.0\\~101.2</td>\n<td colspan=\"1\" rowspan=\"3\">啤酒</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">88.4\\~100.4</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$91.0\\sim 103.2$</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$87.3\\sim 103.2$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.8\\~101.6</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$90.0\\sim 102.1$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">龙眼干</td>\n<td colspan=\"1\" rowspan=\"1\">2.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$82.5\\sim 93.0$</td>\n<td colspan=\"1\" rowspan=\"3\">小麦粉</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$89.0\\sim 100.4$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.3\\sim 95.3$</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$90.4\\sim 104.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">25.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$87.8\\sim 97.0$</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$92.0\\sim 101.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">芒果</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$84.6\\sim 97.4$</td>\n<td colspan=\"1\" rowspan=\"3\">大米</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.4\\sim 99.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$85.6\\sim 96.2$</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$88.6\\sim 101.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$89.7\\sim 97.6$</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$88.6\\sim 100.6$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">芒果汁</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.4\\sim 98.2$</td>\n<td colspan=\"1\" rowspan=\"3\">牛奶</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.6\\sim 97.4$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$88.5\\sim 97.6$</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$89.4\\sim 102.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$90.7\\sim 100.4$</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$89.8\\sim 100.5$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">苹果汁</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$87.4\\sim 100.4$</td>\n<td colspan=\"1\" rowspan=\"3\">猪肉</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$84.4\\sim 93.6$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">89.2\\~102.3</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$87.5\\sim 96.8$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$90.9\\sim 100.4$</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$87.8\\sim 95.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">混合果蔬汁</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$84.4\\sim 98.2$</td>\n<td colspan=\"1\" rowspan=\"3\">鱼</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.4\\sim 100.2$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.5\\sim 96.3$</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$88.5\\sim 99.3$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$91.0\\sim 100.2$</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$90.6\\sim 101.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">土豆</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$84.2\\sim 97.2$</td>\n<td colspan=\"1\" rowspan=\"3\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.7\\sim 96.2$</td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$91.7\\sim 98.0$</td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n</tr></table>\n\n## 9 精密度\n\n在重复性条件下获得的两次独立测定结果的绝对差值不得超过算术平均值的 $10\\%$ 。\n\n<!-- 4 -->\n\n## 版权所有·禁止翻制、电子传阅、发售\n\nSN/T 4049-2014\n\n## 附录A\n\n## （资料性附录）\n\n## 标准物质色谱图\n\n<!-- 4.00 $\\mathrm {ClO}_{3}^{-}$ -0.30 1.8 5.0 7.5 10.0 12.5 15.0 17.5 20.0 22.5 25.0 27.5 30.0 32.5 35.0 39.5 min -->\n![](https://web-api.textin.com/ocr_image/external/44bd9241892ee820.jpg)\n\n**图A.1** $2.0mg/LClO_{3}^{-}$ **标准物质的离子色谱图**\n\n<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>\n\n<!-- 5 -->\n\n## 版权所有·禁止翻制、电子传阅、发售\n\nSN/T 4049-2014\n\n## Foreword\n\nThis standard was drafted in accordance with the GB/T 1.1-2009.\n\nPlease note that some of the content of the standard may involve patents.Publication of the present the standard does not bear the responsibility of identifying these patents.\n\nThis standard was proposed by and is under the jurisdiction of Certifercation and Accreditation Ad-ministration of the People's Republic of China.\n\nThis standard was drafted by Guangdong Entry-Exit Inspection and Quarantine Bureau of the People's Republic of China.\n\nThis standard was mainly drafted by Pan Bingzhen,Xi Xinglin,Ouyang Shaolun,Pang Shiqi,Liu Qing,Li Min,Li Xun,Chen Xiuming,Liang Ruiting, Li Shuang.\n\nNote:This English version,a translation from the Chinese text, is solely for guidance.\n\n<!-- 6 -->\n\n## 版权所有·禁止翻制、电子传阅、发售\n\nSN/T 4049-2014\n\n## Determination of chlorate in food for export-\n\n## lon chromatography method\n\n### 1 Scope\n\nThis standard specifies the method for the determination of chlorate in food for export by lon chro-matography method.\n\nThis standard is applicable to the determination of chlorate in Fresh longan, canned longan,dried lon-gan, mango, mango juice,apple juice, mixed fruit and vegetables juice, potato, wine,beer,flour,rice,milk,pork,fish.\n\n### 2 Principle\n\nChlorate residues are extracted from the sample by water. It is cleaned up with solid phase extraction column. It is determined by lon chromatography equipped with conductance detector,quantified by external standard method.\n\n### 3 Reagents and materials\n\nUnless otherwise specified, the reagents used should be analytical grade. And the water should be ul-tra-pure grade water with 18.2 MΩ·cm resistivity.\n\n3.1 Petroleum ether:Boiling range (30 ℃\\~60 ℃).\n\n**3.2** Acetic acid:Guaranteed reagent.\n\n**3.3** 3%(V/V)Acetic acid solution:Pipette 3.0 mL acetic acid and dilute to 100 mL with water.\n\n**3.4** Chlorate potassium standard:Purity was 99% above.\n\n**3.5** Standard stock solution: Accurately weigh 0.146 7 g chlorate potassium (3.4),dissolve and quantitatively with water. The concentration of the solution is 1.0 mg/mL. Standard stock solution has certificate can also be used(be stored at 0 ℃\\~4 ℃).\n\n<!-- 7 -->\n\n<!-- SN/T 4049-2014 -->\n\n### 版权所有·禁止翻制、电子传阅、发售\n\n3.6 Standard transition solution: Accurately transfer $2.0mL$  Standard stock solution(3.5) into $100mL$  volummetric flask, then make up to graduation with water. The concentration of the solution is $20mg/L$ (be stored at $0^{\\circ }C\\sim 4^{\\circ }C$ .\n\n3.7 Standard working solution:According to the requirement,accurately measure different volumes of standard stock soluttion to volumetric flask, dilute with water to make different concentration of Standard working solution such as $0.025mg/L$ , $0.05mg/L$ , $0.10mg/L$ , , $0.20mg/L,0.40mg/L$  $1.00mg/L$ , . $2.00mg/L$ \n\n### 4 Apparatus and equipment\n\n**4.1** lon chromatography,equipped with conductance detector.\n\n**4.2** Tissue blender.\n\n**4.3** Mixer.\n\n**4.4** Balance(0.2 mg).\n\n**4.5** Ulrasonic cleaner.\n\n**4.6** Centrifuge:4 500 r/min,50 mL centrifuge tube.\n\n**4.7** Solid phase extraction $\\mathrm {C}_{18}$ column:200 mg,3 mL or equivalent column.\n\n**4.8** Membrane filter: nylon, $0.45μm$ ,water phase.\n\n### 5 Sample preparation and storage\n\n#### 5.1 Requirement\n\nIn the course of sampling and sample preparation $,$  precations shall be take to avoid the contamination or any factors which may cause the change of residue content.\n\n#### 5.2 preparation and storage\n\n5.2.1 Fresh longan, canned longan, dried longan, mango, potato,flour,rice, pork, and fish\n\nAbout 500 g representative samples shouled be taken from all samples, the edible parts are cut into 8\n\n##### 版权所有·禁止翻制、电子传阅、发售\n\nSN/T 4049-2014\n\nmince and homogenized by a high speed tissue triturator.The mixed primary sample is divided into two equal portion .Each portion is put into one clean sample bottle which is sealed and labled. The sample should be stored at 0℃\\~4℃.\n\n###### 5.2.2 Mango juice,apple juice, mixed fruit and vegetables juice,milk,wine\n\nAbout 500 g representative samples shouled be taken from all samples.The mixed primary sample is divided into two equal portion .Each portion is put into one clean sample bottle which is sealed and labled. The sample should be stored at 0 ℃\\~4 ℃.\n\n###### 5.2.3 Beer\n\nAbout 500 g representative samples shouled be taken from all samples. Smash thoroughly in a ceramic barrel. The mixed primary sample is divided into two equal portion .Each portion is put into one clean sample bottle which is sealed and fabled. The sample should be stored at 0 ℃\\~4 ℃.\n\n### 6 Determination procedure\n\n#### 6.1 Extraction\n\n6.1.1 Fresh longan, canned longan, dried longan, mango,potato, mango juice,apple juice,mixed fruit and vegetables juice,wvine, Beer, flour and rice\n\nWeight 5.0 g sample (dried longan 1.0 g),accurate to 0.000 2 g, in 50 mL volumetric flask, add about 30 mL water, mix well, extract for 30 min in ultrasonic water bath.dilute to volume with water.Transfer the extraction to 50 mL centrifuge tube tube, then centrifuge at 4 500 r/min for 10 min.\n\n##### 6.1.2 Milk\n\nWeight the sample (milk:5.0 g),accurate to 0.000 2 g ,in 50 mL volumetric flask, add about 30 mL water,mix well,extract for 30 min in ultrasonic water bath. Add 3% Acetic acid solution 4 mL,mix well, place 20 min under 4 ℃.Put out and return to room temperature,dilute to volume with water.Transfer the extraction to 50 mL centrifuge tube tube, then centrifuge at 4 500 r/min for 10 min.\n\n##### 6.1.3 Pork and fish\n\nWeight 5.0 g sample in beaker, accurate to 0.000 2 g, add 25 mL petroleum ether, stir well,pour out petroleum ether. Add 25 mL petroleum ether again, stir well, pour out petroleum ether.Volatile pe-troleum ether in heating slightly by water bath.Transfer the sample to 50 mL volumetric flask, add a-bout 30 mL water, mix well, extract for 30 min in ultrasonic water bath. Dilute to volume with wa-\n\n<!-- 9 -->\n\n### 版权所有·禁止翻制、电子传阅、发售\n\n### SN/T 4049-2014\n\nter. Transfer theextraction to $50mL$  centrifuge tube, then centrifuge at $4500r/min$  for $10min$ .\n\n### 6.2 Clean up\n\nThe $\\mathrm {C}_{18}$ column(4.7) is leached with $10mL$  methanol and $15mL$  water one by one.Then The $\\mathrm {C}_{18}$ column is activated for 30 min before using. The supernatant(6.1)is cleaned up by $\\mathrm {C}_{18}$  column,and then is filtrated with $0.45μm$  microporous membrane(4.8). The filtrate is collected to be measured.\n\n### 6.3 Determination\n\n#### 6.3.1 Chromatography reference operating conditions\n\nChromatography reference operating conditions are as follows:\n\na) Chromatography column: lonPac AS19,4 mmx 250 mm(with lonPac AG19, $4\\mathrm {\\sim mm}x50\\mathrm {\\sim mm}$  ,guard column)or equivalent column.\n\nb) Mobile phase:5 mmol/L\\~50 mmol/L KOH.\n\nc) Flow rate:0.80 mL/min.\n\nd) Restrain electric current: $100mA$ .\n\ne) Column tempreture: 30 ℃.\n\nf) Injector volumn:100 μL.\n\n**Table 1-Mobile phase gradient program**\n\n<table border=\"1\" ><tr>\n<td colspan=\"1\" rowspan=\"1\">Time/min</td>\n<td colspan=\"1\" rowspan=\"1\">Mobile phase/(mmol/L)</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">0 </td>\n<td colspan=\"1\" rowspan=\"1\">5 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">25 </td>\n<td colspan=\"1\" rowspan=\"1\">5 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">30 </td>\n<td colspan=\"1\" rowspan=\"1\">50 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">40 </td>\n<td colspan=\"1\" rowspan=\"1\">50 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">45 </td>\n<td colspan=\"1\" rowspan=\"1\">5 </td>\n</tr></table>\n\n#### 6.3.2 Determination\n\nAccording to the approximate concentration of the chlorate potassium in the sample solution,select the standard working solution with similar concentration of the sample solution. The standard\n\n<!-- 10 -->\n\n### 版权所有·禁止翻制、电子传阅、发售\n\nSN/T 4049-2014\n\nworking solution should be injected in-between the injections of the sample solution with one com-mon volume. The response of chlorate potassium in the standard working solution and sample solu-tion should be within the linear range of the instrument detection.Under the above chromatograph conditions,the reference retention time of chlorate potassium is about $17min$ . The chromatograms of the standard working solution is showed in figure A.1.\n\n#### 6.3.3 Blank test\n\nThe operation of the blank test is the same as that described in the method of determination, but without addition of sample.\n\n### 7 Calculation and expression of the result\n\nCalculate the content of chlorate in the test sample by IC data processor or according to the formula (1),the blank value should be subtracted from the above resule of calculation.\n\n$$X=\\frac {C\\times V}{m}\\tag{1}$$\n\nWhere:\n\nX-the concentration of chlorate in the test sample,mg/kg;\n\nC -the concentration of chlorate is fromcalibration curve,mg/L;\n\n$V$ -the final volume of the sample solution,mL;\n\n$m$ -the corresponding mass of the test sample in the final sample solution,g.\n\n### 8 Limit of quatification and recovery\n\n#### 8.1 Limit of quatification\n\nThe limit of determination of this method is $0.5mg/kg$  for Fresh longan, canned longan,mango,mango juice,apple juice, mixed fruit and vegetables juice, potato, beer,flour, rice,milk,pork and fish.The limit of determination of this method is $1.0mg/kg$  for wine. The limit of determination of this method is $2.5mg/kg$  for dried longan.\n\n<!-- 11 -->\n\n<!-- 版权所有·禁止翻制、电子传阅、发售 -->\n\nSN/T 4049-2014\n\n#### 8.2 Recovery\n\nThe recoveries range of fortifying concentrations see table 2.\n\nTable **2-Recoveries range of chlorate** in **different samples**\n\n<table border=\"1\" ><tr>\n<td colspan=\"1\" rowspan=\"1\">sample </td>\n<td colspan=\"1\" rowspan=\"1\">Spike levels/(mg/kg)</td>\n<td colspan=\"1\" rowspan=\"1\">Recovery range/%</td>\n<td colspan=\"1\" rowspan=\"1\">sample </td>\n<td colspan=\"1\" rowspan=\"1\">Spike levels/(mg/kg)</td>\n<td colspan=\"1\" rowspan=\"1\">Recovery range/%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">Fresh longan </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">88.6\\~100.8</td>\n<td colspan=\"1\" rowspan=\"3\">wine </td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">81.9\\~92.1</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.6\\~103.4</td>\n<td colspan=\"1\" rowspan=\"1\">2.0 </td>\n<td colspan=\"1\" rowspan=\"1\">84.2\\~95.8</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.2\\~101.1</td>\n<td colspan=\"1\" rowspan=\"1\">10.0 </td>\n<td colspan=\"1\" rowspan=\"1\">88.4\\~100.1</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">canned longan </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">89.0\\~101.2</td>\n<td colspan=\"1\" rowspan=\"3\">Beer </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">88.4\\~100.4</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.0\\~103.2</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">87.3\\~103.2</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.8\\~101.6</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">90.0\\~102.1</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">dried longan </td>\n<td colspan=\"1\" rowspan=\"1\">2.5 </td>\n<td colspan=\"1\" rowspan=\"1\">82.5\\~93.0</td>\n<td colspan=\"1\" rowspan=\"3\">Flour </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">89.0\\~100.4</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">86.3\\~95.3</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">90.4\\~104.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">25.0 </td>\n<td colspan=\"1\" rowspan=\"1\">87.8\\~97.0</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">92.0\\~101.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">mango </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">84.6\\~97.4</td>\n<td colspan=\"1\" rowspan=\"3\">Rice </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">86.4\\~99.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">85.6\\~96.2</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">88.6\\~101.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">89.7\\~97.6</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">88.6\\~100.6</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">Mango juice </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">86.4\\~98.2</td>\n<td colspan=\"1\" rowspan=\"3\">milk </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">86.6\\~97.4</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">88.5\\~97.6</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">89.4\\~102.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">90.7\\~100.4</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">89.8\\~100.5</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">apple juice </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">87.4\\~100.4</td>\n<td colspan=\"1\" rowspan=\"3\">pork </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">84.4\\~93.6</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">89.2\\~102.3</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">87.5\\~96.8</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">90.9\\~100.4</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">87.8\\~95.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">mixed fruit and vagetables juice </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">84.4\\~98.2</td>\n<td colspan=\"1\" rowspan=\"3\">Fish </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">86.4\\~100.2</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">86.5\\~96.3</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">88.5\\~99.3</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.0\\~100.2</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">90.6\\~101.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">potato </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">84.2\\~97.2</td>\n<td colspan=\"1\" rowspan=\"3\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">86.7\\~96.2</td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.7\\~98.0</td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n</tr></table>\n\n### 9 Precision\n\nThe absolute difference of the two independent test results obtained under the condition of repetition is not more than 10% of arithmetic mean.\n\n<!-- 12 -->\n\n版权所有·禁止翻制、电子传阅、发售\n\nSN/T 4049-2014\n\n## AnnexA\n\n(Informative)\n\nChromatogram of the standard\n\n<!-- 4.00 $\\mathrm {ClO}_{3}^{-}$ -0.30 1.8 5.0 7.5 10.0 12.5 15.0 17.5 20.0 22.5 25.0 27.5 30.0 32.5 35.0 39.5 min -->\n![](https://web-api.textin.com/ocr_image/external/7f27d2b57932335e.jpg)\n\nFigure A.1-IC chromatogram of the chlorate standard working solution( $2.0mg/L)$ \n\n<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>\n\n<!-- 13 -->\n\n版权所有·禁止翻制、电子传阅、发售\n\n版权所有·禁止翻制、电子传阅、发售\n\n## 版权所有·禁止翻制、电子传阅、发售\n\ntI0z-6t0 J/NS\n\n中华人民共和国出入境检验检疫\n\n行业标准\n\n**出口食品中氯酸盐的测定 离子色谱法**\n\nSN/T 4049-2014\n\n*\n\n中国标准出版社出版\n\n北京市朝阳区和平里西街甲2号（100029）\n\n北京市西城区三里河北街16号（100045）\n\n总编室：(010)68533533\n\n网址 www.spc.net.cn\n\n中国标准出版社秦皇岛印刷厂印刷\n\n*\n\n开本 880x1230 1/16 印张 1.25 字数28千字\n\n2016年1月第一版 2016年1月第一次印刷\n\n\n![](https://web-api.textin.com/ocr_image/external/2a2cfce9d9a96821.jpg)\n\n印数1-1 100\n\n*\n\nSN/T 4049-2014 书号：155066·2-29302 定价21.00元\n\n", "success_count": 18, "valid_page_number": 18, "total_page_number": 18, "total_count": 18, "detail": [{"paragraph_id": 0, "page_id": 1, "tags": [], "outline_level": -1, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [232, 14, 955, 14, 955, 53, 232, 53], "content": 0, "sub_type": "text"}, {"type": "image", "text": "SN", "paragraph_id": 1, "page_id": 1, "content": 0, "position": [810, 67, 1052, 68, 1052, 193, 810, 192], "outline_level": -1, "image_url": "https://web-api.textin.com/ocr_image/external/ecb2779d5694a807.jpg"}, {"paragraph_id": 2, "page_id": 1, "tags": [], "outline_level": 0, "text": "中华人民共和国出入境检验检疫行业标准", "type": "paragraph", "position": [148, 235, 1102, 235, 1102, 284, 148, 284], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 3, "page_id": 1, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [855, 344, 1080, 344, 1080, 367, 855, 367], "content": 0, "sub_type": "text"}, {"paragraph_id": 4, "page_id": 1, "tags": [], "outline_level": 0, "text": "出口食品中氯酸盐的测定 离子色谱法", "type": "paragraph", "position": [161, 651, 1089, 651, 1089, 701, 161, 701], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 5, "page_id": 1, "tags": [], "outline_level": 1, "text": "Determination of chlorate in food for export-Ion chromatography method", "type": "paragraph", "position": [176, 759, 1071, 759, 1071, 783, 176, 783], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 6, "page_id": 1, "tags": [], "outline_level": -1, "text": "2014-11-19发布 2015-05-01实施", "type": "paragraph", "position": [138, 1418, 1108, 1418, 1108, 1444, 138, 1444], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 1, "tags": [], "outline_level": -1, "text": "中华人民共和国 发布", "type": "paragraph", "position": [322, 1511, 924, 1511, 924, 1555, 322, 1555], "content": 0, "sub_type": "text"}, {"paragraph_id": 10, "page_id": 1, "tags": [], "outline_level": -1, "text": "国家质量监督检验检疫总局", "type": "paragraph", "position": [322, 1544, 826, 1544, 826, 1574, 322, 1574], "content": 0, "sub_type": "text"}, {"paragraph_id": 0, "page_id": 2, "tags": [], "outline_level": -1, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [232, 14, 955, 14, 955, 53, 232, 53], "content": 0, "sub_type": "text"}, {"paragraph_id": 1, "page_id": 2, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [896, 145, 1067, 145, 1067, 161, 896, 161], "content": 0, "sub_type": "text"}, {"paragraph_id": 2, "page_id": 2, "tags": [], "outline_level": 0, "text": "前 言", "type": "paragraph", "position": [538, 247, 666, 247, 666, 280, 538, 280], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 3, "page_id": 2, "tags": [], "outline_level": -1, "text": "本标准按照GB/T 1.1-2009给出的规则起草。", "type": "paragraph", "position": [181, 342, 608, 342, 608, 370, 181, 370], "content": 0, "sub_type": "text"}, {"paragraph_id": 4, "page_id": 2, "tags": [], "outline_level": -1, "text": "请注意本文件的某些内容可能涉及专利。本文件的发布机构不承担识别这些专利的责任。", "type": "paragraph", "position": [179, 378, 992, 378, 992, 398, 179, 398], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 2, "tags": [], "outline_level": -1, "text": "本标准由国家认证认可监督管理委员会提出并归口。", "type": "paragraph", "position": [181, 409, 656, 409, 656, 428, 181, 428], "content": 0, "sub_type": "text"}, {"paragraph_id": 6, "page_id": 2, "tags": [], "outline_level": -1, "text": "本标准起草单位：中华人民共和国广东出入境检验检疫局。", "type": "paragraph", "position": [181, 441, 709, 441, 709, 460, 181, 460], "content": 0, "sub_type": "text"}, {"paragraph_id": 7, "page_id": 2, "tags": [], "outline_level": -1, "text": "本标准主要起草人：潘丙珍、奚星林、欧阳少伦、庞世琦、刘青、李敏、李荀、陈秀明、梁瑞婷、李双。", "type": "paragraph", "position": [181, 472, 1033, 472, 1033, 491, 181, 491], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 2, "tags": [], "outline_level": -1, "text": "I", "type": "paragraph", "position": [1026, 1549, 1039, 1549, 1039, 1569, 1026, 1569], "content": 1, "sub_type": "footer"}, {"paragraph_id": 0, "page_id": 3, "tags": [], "outline_level": -1, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [232, 14, 955, 14, 955, 53, 232, 53], "content": 0, "sub_type": "text"}, {"paragraph_id": 1, "page_id": 3, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [896, 145, 1067, 145, 1067, 161, 896, 161], "content": 0, "sub_type": "text"}, {"paragraph_id": 2, "page_id": 3, "tags": [], "outline_level": 0, "text": "出口食品中氯酸盐的测定 离子色谱法", "type": "paragraph", "position": [335, 249, 870, 249, 870, 280, 335, 280], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 3, "page_id": 3, "tags": [], "outline_level": 1, "text": "1 范围", "type": "paragraph", "position": [138, 356, 217, 356, 217, 374, 138, 374], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 4, "page_id": 3, "tags": [], "outline_level": -1, "text": "本标准规定了出口食品中氯酸盐的离子色谱测定方法。", "type": "paragraph", "position": [181, 420, 678, 420, 678, 439, 181, 439], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 3, "tags": [], "outline_level": -1, "text": "本标准适用于鲜龙眼、龙眼罐头、龙眼干、芒果、芒果汁、苹果汁、混合果蔬汁、土豆、葡萄酒、啤酒、小麦粉、大米、牛奶、猪肉和鱼等食品中氯酸盐的测定。", "type": "paragraph", "position": [138, 451, 1067, 451, 1067, 504, 138, 504], "content": 0, "sub_type": "text"}, {"paragraph_id": 6, "page_id": 3, "tags": [], "outline_level": 1, "text": "2 方法提要", "type": "paragraph", "position": [136, 548, 260, 548, 260, 567, 136, 567], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 7, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "试样中的氯酸根离子（ $\\mathrm {ClO}_{3}^{-}$ ）用纯水提取，经固相萃取柱净化，采用阴离子交换色谱柱分离，离子色谱-电导检测器测定，外标法定量。", "type": "paragraph", "position": [138, 612, 1067, 612, 1067, 666, 138, 666], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 3, "tags": [], "outline_level": 1, "text": "3 试剂和材料", "type": "paragraph", "position": [136, 711, 281, 711, 281, 729, 136, 729], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 9, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "除另有规定外，所用试剂均为分析纯，实验用水为高纯水，电阻率为 $18.2\\mathrm {M}Ω·\\mathrm {\\sim cm}$ 。", "type": "paragraph", "position": [181, 774, 923, 774, 923, 793, 181, 793], "content": 0, "sub_type": "text"}, {"paragraph_id": 10, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "3.1 石油醚：沸程30 $^{\\circ }C\\sim 60^{\\circ }C。$ ", "type": "paragraph", "position": [138, 803, 441, 803, 441, 829, 138, 829], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 3, "tags": [], "outline_level": -1, "text": "3.2 冰乙酸：优级纯。", "type": "paragraph", "position": [138, 838, 337, 838, 337, 857, 138, 857], "content": 0, "sub_type": "text"}, {"paragraph_id": 12, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "3.3 $3\\%$ （体积分数）冰乙酸溶液：取冰乙酸 $(3.2)3mL$ ，加水并定容至 $100mL$ 。", "type": "paragraph", "position": [138, 870, 844, 870, 844, 889, 138, 889], "content": 0, "sub_type": "text"}, {"paragraph_id": 13, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "3.4 氯酸钾标准物质：纯度大于 $99\\%$ 。", "type": "paragraph", "position": [138, 902, 488, 902, 488, 921, 138, 921], "content": 0, "sub_type": "text"}, {"paragraph_id": 14, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "3.5 氯酸根离子（ $\\mathrm {ClO}_{3}^{-}$ ）标准储备液 $(1.0mg/mL)$ ：准确称取 $0.1467g$ 氯酸钾（3.4)，用水溶解并定容至 $100mL$ ，配成含 $\\mathrm {ClO}_{3}^{-}$ $1.0mg/mL$ 标准储备液，0 $^{\\circ }C\\sim 4^{\\circ }$ 保存。此溶液也可采用有证标准物质溶液。", "type": "paragraph", "position": [138, 933, 1067, 933, 1067, 1021, 138, 1021], "content": 0, "sub_type": "text"}, {"paragraph_id": 15, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "3.6 氯酸根离子（ $\\mathrm {ClO}_{3}^{-}$ ）标准中间液：准确吸取 $2.0mL$ 的氯酸根标准储备液（3.5）到 $100mL$ 容量瓶，用水定容，摇匀备用， $\\mathrm {ClO}_{3}^{-}$ 浓度为20mg/L,0 $^{\\circ }C\\sim 4^{\\circ }C$ 保存。", "type": "paragraph", "position": [138, 1033, 1066, 1033, 1066, 1092, 138, 1092], "content": 0, "sub_type": "text"}, {"paragraph_id": 16, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "3.7 氯酸根离子（ $\\mathrm {ClO}_{3}^{-}$ ）标准工作溶液：根据需要用水将标准应用液（3.6）稀释成 $0.025mg/L$ 、 $0.05mg/L$ 、 $0.10mg/L、0.20mg/L、0.40mg/L、1.00mg/L$  和 $2.00mg/L$ 标准工作曲线溶液。现配现用。", "type": "paragraph", "position": [138, 1103, 1067, 1103, 1067, 1187, 138, 1187], "content": 0, "sub_type": "text"}, {"paragraph_id": 17, "page_id": 3, "tags": [], "outline_level": 1, "text": "4 仪器和设备", "type": "paragraph", "position": [138, 1232, 280, 1232, 280, 1251, 138, 1251], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 18, "page_id": 3, "tags": [], "outline_level": -1, "text": "4.1 离子色谱仪：电导检测器，配梯度泵，自动淋洗液发生器。", "type": "paragraph", "position": [138, 1295, 694, 1295, 694, 1314, 138, 1314], "content": 0, "sub_type": "text"}, {"paragraph_id": 19, "page_id": 3, "tags": [], "outline_level": -1, "text": "4.2 组织捣碎机。", "type": "paragraph", "position": [138, 1327, 306, 1327, 306, 1346, 138, 1346], "content": 0, "sub_type": "text"}, {"paragraph_id": 20, "page_id": 3, "tags": [], "outline_level": -1, "text": "4.3 搅拌器。", "type": "paragraph", "position": [138, 1359, 263, 1359, 263, 1378, 138, 1378], "content": 0, "sub_type": "text"}, {"paragraph_id": 21, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "4.4 分析天平：感量为 $0.2mg$ 。", "type": "paragraph", "position": [138, 1391, 426, 1391, 426, 1410, 138, 1410], "content": 0, "sub_type": "text"}, {"paragraph_id": 22, "page_id": 3, "tags": [], "outline_level": -1, "text": "4.5 超声波提取器。", "type": "paragraph", "position": [138, 1423, 326, 1423, 326, 1442, 138, 1442], "content": 0, "sub_type": "text"}, {"paragraph_id": 23, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "**4.6** 离心机： $4500r/\\min$ ，适配 $50mL$ 离心管。", "type": "paragraph", "position": [138, 1454, 569, 1454, 569, 1474, 138, 1474], "content": 0, "sub_type": "text"}, {"paragraph_id": 24, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "**4.7** 固相萃取 $\\mathrm {C}_{18}$ 柱： $200mg/3mL$ mL或相当者。", "type": "paragraph", "position": [138, 1486, 563, 1486, 563, 1509, 138, 1509], "content": 0, "sub_type": "text"}, {"paragraph_id": 25, "page_id": 3, "tags": ["formula"], "outline_level": -1, "text": "4.8 微孔滤膜： $0.45μm$ ，水系。", "type": "paragraph", "position": [138, 1520, 421, 1520, 421, 1539, 138, 1539], "content": 0, "sub_type": "text"}, {"paragraph_id": 26, "page_id": 3, "tags": [], "outline_level": -1, "text": "1", "type": "paragraph", "position": [1025, 1551, 1038, 1551, 1038, 1571, 1025, 1571], "content": 1, "sub_type": "footer"}, {"paragraph_id": 1, "page_id": 4, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [122, 145, 293, 145, 293, 161, 122, 161], "content": 1, "sub_type": "header"}, {"paragraph_id": 0, "page_id": 4, "tags": [], "outline_level": 1, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [234, 14, 957, 14, 957, 53, 234, 53], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 3, "page_id": 4, "tags": [], "outline_level": 1, "text": "5 试样制备和保存", "type": "paragraph", "position": [122, 231, 306, 231, 306, 250, 122, 250], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 4, "page_id": 4, "tags": [], "outline_level": 2, "text": "5.1 一般要求", "type": "paragraph", "position": [122, 287, 261, 287, 261, 306, 122, 306], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 5, "page_id": 4, "tags": [], "outline_level": -1, "text": "在抽样和制样过程中，应防止样品受到污染或发生残留量的变化。", "type": "paragraph", "position": [164, 333, 755, 333, 755, 353, 164, 353], "content": 0, "sub_type": "text"}, {"paragraph_id": 6, "page_id": 4, "tags": [], "outline_level": 2, "text": "5.2 制备与保存", "type": "paragraph", "position": [122, 379, 283, 379, 283, 398, 122, 398], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 7, "page_id": 4, "tags": [], "outline_level": 3, "text": "5.2.1 鲜龙眼、龙眼罐头、龙眼干、芒果、土豆、小麦粉、大米、猪肉和鱼", "type": "paragraph", "position": [122, 425, 750, 425, 750, 445, 122, 445], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 8, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "从所取全部样品中取出有代表性样品约 $500g$ ，取可食用部分经捣碎机（4.2）充分捣碎均匀，均分成两份，分别装入洁净容器内作为试样，密封并标明标记。试样在C0 $^{\\circ }C\\sim 4^{\\circ }C$ 冷藏保存。", "type": "paragraph", "position": [123, 471, 1053, 471, 1053, 522, 123, 522], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 4, "tags": [], "outline_level": 3, "text": "5.2.2 芒果汁、苹果汁、混合果蔬汁、葡萄酒", "type": "paragraph", "position": [122, 547, 520, 547, 520, 566, 122, 566], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 10, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "从所取全部样品中取出有代表性样品约 $500g$ ，均分成两份，分别装入洁净容器内作为试样，密封并标明标记。试样在 $0^{\\circ }C\\sim 4^{\\circ }C$ 冷藏保存。", "type": "paragraph", "position": [123, 592, 1051, 592, 1051, 643, 123, 643], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 4, "tags": [], "outline_level": 3, "text": "5.2.3 啤酒", "type": "paragraph", "position": [122, 670, 237, 670, 237, 689, 122, 689], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 12, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "从所取全部样品中取出有代表性样品约 $500g$ ，以搅拌器（4.3）充分搅拌，除去二氧化碳，均分成两份，分别装入洁净容器内作为试样，密封并标明标记。试样在0 $^{\\circ }C\\sim 4^{\\circ }C$ 冷藏保存。", "type": "paragraph", "position": [123, 715, 1051, 715, 1051, 768, 123, 768], "content": 0, "sub_type": "text"}, {"paragraph_id": 13, "page_id": 4, "tags": [], "outline_level": 1, "text": "6 测定步骤", "type": "paragraph", "position": [122, 809, 245, 809, 245, 826, 122, 826], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 14, "page_id": 4, "tags": [], "outline_level": 2, "text": "6.1 试样的处理", "type": "paragraph", "position": [122, 864, 283, 864, 283, 883, 122, 883], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 15, "page_id": 4, "tags": [], "outline_level": 3, "text": "6.1.1 鲜龙眼、龙眼罐头、龙眼干、芒果、土豆、芒果汁、苹果汁、混合果蔬汁、葡萄酒、啤酒、小麦粉和大米", "type": "paragraph", "position": [122, 910, 1026, 910, 1026, 929, 122, 929], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 16, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "称取试样 $5.0g$ （龙眼干 $1.0g)$ ，精确至 $0.0002g$ ，置于 $50mL$ 的容量瓶中，加入约 $30mL$ 水，摇匀。超声提取 $30min$ ，用水定容。转移试液至 $50mL$ 离心管，以 $4500r/\\min$ 速度离心 $10\\min$ 。取上清液备用。", "type": "paragraph", "position": [123, 956, 1051, 956, 1051, 1036, 123, 1036], "content": 0, "sub_type": "text"}, {"paragraph_id": 17, "page_id": 4, "tags": [], "outline_level": 4, "text": "6.1.2 牛奶", "type": "paragraph", "position": [122, 1063, 237, 1063, 237, 1083, 122, 1083], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 18, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "称取试样（牛奶： $5.0g$ ，精确至 $0.0002g)$  ，置于 $50mL$ 容量瓶中，加入约 $30mL$ 水，摇匀，超声提取 $30min$ 。加入 $3\\%$  （体积分数）乙酸溶液 $(3.3)4mL$ ，摇匀，于 $4^{\\circ }C$ 放置 $20min$ 。取出放置至室温，用水定容。转移试液至 $50mL$ 离心管中，以 $4500r/\\min$ 速度离心 $10min$ 。取上清液备用。", "type": "paragraph", "position": [123, 1108, 1051, 1108, 1051, 1188, 123, 1188], "content": 0, "sub_type": "text"}, {"paragraph_id": 19, "page_id": 4, "tags": [], "outline_level": 4, "text": "6.1.3 猪肉和鱼", "type": "paragraph", "position": [122, 1215, 278, 1215, 278, 1234, 122, 1234], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 20, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "称取试样 $5.0g$ （精确至 $0.0002g)$  于烧杯中，加入 $25mL$ 石油醚（3.1)，搅拌，倾出上层石油醚。再次加 $25mL$  石油醚（3.1)，搅拌，倾出上层石油醚，水浴上微微加热挥干石油醚。将试样转移至 $50mL$  容量瓶中，加入约 $30mL$ 水，摇匀，超声提取 $30min$ ，用水定容。转移试液至 $50mL$ 离心管中，以 $4500r/\\min$ 速度离心 $10min$ 。取上清液备用。", "type": "paragraph", "position": [122, 1260, 1051, 1260, 1051, 1371, 122, 1371], "content": 0, "sub_type": "text"}, {"paragraph_id": 21, "page_id": 4, "tags": [], "outline_level": 2, "text": "6.2 净化", "type": "paragraph", "position": [122, 1398, 219, 1398, 219, 1417, 122, 1417], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 22, "page_id": 4, "tags": ["formula"], "outline_level": -1, "text": "固相萃取 $\\mathrm {C}_{18}$ 柱（4.7）使用前依次用 $10mL$ 甲醇、 $15mL$ 水通过，活化 $30\\min$ 。将6.1中上清液经 $\\mathrm {C}_{18}$ 柱净化，再经 $0.45\\mu m$ 微孔滤膜（4.8）过滤，收集滤液待测。", "type": "paragraph", "position": [123, 1442, 1051, 1442, 1051, 1498, 123, 1498], "content": 0, "sub_type": "text"}, {"paragraph_id": 23, "page_id": 4, "tags": [], "outline_level": -1, "text": "2", "type": "paragraph", "position": [150, 1552, 163, 1552, 163, 1571, 150, 1571], "content": 1, "sub_type": "footer"}, {"paragraph_id": 0, "page_id": 5, "tags": [], "outline_level": 2, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [232, 14, 955, 14, 955, 53, 232, 53], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 1, "page_id": 5, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [896, 145, 1067, 145, 1067, 161, 896, 161], "content": 0, "sub_type": "text"}, {"paragraph_id": 2, "page_id": 5, "tags": [], "outline_level": 3, "text": "6.3 测定", "type": "paragraph", "position": [136, 215, 235, 215, 235, 233, 136, 233], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 3, "page_id": 5, "tags": [], "outline_level": 4, "text": "6.3.1 离子色谱参考条件", "type": "paragraph", "position": [136, 261, 378, 261, 378, 280, 136, 280], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 4, "page_id": 5, "tags": [], "outline_level": -1, "text": "离子色谱参考条件如下：", "type": "paragraph", "position": [181, 306, 405, 306, 405, 326, 181, 326], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "a）色谱柱：阴离子交换色谱柱［分析柱：DIONEX IonPacAS19(4 $\\mathrm {mm}x250$ mm)，保护柱：DI-ONEX IonPac AG19(4 $mm\\times 50mm)$ ]，或效能相当者；", "type": "paragraph", "position": [179, 336, 1066, 336, 1066, 386, 179, 386], "content": 0, "sub_type": "text"}, {"paragraph_id": 6, "page_id": 5, "tags": [], "outline_level": -1, "text": "b）淋洗液：由自动淋洗液发生器产生氢氧化钾，梯度如表1；", "type": "paragraph", "position": [181, 397, 733, 397, 733, 416, 181, 416], "content": 0, "sub_type": "text"}, {"paragraph_id": 7, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "c）流速： $0.8mL/\\min$ ；", "type": "paragraph", "position": [181, 429, 400, 429, 400, 448, 181, 448], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "d）抑制电流： $100mA$ ；", "type": "paragraph", "position": [180, 456, 400, 459, 401, 478, 181, 476], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "e）检测池温度： $30^{\\circ }C$ ；", "type": "paragraph", "position": [181, 489, 401, 489, 401, 507, 181, 507], "content": 0, "sub_type": "text"}, {"paragraph_id": 10, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "f）进样量 $100\\mu L$ 。", "type": "paragraph", "position": [179, 519, 373, 519, 373, 537, 179, 537], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 5, "tags": [], "outline_level": -1, "text": "**表1 淋洗液梯度淋洗程序**", "type": "paragraph", "position": [477, 565, 729, 565, 729, 583, 477, 583], "content": 0, "sub_type": "table_title"}, {"paragraph_id": 12, "page_id": 5, "content": 0, "outline_level": -1, "text": "<table border=\"1\" ><tr>\n<td colspan=\"1\" rowspan=\"1\">时间／$min$</td>\n<td colspan=\"1\" rowspan=\"1\">淋洗液浓度／($(mmol/L)$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">0 </td>\n<td colspan=\"1\" rowspan=\"1\">5 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">25 </td>\n<td colspan=\"1\" rowspan=\"1\">5 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">30 </td>\n<td colspan=\"1\" rowspan=\"1\">50 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">40 </td>\n<td colspan=\"1\" rowspan=\"1\">50 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">45 </td>\n<td colspan=\"1\" rowspan=\"1\">5 </td>\n</tr></table>", "type": "table", "caption_id": {"paragraph_id": 11, "page_id": 5}, "position": [141, 607, 1063, 608, 1063, 829, 140, 827], "cells": [{"row_span": 1, "type": "cell", "text": "时间／$min$", "page_id": 5, "col_span": 1, "position": [141, 610, 601, 610, 601, 646, 141, 646], "col": 0, "row": 0}, {"row_span": 1, "type": "cell", "text": "淋洗液浓度／($(mmol/L)$", "page_id": 5, "col_span": 1, "position": [601, 610, 1063, 610, 1063, 646, 601, 646], "col": 1, "row": 0}, {"row_span": 1, "type": "cell", "text": "0 ", "page_id": 5, "col_span": 1, "position": [141, 646, 601, 646, 601, 682, 141, 682], "col": 0, "row": 1}, {"row_span": 1, "type": "cell", "text": "5 ", "page_id": 5, "col_span": 1, "position": [601, 646, 1063, 646, 1063, 682, 601, 682], "col": 1, "row": 1}, {"row_span": 1, "type": "cell", "text": "25 ", "page_id": 5, "col_span": 1, "position": [141, 682, 601, 682, 601, 718, 141, 718], "col": 0, "row": 2}, {"row_span": 1, "type": "cell", "text": "5 ", "page_id": 5, "col_span": 1, "position": [601, 682, 1063, 682, 1063, 718, 601, 718], "col": 1, "row": 2}, {"row_span": 1, "type": "cell", "text": "30 ", "page_id": 5, "col_span": 1, "position": [141, 718, 601, 718, 601, 754, 141, 754], "col": 0, "row": 3}, {"row_span": 1, "type": "cell", "text": "50 ", "page_id": 5, "col_span": 1, "position": [601, 718, 1063, 718, 1063, 754, 601, 754], "col": 1, "row": 3}, {"row_span": 1, "type": "cell", "text": "40 ", "page_id": 5, "col_span": 1, "position": [141, 754, 601, 754, 601, 790, 141, 790], "col": 0, "row": 4}, {"row_span": 1, "type": "cell", "text": "50 ", "page_id": 5, "col_span": 1, "position": [601, 754, 1063, 754, 1063, 790, 601, 790], "col": 1, "row": 4}, {"row_span": 1, "type": "cell", "text": "45 ", "page_id": 5, "col_span": 1, "position": [141, 790, 601, 790, 601, 827, 141, 827], "col": 0, "row": 5}, {"row_span": 1, "type": "cell", "text": "5 ", "page_id": 5, "col_span": 1, "position": [601, 790, 1063, 790, 1063, 827, 601, 827], "col": 1, "row": 5}], "sub_type": "bordered"}, {"paragraph_id": 13, "page_id": 5, "tags": [], "outline_level": 4, "text": "6.3.2 离子色谱测定", "type": "paragraph", "position": [138, 871, 335, 871, 335, 890, 138, 890], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 14, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "根据样液中 $\\mathrm {ClO}_{3}^{-}$ 浓度的情况选定峰面积相近的标准溶液系列。标准溶液和样液中 $\\mathrm {ClO}_{3}^{-}$ 的响应值均应在仪器检测的线性范围内。以峰面积为纵坐标， $\\mathrm {ClO}_{3}^{-}$ 标准溶液浓度为横坐标绘制标准工作曲线。用保留时间定性，外标法定量。在上述色谱条件下 $ClO_{3}^{-}$  的参考保留时间约为 $18min$ ，色谱图参见图A.1。", "type": "paragraph", "position": [138, 916, 1066, 916, 1066, 1032, 138, 1032], "content": 0, "sub_type": "text"}, {"paragraph_id": 15, "page_id": 5, "tags": [], "outline_level": 4, "text": "6.3.3 空白试验", "type": "paragraph", "position": [136, 1060, 293, 1060, 293, 1079, 136, 1079], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 16, "page_id": 5, "tags": [], "outline_level": -1, "text": "除不加试样外，均按上述操作步骤进行。", "type": "paragraph", "position": [181, 1106, 541, 1106, 541, 1125, 181, 1125], "content": 0, "sub_type": "text"}, {"paragraph_id": 17, "page_id": 5, "tags": [], "outline_level": 1, "text": "7 结果计算", "type": "paragraph", "position": [136, 1167, 260, 1167, 260, 1186, 136, 1186], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 18, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "试样中 $\\mathrm {ClO}_{3}^{-}$ 的测定结果可按式（1）计算，计算结果需扣除空白值：", "type": "paragraph", "position": [182, 1228, 780, 1228, 780, 1255, 182, 1255], "content": 0, "sub_type": "text"}, {"paragraph_id": 19, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "$$X=\\frac {CxV}{m}\\tag{1}$$", "type": "paragraph", "position": [556, 1261, 1066, 1261, 1066, 1309, 556, 1309], "content": 0, "sub_type": "text"}, {"paragraph_id": 20, "page_id": 5, "tags": [], "outline_level": -1, "text": "式中：", "type": "paragraph", "position": [181, 1319, 237, 1319, 237, 1338, 181, 1338], "content": 0, "sub_type": "text"}, {"paragraph_id": 21, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "$X$ -试样中（ $\\mathrm {ClO}_{3}^{-}$  的含量，单位为毫克每千克 $(mg/kg)$ ", "type": "paragraph", "position": [181, 1349, 700, 1349, 700, 1372, 181, 1372], "content": 0, "sub_type": "text"}, {"paragraph_id": 22, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "$C$ -从标准曲线上查得的试样溶液（ $\\mathrm {ClO}_{3}^{-}$ 浓度，单位为毫克每升 $(mg/L)$ ;", "type": "paragraph", "position": [181, 1380, 867, 1380, 867, 1406, 181, 1406], "content": 0, "sub_type": "text"}, {"paragraph_id": 23, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "$A$ -试样溶液最终定容体积，单位为毫升（ $(mL)$ ；", "type": "paragraph", "position": [179, 1413, 638, 1413, 638, 1432, 179, 1432], "content": 0, "sub_type": "text"}, {"paragraph_id": 24, "page_id": 5, "tags": ["formula"], "outline_level": -1, "text": "$m$ -样液所代表的试样质量，单位为克 $(g)$ 。", "type": "paragraph", "position": [179, 1443, 595, 1443, 595, 1462, 179, 1462], "content": 0, "sub_type": "text"}, {"paragraph_id": 25, "page_id": 5, "tags": [], "outline_level": -1, "text": "计算结果保留3位有效数字。", "type": "paragraph", "position": [181, 1473, 447, 1473, 447, 1492, 181, 1492], "content": 0, "sub_type": "text"}, {"paragraph_id": 26, "page_id": 5, "tags": [], "outline_level": -1, "text": "3", "type": "paragraph", "position": [1025, 1552, 1038, 1552, 1038, 1571, 1025, 1571], "content": 1, "sub_type": "footer"}, {"paragraph_id": 1, "page_id": 6, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [122, 144, 293, 144, 293, 161, 122, 161], "content": 1, "sub_type": "header"}, {"paragraph_id": 0, "page_id": 6, "tags": [], "outline_level": 2, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [234, 14, 957, 14, 957, 53, 234, 53], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 3, "page_id": 6, "tags": [], "outline_level": 1, "text": "8 测定低限和回收率", "type": "paragraph", "position": [122, 231, 327, 231, 327, 249, 122, 249], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 4, "page_id": 6, "tags": [], "outline_level": 2, "text": "8.1 测定低限", "type": "paragraph", "position": [122, 286, 261, 286, 261, 305, 122, 305], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 5, "page_id": 6, "tags": ["formula"], "outline_level": -1, "text": "本标准标准曲线线性范围为 $0.025mg/L\\sim 2.0mg/L$ 。本标准对鲜龙眼、龙眼罐头、芒果、芒果汁、苹果汁、混合果蔬汁、土豆、啤酒、小麦粉、大米、牛奶、猪肉和鱼试样中 $\\mathrm {ClO}_{3}^{-}$  的测定低限为 $0.5mg/kg$ ；对葡萄酒试样中 $\\mathrm {ClO}_{3}^{-}$  的测定低限为 $1.0mg/kg$ ；；对龙眼干试样中C $\\mathrm {ClO}_{3}^{-}$  的测定低限为 $2.5mg/kg$ 。", "type": "paragraph", "position": [122, 331, 1051, 331, 1051, 413, 122, 413], "content": 0, "sub_type": "text"}, {"paragraph_id": 6, "page_id": 6, "tags": [], "outline_level": 2, "text": "8.2 回收率", "type": "paragraph", "position": [122, 433, 240, 433, 240, 451, 122, 451], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 7, "page_id": 6, "tags": [], "outline_level": -1, "text": "食品中氯酸盐不同添加水平的添加回收率范围见表2。", "type": "paragraph", "position": [166, 477, 656, 477, 656, 497, 166, 497], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 6, "tags": [], "outline_level": -1, "text": "表2 **食品中氯酸盐的添加回收率范围**", "type": "paragraph", "position": [409, 520, 765, 520, 765, 539, 409, 539], "content": 0, "sub_type": "table_title"}, {"paragraph_id": 9, "page_id": 6, "content": 0, "outline_level": -1, "text": "<table border=\"1\" ><tr>\n<td colspan=\"1\" rowspan=\"1\">基质</td>\n<td colspan=\"1\" rowspan=\"1\">加标浓度／$(mg/kg)$</td>\n<td colspan=\"1\" rowspan=\"1\">回收率／$\\%$</td>\n<td colspan=\"1\" rowspan=\"1\">基质</td>\n<td colspan=\"1\" rowspan=\"1\">加标浓度／$(mg/kg)$</td>\n<td colspan=\"1\" rowspan=\"1\">回收率／$\\%$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">鲜龙眼</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">88.6\\~100.8</td>\n<td colspan=\"1\" rowspan=\"3\">葡萄酒</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$81.9\\sim 92.1$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.6\\~103.4</td>\n<td colspan=\"1\" rowspan=\"1\">2.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$84.2\\sim 95.8$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$91.2\\sim 101.1$</td>\n<td colspan=\"1\" rowspan=\"1\">10.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$88.4\\sim 100.1$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">龙眼罐头</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">89.0\\~101.2</td>\n<td colspan=\"1\" rowspan=\"3\">啤酒</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">88.4\\~100.4</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$91.0\\sim 103.2$</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$87.3\\sim 103.2$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.8\\~101.6</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$90.0\\sim 102.1$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">龙眼干</td>\n<td colspan=\"1\" rowspan=\"1\">2.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$82.5\\sim 93.0$</td>\n<td colspan=\"1\" rowspan=\"3\">小麦粉</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$89.0\\sim 100.4$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.3\\sim 95.3$</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$90.4\\sim 104.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">25.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$87.8\\sim 97.0$</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$92.0\\sim 101.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">芒果</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$84.6\\sim 97.4$</td>\n<td colspan=\"1\" rowspan=\"3\">大米</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.4\\sim 99.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$85.6\\sim 96.2$</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$88.6\\sim 101.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$89.7\\sim 97.6$</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$88.6\\sim 100.6$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">芒果汁</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.4\\sim 98.2$</td>\n<td colspan=\"1\" rowspan=\"3\">牛奶</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.6\\sim 97.4$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$88.5\\sim 97.6$</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$89.4\\sim 102.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$90.7\\sim 100.4$</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$89.8\\sim 100.5$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">苹果汁</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$87.4\\sim 100.4$</td>\n<td colspan=\"1\" rowspan=\"3\">猪肉</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$84.4\\sim 93.6$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">89.2\\~102.3</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$87.5\\sim 96.8$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$90.9\\sim 100.4$</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$87.8\\sim 95.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">混合果蔬汁</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$84.4\\sim 98.2$</td>\n<td colspan=\"1\" rowspan=\"3\">鱼</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.4\\sim 100.2$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.5\\sim 96.3$</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$88.5\\sim 99.3$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$91.0\\sim 100.2$</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$90.6\\sim 101.0$</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">土豆</td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">$84.2\\sim 97.2$</td>\n<td colspan=\"1\" rowspan=\"3\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$86.7\\sim 96.2$</td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">$91.7\\sim 98.0$</td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n</tr></table>", "type": "table", "caption_id": {"paragraph_id": 8, "page_id": 6}, "position": [126, 563, 1051, 567, 1050, 1419, 127, 1420], "cells": [{"row_span": 1, "type": "cell", "text": "基质", "page_id": 6, "col_span": 1, "position": [125, 567, 243, 567, 243, 601, 125, 601], "col": 0, "row": 0}, {"row_span": 1, "type": "cell", "text": "加标浓度／$(mg/kg)$", "page_id": 6, "col_span": 1, "position": [243, 567, 414, 567, 414, 601, 243, 601], "col": 1, "row": 0}, {"row_span": 1, "type": "cell", "text": "回收率／$\\%$", "page_id": 6, "col_span": 1, "position": [414, 567, 586, 567, 586, 601, 414, 601], "col": 2, "row": 0}, {"row_span": 1, "type": "cell", "text": "基质", "page_id": 6, "col_span": 1, "position": [586, 567, 703, 567, 703, 601, 586, 601], "col": 3, "row": 0}, {"row_span": 1, "type": "cell", "text": "加标浓度／$(mg/kg)$", "page_id": 6, "col_span": 1, "position": [703, 567, 875, 567, 875, 601, 703, 601], "col": 4, "row": 0}, {"row_span": 1, "type": "cell", "text": "回收率／$\\%$", "page_id": 6, "col_span": 1, "position": [875, 567, 1048, 567, 1048, 601, 875, 601], "col": 5, "row": 0}, {"row_span": 3, "type": "cell", "text": "鲜龙眼", "page_id": 6, "col_span": 1, "position": [125, 601, 243, 601, 243, 703, 125, 703], "col": 0, "row": 1}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 6, "col_span": 1, "position": [243, 601, 414, 601, 414, 634, 243, 634], "col": 1, "row": 1}, {"row_span": 1, "type": "cell", "text": "88.6\\~100.8", "page_id": 6, "col_span": 1, "position": [414, 601, 586, 601, 586, 634, 414, 634], "col": 2, "row": 1}, {"row_span": 3, "type": "cell", "text": "葡萄酒", "page_id": 6, "col_span": 1, "position": [586, 601, 703, 601, 703, 703, 586, 703], "col": 3, "row": 1}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [703, 601, 875, 601, 875, 634, 703, 634], "col": 4, "row": 1}, {"row_span": 1, "type": "cell", "text": "$81.9\\sim 92.1$", "page_id": 6, "col_span": 1, "position": [875, 601, 1048, 601, 1048, 634, 875, 634], "col": 5, "row": 1}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [243, 634, 414, 634, 414, 668, 243, 668], "col": 1, "row": 2}, {"row_span": 1, "type": "cell", "text": "91.6\\~103.4", "page_id": 6, "col_span": 1, "position": [414, 634, 586, 634, 586, 668, 414, 668], "col": 2, "row": 2}, {"row_span": 1, "type": "cell", "text": "2.0 ", "page_id": 6, "col_span": 1, "position": [703, 634, 875, 634, 875, 668, 703, 668], "col": 4, "row": 2}, {"row_span": 1, "type": "cell", "text": "$84.2\\sim 95.8$", "page_id": 6, "col_span": 1, "position": [875, 634, 1048, 634, 1048, 668, 875, 668], "col": 5, "row": 2}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [243, 668, 414, 668, 414, 703, 243, 703], "col": 1, "row": 3}, {"row_span": 1, "type": "cell", "text": "$91.2\\sim 101.1$", "page_id": 6, "col_span": 1, "position": [414, 668, 586, 668, 586, 703, 414, 703], "col": 2, "row": 3}, {"row_span": 1, "type": "cell", "text": "10.0 ", "page_id": 6, "col_span": 1, "position": [703, 668, 875, 668, 875, 703, 703, 703], "col": 4, "row": 3}, {"row_span": 1, "type": "cell", "text": "$88.4\\sim 100.1$", "page_id": 6, "col_span": 1, "position": [875, 668, 1048, 668, 1048, 703, 875, 703], "col": 5, "row": 3}, {"row_span": 3, "type": "cell", "text": "龙眼罐头", "page_id": 6, "col_span": 1, "position": [125, 703, 243, 703, 243, 804, 125, 804], "col": 0, "row": 4}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 6, "col_span": 1, "position": [243, 703, 414, 703, 414, 737, 243, 737], "col": 1, "row": 4}, {"row_span": 1, "type": "cell", "text": "89.0\\~101.2", "page_id": 6, "col_span": 1, "position": [414, 703, 586, 703, 586, 737, 414, 737], "col": 2, "row": 4}, {"row_span": 3, "type": "cell", "text": "啤酒", "page_id": 6, "col_span": 1, "position": [586, 703, 703, 703, 703, 804, 586, 804], "col": 3, "row": 4}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 6, "col_span": 1, "position": [703, 703, 875, 703, 875, 737, 703, 737], "col": 4, "row": 4}, {"row_span": 1, "type": "cell", "text": "88.4\\~100.4", "page_id": 6, "col_span": 1, "position": [875, 703, 1048, 703, 1048, 737, 875, 737], "col": 5, "row": 4}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [243, 737, 414, 737, 414, 771, 243, 771], "col": 1, "row": 5}, {"row_span": 1, "type": "cell", "text": "$91.0\\sim 103.2$", "page_id": 6, "col_span": 1, "position": [414, 737, 586, 737, 586, 771, 414, 771], "col": 2, "row": 5}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [703, 737, 875, 737, 875, 771, 703, 771], "col": 4, "row": 5}, {"row_span": 1, "type": "cell", "text": "$87.3\\sim 103.2$", "page_id": 6, "col_span": 1, "position": [875, 737, 1048, 737, 1048, 771, 875, 771], "col": 5, "row": 5}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [243, 771, 414, 771, 414, 804, 243, 804], "col": 1, "row": 6}, {"row_span": 1, "type": "cell", "text": "91.8\\~101.6", "page_id": 6, "col_span": 1, "position": [414, 771, 586, 771, 586, 804, 414, 804], "col": 2, "row": 6}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [703, 771, 875, 771, 875, 804, 703, 804], "col": 4, "row": 6}, {"row_span": 1, "type": "cell", "text": "$90.0\\sim 102.1$", "page_id": 6, "col_span": 1, "position": [875, 771, 1048, 771, 1048, 804, 875, 804], "col": 5, "row": 6}, {"row_span": 3, "type": "cell", "text": "龙眼干", "page_id": 6, "col_span": 1, "position": [125, 804, 243, 804, 243, 907, 125, 907], "col": 0, "row": 7}, {"row_span": 1, "type": "cell", "text": "2.5 ", "page_id": 6, "col_span": 1, "position": [243, 804, 414, 804, 414, 839, 243, 839], "col": 1, "row": 7}, {"row_span": 1, "type": "cell", "text": "$82.5\\sim 93.0$", "page_id": 6, "col_span": 1, "position": [414, 804, 586, 804, 586, 839, 414, 839], "col": 2, "row": 7}, {"row_span": 3, "type": "cell", "text": "小麦粉", "page_id": 6, "col_span": 1, "position": [586, 804, 703, 804, 703, 907, 586, 907], "col": 3, "row": 7}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 6, "col_span": 1, "position": [703, 804, 875, 804, 875, 839, 703, 839], "col": 4, "row": 7}, {"row_span": 1, "type": "cell", "text": "$89.0\\sim 100.4$", "page_id": 6, "col_span": 1, "position": [875, 804, 1048, 804, 1048, 839, 875, 839], "col": 5, "row": 7}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [243, 839, 414, 839, 414, 873, 243, 873], "col": 1, "row": 8}, {"row_span": 1, "type": "cell", "text": "$86.3\\sim 95.3$", "page_id": 6, "col_span": 1, "position": [414, 839, 586, 839, 586, 873, 414, 873], "col": 2, "row": 8}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [703, 839, 875, 839, 875, 873, 703, 873], "col": 4, "row": 8}, {"row_span": 1, "type": "cell", "text": "$90.4\\sim 104.0$", "page_id": 6, "col_span": 1, "position": [875, 839, 1048, 839, 1048, 873, 875, 873], "col": 5, "row": 8}, {"row_span": 1, "type": "cell", "text": "25.0 ", "page_id": 6, "col_span": 1, "position": [243, 873, 414, 873, 414, 907, 243, 907], "col": 1, "row": 9}, {"row_span": 1, "type": "cell", "text": "$87.8\\sim 97.0$", "page_id": 6, "col_span": 1, "position": [414, 873, 586, 873, 586, 907, 414, 907], "col": 2, "row": 9}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [703, 873, 875, 873, 875, 907, 703, 907], "col": 4, "row": 9}, {"row_span": 1, "type": "cell", "text": "$92.0\\sim 101.0$", "page_id": 6, "col_span": 1, "position": [875, 873, 1048, 873, 1048, 907, 875, 907], "col": 5, "row": 9}, {"row_span": 3, "type": "cell", "text": "芒果", "page_id": 6, "col_span": 1, "position": [125, 907, 243, 907, 243, 1009, 125, 1009], "col": 0, "row": 10}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 6, "col_span": 1, "position": [243, 907, 414, 907, 414, 941, 243, 941], "col": 1, "row": 10}, {"row_span": 1, "type": "cell", "text": "$84.6\\sim 97.4$", "page_id": 6, "col_span": 1, "position": [414, 907, 586, 907, 586, 941, 414, 941], "col": 2, "row": 10}, {"row_span": 3, "type": "cell", "text": "大米", "page_id": 6, "col_span": 1, "position": [586, 907, 703, 907, 703, 1009, 586, 1009], "col": 3, "row": 10}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 6, "col_span": 1, "position": [703, 907, 875, 907, 875, 941, 703, 941], "col": 4, "row": 10}, {"row_span": 1, "type": "cell", "text": "$86.4\\sim 99.0$", "page_id": 6, "col_span": 1, "position": [875, 907, 1048, 907, 1048, 941, 875, 941], "col": 5, "row": 10}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [243, 941, 414, 941, 414, 975, 243, 975], "col": 1, "row": 11}, {"row_span": 1, "type": "cell", "text": "$85.6\\sim 96.2$", "page_id": 6, "col_span": 1, "position": [414, 941, 586, 941, 586, 975, 414, 975], "col": 2, "row": 11}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [703, 941, 875, 941, 875, 975, 703, 975], "col": 4, "row": 11}, {"row_span": 1, "type": "cell", "text": "$88.6\\sim 101.0$", "page_id": 6, "col_span": 1, "position": [875, 941, 1048, 941, 1048, 975, 875, 975], "col": 5, "row": 11}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [243, 975, 414, 975, 414, 1009, 243, 1009], "col": 1, "row": 12}, {"row_span": 1, "type": "cell", "text": "$89.7\\sim 97.6$", "page_id": 6, "col_span": 1, "position": [414, 975, 586, 975, 586, 1009, 414, 1009], "col": 2, "row": 12}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [703, 975, 875, 975, 875, 1009, 703, 1009], "col": 4, "row": 12}, {"row_span": 1, "type": "cell", "text": "$88.6\\sim 100.6$", "page_id": 6, "col_span": 1, "position": [875, 975, 1048, 975, 1048, 1009, 875, 1009], "col": 5, "row": 12}, {"row_span": 3, "type": "cell", "text": "芒果汁", "page_id": 6, "col_span": 1, "position": [125, 1009, 243, 1009, 243, 1111, 125, 1111], "col": 0, "row": 13}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 6, "col_span": 1, "position": [243, 1009, 414, 1009, 414, 1042, 243, 1042], "col": 1, "row": 13}, {"row_span": 1, "type": "cell", "text": "$86.4\\sim 98.2$", "page_id": 6, "col_span": 1, "position": [414, 1009, 586, 1009, 586, 1042, 414, 1042], "col": 2, "row": 13}, {"row_span": 3, "type": "cell", "text": "牛奶", "page_id": 6, "col_span": 1, "position": [586, 1009, 703, 1009, 703, 1111, 586, 1111], "col": 3, "row": 13}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 6, "col_span": 1, "position": [703, 1009, 875, 1009, 875, 1042, 703, 1042], "col": 4, "row": 13}, {"row_span": 1, "type": "cell", "text": "$86.6\\sim 97.4$", "page_id": 6, "col_span": 1, "position": [875, 1009, 1048, 1009, 1048, 1042, 875, 1042], "col": 5, "row": 13}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [243, 1042, 414, 1042, 414, 1077, 243, 1077], "col": 1, "row": 14}, {"row_span": 1, "type": "cell", "text": "$88.5\\sim 97.6$", "page_id": 6, "col_span": 1, "position": [414, 1042, 586, 1042, 586, 1077, 414, 1077], "col": 2, "row": 14}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [703, 1042, 875, 1042, 875, 1077, 703, 1077], "col": 4, "row": 14}, {"row_span": 1, "type": "cell", "text": "$89.4\\sim 102.0$", "page_id": 6, "col_span": 1, "position": [875, 1042, 1048, 1042, 1048, 1077, 875, 1077], "col": 5, "row": 14}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [243, 1077, 414, 1077, 414, 1111, 243, 1111], "col": 1, "row": 15}, {"row_span": 1, "type": "cell", "text": "$90.7\\sim 100.4$", "page_id": 6, "col_span": 1, "position": [414, 1077, 586, 1077, 586, 1111, 414, 1111], "col": 2, "row": 15}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [703, 1077, 875, 1077, 875, 1111, 703, 1111], "col": 4, "row": 15}, {"row_span": 1, "type": "cell", "text": "$89.8\\sim 100.5$", "page_id": 6, "col_span": 1, "position": [875, 1077, 1048, 1077, 1048, 1111, 875, 1111], "col": 5, "row": 15}, {"row_span": 3, "type": "cell", "text": "苹果汁", "page_id": 6, "col_span": 1, "position": [125, 1111, 243, 1111, 243, 1213, 125, 1213], "col": 0, "row": 16}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 6, "col_span": 1, "position": [243, 1111, 414, 1111, 414, 1145, 243, 1145], "col": 1, "row": 16}, {"row_span": 1, "type": "cell", "text": "$87.4\\sim 100.4$", "page_id": 6, "col_span": 1, "position": [414, 1111, 586, 1111, 586, 1145, 414, 1145], "col": 2, "row": 16}, {"row_span": 3, "type": "cell", "text": "猪肉", "page_id": 6, "col_span": 1, "position": [586, 1111, 703, 1111, 703, 1213, 586, 1213], "col": 3, "row": 16}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 6, "col_span": 1, "position": [703, 1111, 875, 1111, 875, 1145, 703, 1145], "col": 4, "row": 16}, {"row_span": 1, "type": "cell", "text": "$84.4\\sim 93.6$", "page_id": 6, "col_span": 1, "position": [875, 1111, 1048, 1111, 1048, 1145, 875, 1145], "col": 5, "row": 16}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [243, 1145, 414, 1145, 414, 1179, 243, 1179], "col": 1, "row": 17}, {"row_span": 1, "type": "cell", "text": "89.2\\~102.3", "page_id": 6, "col_span": 1, "position": [414, 1145, 586, 1145, 586, 1179, 414, 1179], "col": 2, "row": 17}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [703, 1145, 875, 1145, 875, 1179, 703, 1179], "col": 4, "row": 17}, {"row_span": 1, "type": "cell", "text": "$87.5\\sim 96.8$", "page_id": 6, "col_span": 1, "position": [875, 1145, 1048, 1145, 1048, 1179, 875, 1179], "col": 5, "row": 17}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [243, 1179, 414, 1179, 414, 1213, 243, 1213], "col": 1, "row": 18}, {"row_span": 1, "type": "cell", "text": "$90.9\\sim 100.4$", "page_id": 6, "col_span": 1, "position": [414, 1179, 586, 1179, 586, 1213, 414, 1213], "col": 2, "row": 18}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [703, 1179, 875, 1179, 875, 1213, 703, 1213], "col": 4, "row": 18}, {"row_span": 1, "type": "cell", "text": "$87.8\\sim 95.0$", "page_id": 6, "col_span": 1, "position": [875, 1179, 1048, 1179, 1048, 1213, 875, 1213], "col": 5, "row": 18}, {"row_span": 3, "type": "cell", "text": "混合果蔬汁", "page_id": 6, "col_span": 1, "position": [125, 1213, 243, 1213, 243, 1316, 125, 1316], "col": 0, "row": 19}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 6, "col_span": 1, "position": [243, 1213, 414, 1213, 414, 1247, 243, 1247], "col": 1, "row": 19}, {"row_span": 1, "type": "cell", "text": "$84.4\\sim 98.2$", "page_id": 6, "col_span": 1, "position": [414, 1213, 586, 1213, 586, 1247, 414, 1247], "col": 2, "row": 19}, {"row_span": 3, "type": "cell", "text": "鱼", "page_id": 6, "col_span": 1, "position": [586, 1213, 703, 1213, 703, 1316, 586, 1316], "col": 3, "row": 19}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 6, "col_span": 1, "position": [703, 1213, 875, 1213, 875, 1247, 703, 1247], "col": 4, "row": 19}, {"row_span": 1, "type": "cell", "text": "$86.4\\sim 100.2$", "page_id": 6, "col_span": 1, "position": [875, 1213, 1048, 1213, 1048, 1247, 875, 1247], "col": 5, "row": 19}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [243, 1247, 414, 1247, 414, 1281, 243, 1281], "col": 1, "row": 20}, {"row_span": 1, "type": "cell", "text": "$86.5\\sim 96.3$", "page_id": 6, "col_span": 1, "position": [414, 1247, 586, 1247, 586, 1281, 414, 1281], "col": 2, "row": 20}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [703, 1247, 875, 1247, 875, 1281, 703, 1281], "col": 4, "row": 20}, {"row_span": 1, "type": "cell", "text": "$88.5\\sim 99.3$", "page_id": 6, "col_span": 1, "position": [875, 1247, 1048, 1247, 1048, 1281, 875, 1281], "col": 5, "row": 20}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [243, 1281, 414, 1281, 414, 1316, 243, 1316], "col": 1, "row": 21}, {"row_span": 1, "type": "cell", "text": "$91.0\\sim 100.2$", "page_id": 6, "col_span": 1, "position": [414, 1281, 586, 1281, 586, 1316, 414, 1316], "col": 2, "row": 21}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [703, 1281, 875, 1281, 875, 1316, 703, 1316], "col": 4, "row": 21}, {"row_span": 1, "type": "cell", "text": "$90.6\\sim 101.0$", "page_id": 6, "col_span": 1, "position": [875, 1281, 1048, 1281, 1048, 1316, 875, 1316], "col": 5, "row": 21}, {"row_span": 3, "type": "cell", "text": "土豆", "page_id": 6, "col_span": 1, "position": [125, 1316, 243, 1316, 243, 1418, 125, 1418], "col": 0, "row": 22}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 6, "col_span": 1, "position": [243, 1316, 414, 1316, 414, 1350, 243, 1350], "col": 1, "row": 22}, {"row_span": 1, "type": "cell", "text": "$84.2\\sim 97.2$", "page_id": 6, "col_span": 1, "position": [414, 1316, 586, 1316, 586, 1350, 414, 1350], "col": 2, "row": 22}, {"row_span": 3, "type": "cell", "text": "", "page_id": 6, "col_span": 1, "position": [586, 1316, 703, 1316, 703, 1418, 586, 1418], "col": 3, "row": 22}, {"row_span": 1, "type": "cell", "text": "", "page_id": 6, "col_span": 1, "position": [703, 1316, 875, 1316, 875, 1350, 703, 1350], "col": 4, "row": 22}, {"row_span": 1, "type": "cell", "text": "", "page_id": 6, "col_span": 1, "position": [875, 1316, 1048, 1316, 1048, 1350, 875, 1350], "col": 5, "row": 22}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 6, "col_span": 1, "position": [243, 1350, 414, 1350, 414, 1384, 243, 1384], "col": 1, "row": 23}, {"row_span": 1, "type": "cell", "text": "$86.7\\sim 96.2$", "page_id": 6, "col_span": 1, "position": [414, 1350, 586, 1350, 586, 1384, 414, 1384], "col": 2, "row": 23}, {"row_span": 1, "type": "cell", "text": "", "page_id": 6, "col_span": 1, "position": [703, 1350, 875, 1350, 875, 1384, 703, 1384], "col": 4, "row": 23}, {"row_span": 1, "type": "cell", "text": "", "page_id": 6, "col_span": 1, "position": [875, 1350, 1048, 1350, 1048, 1384, 875, 1384], "col": 5, "row": 23}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 6, "col_span": 1, "position": [243, 1384, 414, 1384, 414, 1418, 243, 1418], "col": 1, "row": 24}, {"row_span": 1, "type": "cell", "text": "$91.7\\sim 98.0$", "page_id": 6, "col_span": 1, "position": [414, 1384, 586, 1384, 586, 1418, 414, 1418], "col": 2, "row": 24}, {"row_span": 1, "type": "cell", "text": "", "page_id": 6, "col_span": 1, "position": [703, 1384, 875, 1384, 875, 1418, 703, 1418], "col": 4, "row": 24}, {"row_span": 1, "type": "cell", "text": "", "page_id": 6, "col_span": 1, "position": [875, 1384, 1048, 1384, 1048, 1418, 875, 1418], "col": 5, "row": 24}], "sub_type": "bordered"}, {"paragraph_id": 10, "page_id": 6, "tags": [], "outline_level": 1, "text": "9 精密度", "type": "paragraph", "position": [122, 1460, 224, 1460, 224, 1477, 122, 1477], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 11, "page_id": 6, "tags": ["formula"], "outline_level": -1, "text": "在重复性条件下获得的两次独立测定结果的绝对差值不得超过算术平均值的 $10\\%$ 。", "type": "paragraph", "position": [164, 1519, 918, 1519, 918, 1539, 164, 1539], "content": 0, "sub_type": "text"}, {"paragraph_id": 12, "page_id": 6, "tags": [], "outline_level": -1, "text": "4", "type": "paragraph", "position": [150, 1552, 164, 1552, 164, 1571, 150, 1571], "content": 1, "sub_type": "footer"}, {"paragraph_id": 0, "page_id": 7, "tags": [], "outline_level": 1, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [232, 14, 955, 14, 955, 53, 232, 53], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 1, "page_id": 7, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [896, 145, 1067, 145, 1067, 161, 896, 161], "content": 0, "sub_type": "text"}, {"paragraph_id": 2, "page_id": 7, "tags": [], "outline_level": 1, "text": "附录A", "type": "paragraph", "position": [551, 248, 656, 248, 656, 267, 551, 267], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 3, "page_id": 7, "tags": [], "outline_level": 1, "text": "（资料性附录）", "type": "paragraph", "position": [538, 279, 666, 279, 666, 297, 538, 297], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 4, "page_id": 7, "tags": [], "outline_level": 1, "text": "标准物质色谱图", "type": "paragraph", "position": [526, 310, 678, 310, 678, 329, 526, 329], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 5, "page_id": 7, "content": 0, "outline_level": -1, "text": "4.00 $\\mathrm {ClO}_{3}^{-}$ -0.30 1.8 5.0 7.5 10.0 12.5 15.0 17.5 20.0 22.5 25.0 27.5 30.0 32.5 35.0 39.5 min", "type": "image", "image_url": "https://web-api.textin.com/ocr_image/external/44bd9241892ee820.jpg", "position": [260, 378, 953, 380, 953, 553, 259, 553], "caption_id": {"paragraph_id": 6, "page_id": 7}, "sub_type": "chart"}, {"paragraph_id": 6, "page_id": 7, "tags": ["formula"], "outline_level": -1, "text": "**图A.1** $2.0mg/LClO_{3}^{-}$ **标准物质的离子色谱图**", "type": "paragraph", "position": [383, 583, 822, 583, 822, 602, 383, 602], "content": 0, "sub_type": "image_title"}, {"paragraph_id": 7, "page_id": 7, "tags": [], "outline_level": -1, "text": "<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>", "type": "paragraph", "position": [486, 649, 720, 649, 720, 670, 486, 670], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 7, "tags": [], "outline_level": -1, "text": "5", "type": "paragraph", "position": [1025, 1551, 1038, 1551, 1038, 1571, 1025, 1571], "content": 1, "sub_type": "footer"}, {"paragraph_id": 0, "page_id": 8, "tags": [], "outline_level": 1, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [234, 14, 957, 14, 957, 53, 234, 53], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 1, "page_id": 8, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [122, 145, 293, 145, 293, 161, 122, 161], "content": 0, "sub_type": "text"}, {"paragraph_id": 2, "page_id": 8, "tags": [], "outline_level": 1, "text": "Forew<PERSON>", "type": "paragraph", "position": [506, 247, 669, 249, 669, 277, 506, 275], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 3, "page_id": 8, "tags": [], "outline_level": -1, "text": "This standard was drafted in accordance with the GB/T 1.1-2009.", "type": "paragraph", "position": [123, 352, 742, 352, 742, 368, 123, 368], "content": 0, "sub_type": "text"}, {"paragraph_id": 4, "page_id": 8, "tags": [], "outline_level": -1, "text": "Please note that some of the content of the standard may involve patents.Publication of the present the standard does not bear the responsibility of identifying these patents.", "type": "paragraph", "position": [123, 414, 1051, 414, 1051, 464, 123, 464], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 8, "tags": [], "outline_level": -1, "text": "This standard was proposed by and is under the jurisdiction of Certifercation and Accreditation Ad-ministration of the People's Republic of China.", "type": "paragraph", "position": [123, 508, 1049, 508, 1049, 558, 123, 558], "content": 0, "sub_type": "text"}, {"paragraph_id": 6, "page_id": 8, "tags": [], "outline_level": -1, "text": "This standard was drafted by Guangdong Entry-Exit Inspection and Quarantine Bureau of the People's Republic of China.", "type": "paragraph", "position": [123, 602, 1051, 602, 1051, 651, 123, 651], "content": 0, "sub_type": "text"}, {"paragraph_id": 7, "page_id": 8, "tags": [], "outline_level": -1, "text": "This standard was mainly drafted by <PERSON>,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>, <PERSON>.", "type": "paragraph", "position": [122, 698, 1048, 698, 1048, 748, 122, 748], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 8, "tags": [], "outline_level": -1, "text": "Note:This English version,a translation from the Chinese text, is solely for guidance.", "type": "paragraph", "position": [158, 1520, 821, 1520, 821, 1536, 158, 1536], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 8, "tags": [], "outline_level": -1, "text": "6", "type": "paragraph", "position": [150, 1552, 164, 1552, 164, 1571, 150, 1571], "content": 1, "sub_type": "footer"}, {"paragraph_id": 0, "page_id": 9, "tags": [], "outline_level": 1, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [232, 14, 955, 14, 955, 53, 232, 53], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 1, "page_id": 9, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [896, 145, 1067, 145, 1067, 161, 896, 161], "content": 0, "sub_type": "text"}, {"paragraph_id": 2, "page_id": 9, "tags": [], "outline_level": 1, "text": "Determination of chlorate in food for export-", "type": "paragraph", "position": [204, 249, 1002, 249, 1002, 281, 204, 281], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 3, "page_id": 9, "tags": [], "outline_level": 1, "text": "lon chromatography method", "type": "paragraph", "position": [365, 297, 842, 297, 842, 329, 365, 329], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 4, "page_id": 9, "tags": [], "outline_level": 2, "text": "1 Scope", "type": "paragraph", "position": [139, 397, 246, 399, 247, 418, 139, 416], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 5, "page_id": 9, "tags": [], "outline_level": -1, "text": "This standard specifies the method for the determination of chlorate in food for export by lon chro-matography method.", "type": "paragraph", "position": [140, 483, 1066, 483, 1066, 535, 140, 535], "content": 0, "sub_type": "text"}, {"paragraph_id": 6, "page_id": 9, "tags": [], "outline_level": -1, "text": "This standard is applicable to the determination of chlorate in Fresh longan, canned longan,dried lon-gan, mango, mango juice,apple juice, mixed fruit and vegetables juice, potato, wine,beer,flour,rice,milk,pork,fish.", "type": "paragraph", "position": [137, 583, 1066, 583, 1066, 668, 137, 668], "content": 0, "sub_type": "text"}, {"paragraph_id": 7, "page_id": 9, "tags": [], "outline_level": 2, "text": "2 Principle", "type": "paragraph", "position": [137, 731, 276, 733, 276, 752, 138, 750], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 8, "page_id": 9, "tags": [], "outline_level": -1, "text": "Chlorate residues are extracted from the sample by water. It is cleaned up with solid phase extraction column. It is determined by lon chromatography equipped with conductance detector,quantified by external standard method.", "type": "paragraph", "position": [138, 817, 1066, 817, 1066, 903, 138, 903], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 9, "tags": [], "outline_level": 2, "text": "3 Reagents and materials", "type": "paragraph", "position": [137, 965, 438, 967, 439, 989, 138, 987], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 10, "page_id": 9, "tags": [], "outline_level": -1, "text": "Unless otherwise specified, the reagents used should be analytical grade. And the water should be ul-tra-pure grade water with 18.2 MΩ·cm resistivity.", "type": "paragraph", "position": [137, 1050, 1066, 1050, 1066, 1104, 137, 1104], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 9, "tags": [], "outline_level": -1, "text": "3.1 Petroleum ether:Boiling range (30 ℃\\~60 ℃).", "type": "paragraph", "position": [138, 1152, 628, 1152, 628, 1171, 138, 1171], "content": 0, "sub_type": "text"}, {"paragraph_id": 12, "page_id": 9, "tags": [], "outline_level": -1, "text": "**3.2** Acetic acid:Guaranteed reagent.", "type": "paragraph", "position": [137, 1216, 486, 1219, 487, 1237, 138, 1234], "content": 0, "sub_type": "text"}, {"paragraph_id": 13, "page_id": 9, "tags": [], "outline_level": -1, "text": "**3.3** 3%(V/V)Acetic acid solution:Pipette 3.0 mL acetic acid and dilute to 100 mL with water.", "type": "paragraph", "position": [137, 1283, 1027, 1285, 1028, 1304, 138, 1303], "content": 0, "sub_type": "text"}, {"paragraph_id": 14, "page_id": 9, "tags": [], "outline_level": -1, "text": "**3.4** Chlorate potassium standard:Purity was 99% above.", "type": "paragraph", "position": [137, 1351, 675, 1353, 676, 1372, 138, 1370], "content": 0, "sub_type": "text"}, {"paragraph_id": 15, "page_id": 9, "tags": [], "outline_level": -1, "text": "**3.5** Standard stock solution: Accurately weigh 0.146 7 g chlorate potassium (3.4),dissolve and quantitatively with water. The concentration of the solution is 1.0 mg/mL. Standard stock solution has certificate can also be used(be stored at 0 ℃\\~4 ℃).", "type": "paragraph", "position": [137, 1417, 1067, 1417, 1067, 1504, 137, 1504], "content": 0, "sub_type": "text"}, {"paragraph_id": 16, "page_id": 9, "tags": [], "outline_level": -1, "text": "7", "type": "paragraph", "position": [1025, 1551, 1038, 1551, 1038, 1569, 1025, 1569], "content": 1, "sub_type": "footer"}, {"paragraph_id": 2, "page_id": 10, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [122, 145, 293, 145, 293, 161, 122, 161], "content": 1, "sub_type": "header"}, {"paragraph_id": 0, "page_id": 10, "tags": [], "outline_level": 2, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [234, 14, 955, 14, 955, 53, 234, 53], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 3, "page_id": 10, "tags": ["formula"], "outline_level": -1, "text": "3.6 Standard transition solution: Accurately transfer $2.0mL$  Standard stock solution(3.5) into $100mL$  volummetric flask, then make up to graduation with water. The concentration of the solution is $20mg/L$ (be stored at $0^{\\circ }C\\sim 4^{\\circ }C$ .", "type": "paragraph", "position": [122, 199, 1053, 199, 1053, 285, 122, 285], "content": 0, "sub_type": "text"}, {"paragraph_id": 4, "page_id": 10, "tags": ["formula"], "outline_level": -1, "text": "3.7 Standard working solution:According to the requirement,accurately measure different volumes of standard stock soluttion to volumetric flask, dilute with water to make different concentration of Standard working solution such as $0.025mg/L$ , $0.05mg/L$ , $0.10mg/L$ , , $0.20mg/L,0.40mg/L$  $1.00mg/L$ , . $2.00mg/L$ ", "type": "paragraph", "position": [122, 329, 1053, 329, 1053, 449, 122, 449], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 10, "tags": [], "outline_level": 2, "text": "4 Apparatus and equipment", "type": "paragraph", "position": [123, 512, 446, 512, 446, 533, 123, 533], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 6, "page_id": 10, "tags": [], "outline_level": -1, "text": "**4.1** lon chromatography,equipped with conductance detector.", "type": "paragraph", "position": [121, 593, 706, 597, 707, 617, 122, 614], "content": 0, "sub_type": "text"}, {"paragraph_id": 7, "page_id": 10, "tags": [], "outline_level": -1, "text": "**4.2** Tissue blender.", "type": "paragraph", "position": [122, 660, 316, 660, 316, 678, 122, 678], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 10, "tags": [], "outline_level": -1, "text": "**4.3** Mixer.", "type": "paragraph", "position": [122, 727, 235, 727, 235, 744, 122, 744], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 10, "tags": [], "outline_level": -1, "text": "**4.4** Balance(0.2 mg).", "type": "paragraph", "position": [121, 792, 333, 794, 334, 812, 122, 810], "content": 0, "sub_type": "text"}, {"paragraph_id": 10, "page_id": 10, "tags": [], "outline_level": -1, "text": "**4.5** Ulrasonic cleaner.", "type": "paragraph", "position": [121, 858, 336, 860, 337, 877, 122, 875], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 10, "tags": [], "outline_level": -1, "text": "**4.6** Centrifuge:4 500 r/min,50 mL centrifuge tube.", "type": "paragraph", "position": [122, 925, 608, 925, 608, 943, 122, 943], "content": 0, "sub_type": "text"}, {"paragraph_id": 12, "page_id": 10, "tags": ["formula"], "outline_level": -1, "text": "**4.7** Solid phase extraction $\\mathrm {C}_{18}$ column:200 mg,3 mL or equivalent column.", "type": "paragraph", "position": [122, 988, 821, 988, 821, 1015, 122, 1015], "content": 0, "sub_type": "text"}, {"paragraph_id": 13, "page_id": 10, "tags": ["formula"], "outline_level": -1, "text": "**4.8** Membrane filter: nylon, $0.45μm$ ,water phase.", "type": "paragraph", "position": [121, 1056, 604, 1058, 605, 1077, 122, 1075], "content": 0, "sub_type": "text"}, {"paragraph_id": 14, "page_id": 10, "tags": [], "outline_level": 2, "text": "5 Sample preparation and storage", "type": "paragraph", "position": [121, 1137, 496, 1140, 497, 1162, 121, 1159], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 15, "page_id": 10, "tags": [], "outline_level": 3, "text": "5.1 Requirement", "type": "paragraph", "position": [122, 1222, 298, 1222, 298, 1240, 122, 1240], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 16, "page_id": 10, "tags": ["formula"], "outline_level": -1, "text": "In the course of sampling and sample preparation $,$  precations shall be take to avoid the contamination or any factors which may cause the change of residue content.", "type": "paragraph", "position": [122, 1289, 1051, 1289, 1051, 1340, 122, 1340], "content": 0, "sub_type": "text"}, {"paragraph_id": 17, "page_id": 10, "tags": [], "outline_level": 3, "text": "5.2 preparation and storage", "type": "paragraph", "position": [121, 1387, 409, 1389, 409, 1407, 122, 1405], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 18, "page_id": 10, "tags": [], "outline_level": -1, "text": "5.2.1 Fresh longan, canned longan, dried longan, mango, potato,flour,rice, pork, and fish", "type": "paragraph", "position": [121, 1451, 1042, 1452, 1043, 1474, 122, 1473], "content": 0, "sub_type": "text"}, {"paragraph_id": 19, "page_id": 10, "tags": [], "outline_level": -1, "text": "About 500 g representative samples shouled be taken from all samples, the edible parts are cut into 8", "type": "paragraph", "position": [123, 1520, 1053, 1520, 1053, 1571, 123, 1571], "content": 0, "sub_type": "text"}, {"paragraph_id": 0, "page_id": 11, "tags": [], "outline_level": 4, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [234, 14, 955, 14, 955, 53, 234, 53], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 1, "page_id": 11, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [896, 145, 1067, 145, 1067, 161, 896, 161], "content": 0, "sub_type": "text"}, {"paragraph_id": 2, "page_id": 11, "tags": [], "outline_level": -1, "text": "mince and homogenized by a high speed tissue triturator.The mixed primary sample is divided into two equal portion .Each portion is put into one clean sample bottle which is sealed and labled. The sample should be stored at 0℃\\~4℃.", "type": "paragraph", "position": [137, 200, 1067, 200, 1067, 284, 137, 284], "content": 0, "sub_type": "text"}, {"paragraph_id": 3, "page_id": 11, "tags": [], "outline_level": 5, "text": "5.2.2 Mango juice,apple juice, mixed fruit and vegetables juice,milk,wine", "type": "paragraph", "position": [138, 331, 888, 331, 888, 351, 138, 351], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 4, "page_id": 11, "tags": [], "outline_level": -1, "text": "About 500 g representative samples shouled be taken from all samples.The mixed primary sample is divided into two equal portion .Each portion is put into one clean sample bottle which is sealed and labled. The sample should be stored at 0 ℃\\~4 ℃.", "type": "paragraph", "position": [138, 397, 1067, 397, 1067, 481, 138, 481], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 11, "tags": [], "outline_level": 5, "text": "5.2.3 Beer", "type": "paragraph", "position": [138, 528, 255, 528, 255, 546, 138, 546], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 6, "page_id": 11, "tags": [], "outline_level": -1, "text": "About 500 g representative samples shouled be taken from all samples. Smash thoroughly in a ceramic barrel. The mixed primary sample is divided into two equal portion .Each portion is put into one clean sample bottle which is sealed and fabled. The sample should be stored at 0 ℃\\~4 ℃.", "type": "paragraph", "position": [137, 596, 1067, 596, 1067, 681, 137, 681], "content": 0, "sub_type": "text"}, {"paragraph_id": 7, "page_id": 11, "tags": [], "outline_level": 2, "text": "6 Determination procedure", "type": "paragraph", "position": [136, 741, 455, 743, 455, 763, 136, 761], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 8, "page_id": 11, "tags": [], "outline_level": 3, "text": "6.1 Extraction", "type": "paragraph", "position": [138, 826, 296, 826, 296, 843, 138, 843], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 9, "page_id": 11, "tags": [], "outline_level": -1, "text": "6.1.1 Fresh longan, canned longan, dried longan, mango,potato, mango juice,apple juice,mixed fruit and vegetables juice,wvine, Beer, flour and rice", "type": "paragraph", "position": [138, 891, 1064, 891, 1064, 945, 138, 945], "content": 0, "sub_type": "text"}, {"paragraph_id": 10, "page_id": 11, "tags": [], "outline_level": -1, "text": "Weight 5.0 g sample (dried longan 1.0 g),accurate to 0.000 2 g, in 50 mL volumetric flask, add about 30 mL water, mix well, extract for 30 min in ultrasonic water bath.dilute to volume with water.Transfer the extraction to 50 mL centrifuge tube tube, then centrifuge at 4 500 r/min for 10 min.", "type": "paragraph", "position": [138, 991, 1067, 991, 1067, 1076, 138, 1076], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 11, "tags": [], "outline_level": 4, "text": "6.1.2 Milk", "type": "paragraph", "position": [138, 1122, 253, 1122, 253, 1140, 138, 1140], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 12, "page_id": 11, "tags": [], "outline_level": -1, "text": "Weight the sample (milk:5.0 g),accurate to 0.000 2 g ,in 50 mL volumetric flask, add about 30 mL water,mix well,extract for 30 min in ultrasonic water bath. Add 3% Acetic acid solution 4 mL,mix well, place 20 min under 4 ℃.Put out and return to room temperature,dilute to volume with water.Transfer the extraction to 50 mL centrifuge tube tube, then centrifuge at 4 500 r/min for 10 min.", "type": "paragraph", "position": [137, 1188, 1067, 1188, 1067, 1306, 137, 1306], "content": 0, "sub_type": "text"}, {"paragraph_id": 13, "page_id": 11, "tags": [], "outline_level": 4, "text": "6.1.3 Pork and fish", "type": "paragraph", "position": [138, 1355, 337, 1355, 337, 1372, 138, 1372], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 14, "page_id": 11, "tags": [], "outline_level": -1, "text": "Weight 5.0 g sample in beaker, accurate to 0.000 2 g, add 25 mL petroleum ether, stir well,pour out petroleum ether. Add 25 mL petroleum ether again, stir well, pour out petroleum ether.Volatile pe-troleum ether in heating slightly by water bath.Transfer the sample to 50 mL volumetric flask, add a-bout 30 mL water, mix well, extract for 30 min in ultrasonic water bath. Dilute to volume with wa-", "type": "paragraph", "position": [138, 1420, 1067, 1420, 1067, 1536, 138, 1536], "content": 0, "sub_type": "text"}, {"paragraph_id": 16, "page_id": 11, "tags": [], "outline_level": -1, "text": "9", "type": "paragraph", "position": [1025, 1552, 1039, 1552, 1039, 1571, 1025, 1571], "content": 1, "sub_type": "footer"}, {"paragraph_id": 0, "page_id": 12, "tags": [], "outline_level": 2, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [234, 14, 955, 14, 955, 53, 234, 53], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 1, "page_id": 12, "tags": [], "outline_level": 2, "text": "SN/T 4049-2014", "type": "paragraph", "position": [122, 145, 293, 145, 293, 161, 122, 161], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 2, "page_id": 12, "tags": ["formula"], "outline_level": -1, "text": "ter. Transfer theextraction to $50mL$  centrifuge tube, then centrifuge at $4500r/min$  for $10min$ .", "type": "paragraph", "position": [123, 199, 1020, 199, 1020, 217, 123, 217], "content": 0, "sub_type": "text"}, {"paragraph_id": 3, "page_id": 12, "tags": [], "outline_level": 2, "text": "6.2 Clean up", "type": "paragraph", "position": [122, 265, 263, 265, 263, 283, 122, 283], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 4, "page_id": 12, "tags": ["formula"], "outline_level": -1, "text": "The $\\mathrm {C}_{18}$ column(4.7) is leached with $10mL$  methanol and $15mL$  water one by one.Then The $\\mathrm {C}_{18}$ column is activated for 30 min before using. The supernatant(6.1)is cleaned up by $\\mathrm {C}_{18}$  column,and then is filtrated with $0.45μm$  microporous membrane(4.8). The filtrate is collected to be measured.", "type": "paragraph", "position": [123, 329, 1051, 329, 1051, 417, 123, 417], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 12, "tags": [], "outline_level": 2, "text": "6.3 Determination", "type": "paragraph", "position": [122, 464, 317, 464, 317, 481, 122, 481], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 6, "page_id": 12, "tags": [], "outline_level": 3, "text": "6.3.1 Chromatography reference operating conditions", "type": "paragraph", "position": [122, 530, 661, 530, 661, 549, 122, 549], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 7, "page_id": 12, "tags": [], "outline_level": -1, "text": "Chromatography reference operating conditions are as follows:", "type": "paragraph", "position": [123, 596, 709, 594, 708, 614, 122, 616], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 12, "tags": ["formula"], "outline_level": -1, "text": "a) Chromatography column: lonPac AS19,4 mmx 250 mm(with lonPac AG19, $4\\mathrm {\\sim mm}x50\\mathrm {\\sim mm}$  ,guard column)or equivalent column.", "type": "paragraph", "position": [122, 662, 1051, 662, 1051, 719, 122, 719], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 12, "tags": [], "outline_level": -1, "text": "b) Mobile phase:5 mmol/L\\~50 mmol/L KOH.", "type": "paragraph", "position": [122, 761, 564, 761, 564, 778, 122, 778], "content": 0, "sub_type": "text"}, {"paragraph_id": 10, "page_id": 12, "tags": [], "outline_level": -1, "text": "c) Flow rate:0.80 mL/min.", "type": "paragraph", "position": [122, 826, 391, 826, 391, 844, 122, 844], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 12, "tags": ["formula"], "outline_level": -1, "text": "d) Restrain electric current: $100mA$ .", "type": "paragraph", "position": [122, 893, 485, 893, 485, 910, 122, 910], "content": 0, "sub_type": "text"}, {"paragraph_id": 12, "page_id": 12, "tags": [], "outline_level": -1, "text": "e) Column tempreture: 30 ℃.", "type": "paragraph", "position": [123, 958, 419, 958, 419, 977, 123, 977], "content": 0, "sub_type": "text"}, {"paragraph_id": 13, "page_id": 12, "tags": [], "outline_level": -1, "text": "f) Injector volumn:100 μL.", "type": "paragraph", "position": [123, 1025, 386, 1025, 386, 1042, 123, 1042], "content": 0, "sub_type": "text"}, {"paragraph_id": 14, "page_id": 12, "tags": [], "outline_level": -1, "text": "**Table 1-Mobile phase gradient program**", "type": "paragraph", "position": [387, 1105, 785, 1107, 786, 1125, 388, 1124], "content": 0, "sub_type": "table_title"}, {"paragraph_id": 15, "page_id": 12, "content": 0, "outline_level": -1, "text": "<table border=\"1\" ><tr>\n<td colspan=\"1\" rowspan=\"1\">Time/min</td>\n<td colspan=\"1\" rowspan=\"1\">Mobile phase/(mmol/L)</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">0 </td>\n<td colspan=\"1\" rowspan=\"1\">5 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">25 </td>\n<td colspan=\"1\" rowspan=\"1\">5 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">30 </td>\n<td colspan=\"1\" rowspan=\"1\">50 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">40 </td>\n<td colspan=\"1\" rowspan=\"1\">50 </td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">45 </td>\n<td colspan=\"1\" rowspan=\"1\">5 </td>\n</tr></table>", "type": "table", "caption_id": {"paragraph_id": 14, "page_id": 12}, "position": [124, 1156, 1049, 1156, 1049, 1375, 121, 1373], "cells": [{"row_span": 1, "type": "cell", "text": "Time/min", "page_id": 12, "col_span": 1, "position": [125, 1155, 586, 1155, 586, 1191, 125, 1191], "col": 0, "row": 0}, {"row_span": 1, "type": "cell", "text": "Mobile phase/(mmol/L)", "page_id": 12, "col_span": 1, "position": [586, 1155, 1048, 1155, 1048, 1191, 586, 1191], "col": 1, "row": 0}, {"row_span": 1, "type": "cell", "text": "0 ", "page_id": 12, "col_span": 1, "position": [125, 1191, 586, 1191, 586, 1227, 125, 1227], "col": 0, "row": 1}, {"row_span": 1, "type": "cell", "text": "5 ", "page_id": 12, "col_span": 1, "position": [586, 1191, 1048, 1191, 1048, 1227, 586, 1227], "col": 1, "row": 1}, {"row_span": 1, "type": "cell", "text": "25 ", "page_id": 12, "col_span": 1, "position": [125, 1227, 586, 1227, 586, 1263, 125, 1263], "col": 0, "row": 2}, {"row_span": 1, "type": "cell", "text": "5 ", "page_id": 12, "col_span": 1, "position": [586, 1227, 1048, 1227, 1048, 1263, 586, 1263], "col": 1, "row": 2}, {"row_span": 1, "type": "cell", "text": "30 ", "page_id": 12, "col_span": 1, "position": [125, 1263, 586, 1263, 586, 1299, 125, 1299], "col": 0, "row": 3}, {"row_span": 1, "type": "cell", "text": "50 ", "page_id": 12, "col_span": 1, "position": [586, 1263, 1048, 1263, 1048, 1299, 586, 1299], "col": 1, "row": 3}, {"row_span": 1, "type": "cell", "text": "40 ", "page_id": 12, "col_span": 1, "position": [125, 1299, 586, 1299, 586, 1335, 125, 1335], "col": 0, "row": 4}, {"row_span": 1, "type": "cell", "text": "50 ", "page_id": 12, "col_span": 1, "position": [586, 1299, 1048, 1299, 1048, 1335, 586, 1335], "col": 1, "row": 4}, {"row_span": 1, "type": "cell", "text": "45 ", "page_id": 12, "col_span": 1, "position": [125, 1335, 586, 1335, 586, 1371, 125, 1371], "col": 0, "row": 5}, {"row_span": 1, "type": "cell", "text": "5 ", "page_id": 12, "col_span": 1, "position": [586, 1335, 1048, 1335, 1048, 1371, 586, 1371], "col": 1, "row": 5}], "sub_type": "bordered"}, {"paragraph_id": 16, "page_id": 12, "tags": [], "outline_level": 3, "text": "6.3.2 Determination", "type": "paragraph", "position": [122, 1418, 334, 1418, 334, 1435, 122, 1435], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 17, "page_id": 12, "tags": [], "outline_level": -1, "text": "According to the approximate concentration of the chlorate potassium in the sample solution,select the standard working solution with similar concentration of the sample solution. The standard", "type": "paragraph", "position": [123, 1487, 1053, 1487, 1053, 1539, 123, 1539], "content": 0, "sub_type": "text"}, {"paragraph_id": 18, "page_id": 12, "tags": [], "outline_level": -1, "text": "10", "type": "paragraph", "position": [151, 1554, 173, 1554, 173, 1567, 151, 1567], "content": 1, "sub_type": "footer"}, {"paragraph_id": 0, "page_id": 13, "tags": [], "outline_level": 2, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [234, 14, 955, 14, 955, 53, 234, 53], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 1, "page_id": 13, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [896, 145, 1067, 145, 1067, 161, 896, 161], "content": 0, "sub_type": "text"}, {"paragraph_id": 2, "page_id": 13, "tags": ["formula"], "outline_level": -1, "text": "working solution should be injected in-between the injections of the sample solution with one com-mon volume. The response of chlorate potassium in the standard working solution and sample solu-tion should be within the linear range of the instrument detection.Under the above chromatograph conditions,the reference retention time of chlorate potassium is about $17min$ . The chromatograms of the standard working solution is showed in figure A.1.", "type": "paragraph", "position": [138, 200, 1067, 200, 1067, 354, 138, 354], "content": 0, "sub_type": "text"}, {"paragraph_id": 3, "page_id": 13, "tags": [], "outline_level": 3, "text": "6.3.3 Blank test", "type": "paragraph", "position": [138, 401, 312, 401, 312, 419, 138, 419], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 4, "page_id": 13, "tags": [], "outline_level": -1, "text": "The operation of the blank test is the same as that described in the method of determination, but without addition of sample.", "type": "paragraph", "position": [138, 471, 1067, 471, 1067, 522, 138, 522], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 13, "tags": [], "outline_level": 2, "text": "7 Calculation and expression of the result", "type": "paragraph", "position": [137, 586, 626, 587, 627, 609, 138, 607], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 6, "page_id": 13, "tags": [], "outline_level": -1, "text": "Calculate the content of chlorate in the test sample by IC data processor or according to the formula (1),the blank value should be subtracted from the above resule of calculation.", "type": "paragraph", "position": [137, 674, 1067, 674, 1067, 727, 137, 727], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 13, "tags": ["formula"], "outline_level": -1, "text": "$$X=\\frac {C\\times V}{m}\\tag{1}$$", "type": "paragraph", "position": [551, 778, 1064, 778, 1064, 824, 551, 824], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 13, "tags": [], "outline_level": -1, "text": "Where:", "type": "paragraph", "position": [138, 874, 212, 874, 212, 891, 138, 891], "content": 0, "sub_type": "text"}, {"paragraph_id": 10, "page_id": 13, "tags": [], "outline_level": -1, "text": "X-the concentration of chlorate in the test sample,mg/kg;", "type": "paragraph", "position": [140, 941, 704, 941, 704, 960, 140, 960], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 13, "tags": [], "outline_level": -1, "text": "C -the concentration of chlorate is fromcalibration curve,mg/L;", "type": "paragraph", "position": [140, 1009, 755, 1009, 755, 1028, 140, 1028], "content": 0, "sub_type": "text"}, {"paragraph_id": 12, "page_id": 13, "tags": ["formula"], "outline_level": -1, "text": "$V$ -the final volume of the sample solution,mL;", "type": "paragraph", "position": [140, 1075, 596, 1077, 597, 1094, 141, 1092], "content": 0, "sub_type": "text"}, {"paragraph_id": 13, "page_id": 13, "tags": ["formula"], "outline_level": -1, "text": "$m$ -the corresponding mass of the test sample in the final sample solution,g.", "type": "paragraph", "position": [140, 1144, 863, 1144, 863, 1165, 140, 1165], "content": 0, "sub_type": "text"}, {"paragraph_id": 14, "page_id": 13, "tags": [], "outline_level": 2, "text": "8 Limit of quatification and recovery", "type": "paragraph", "position": [136, 1225, 570, 1227, 571, 1250, 136, 1248], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 15, "page_id": 13, "tags": [], "outline_level": 3, "text": "8.1 Limit of quatification", "type": "paragraph", "position": [138, 1316, 405, 1316, 405, 1334, 138, 1334], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 16, "page_id": 13, "tags": ["formula"], "outline_level": -1, "text": "The limit of determination of this method is $0.5mg/kg$  for Fresh longan, canned longan,mango,mango juice,apple juice, mixed fruit and vegetables juice, potato, beer,flour, rice,milk,pork and fish.The limit of determination of this method is $1.0mg/kg$  for wine. The limit of determination of this method is $2.5mg/kg$  for dried longan.", "type": "paragraph", "position": [138, 1385, 1067, 1385, 1067, 1505, 138, 1505], "content": 0, "sub_type": "text"}, {"paragraph_id": 18, "page_id": 13, "tags": [], "outline_level": -1, "text": "11", "type": "paragraph", "position": [1018, 1554, 1036, 1554, 1036, 1567, 1018, 1567], "content": 1, "sub_type": "footer"}, {"paragraph_id": 0, "page_id": 14, "tags": [], "outline_level": -1, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [234, 14, 957, 14, 957, 53, 234, 53], "content": 1, "sub_type": "header"}, {"paragraph_id": 2, "page_id": 14, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [122, 145, 293, 145, 293, 161, 122, 161], "content": 0, "sub_type": "text"}, {"paragraph_id": 3, "page_id": 14, "tags": [], "outline_level": 3, "text": "8.2 Recovery", "type": "paragraph", "position": [121, 198, 266, 200, 266, 219, 121, 217], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 4, "page_id": 14, "tags": [], "outline_level": -1, "text": "The recoveries range of fortifying concentrations see table 2.", "type": "paragraph", "position": [123, 267, 696, 267, 696, 286, 123, 286], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 14, "tags": [], "outline_level": -1, "text": "Table **2-Recoveries range of chlorate** in **different samples**", "type": "paragraph", "position": [295, 313, 877, 315, 878, 335, 296, 333], "content": 0, "sub_type": "table_title"}, {"paragraph_id": 6, "page_id": 14, "content": 0, "outline_level": -1, "text": "<table border=\"1\" ><tr>\n<td colspan=\"1\" rowspan=\"1\">sample </td>\n<td colspan=\"1\" rowspan=\"1\">Spike levels/(mg/kg)</td>\n<td colspan=\"1\" rowspan=\"1\">Recovery range/%</td>\n<td colspan=\"1\" rowspan=\"1\">sample </td>\n<td colspan=\"1\" rowspan=\"1\">Spike levels/(mg/kg)</td>\n<td colspan=\"1\" rowspan=\"1\">Recovery range/%</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">Fresh longan </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">88.6\\~100.8</td>\n<td colspan=\"1\" rowspan=\"3\">wine </td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">81.9\\~92.1</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.6\\~103.4</td>\n<td colspan=\"1\" rowspan=\"1\">2.0 </td>\n<td colspan=\"1\" rowspan=\"1\">84.2\\~95.8</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.2\\~101.1</td>\n<td colspan=\"1\" rowspan=\"1\">10.0 </td>\n<td colspan=\"1\" rowspan=\"1\">88.4\\~100.1</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">canned longan </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">89.0\\~101.2</td>\n<td colspan=\"1\" rowspan=\"3\">Beer </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">88.4\\~100.4</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.0\\~103.2</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">87.3\\~103.2</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.8\\~101.6</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">90.0\\~102.1</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">dried longan </td>\n<td colspan=\"1\" rowspan=\"1\">2.5 </td>\n<td colspan=\"1\" rowspan=\"1\">82.5\\~93.0</td>\n<td colspan=\"1\" rowspan=\"3\">Flour </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">89.0\\~100.4</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">86.3\\~95.3</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">90.4\\~104.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">25.0 </td>\n<td colspan=\"1\" rowspan=\"1\">87.8\\~97.0</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">92.0\\~101.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">mango </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">84.6\\~97.4</td>\n<td colspan=\"1\" rowspan=\"3\">Rice </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">86.4\\~99.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">85.6\\~96.2</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">88.6\\~101.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">89.7\\~97.6</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">88.6\\~100.6</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">Mango juice </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">86.4\\~98.2</td>\n<td colspan=\"1\" rowspan=\"3\">milk </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">86.6\\~97.4</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">88.5\\~97.6</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">89.4\\~102.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">90.7\\~100.4</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">89.8\\~100.5</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">apple juice </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">87.4\\~100.4</td>\n<td colspan=\"1\" rowspan=\"3\">pork </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">84.4\\~93.6</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">89.2\\~102.3</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">87.5\\~96.8</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">90.9\\~100.4</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">87.8\\~95.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">mixed fruit and vagetables juice </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">84.4\\~98.2</td>\n<td colspan=\"1\" rowspan=\"3\">Fish </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">86.4\\~100.2</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">86.5\\~96.3</td>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">88.5\\~99.3</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.0\\~100.2</td>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">90.6\\~101.0</td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"3\">potato </td>\n<td colspan=\"1\" rowspan=\"1\">0.5 </td>\n<td colspan=\"1\" rowspan=\"1\">84.2\\~97.2</td>\n<td colspan=\"1\" rowspan=\"3\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">1.0 </td>\n<td colspan=\"1\" rowspan=\"1\">86.7\\~96.2</td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n</tr><tr>\n<td colspan=\"1\" rowspan=\"1\">5.0 </td>\n<td colspan=\"1\" rowspan=\"1\">91.7\\~98.0</td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n<td colspan=\"1\" rowspan=\"1\"></td>\n</tr></table>", "type": "table", "caption_id": {"paragraph_id": 5, "page_id": 14}, "position": [120, 369, 1053, 373, 1047, 1275, 123, 1275], "cells": [{"row_span": 1, "type": "cell", "text": "sample ", "page_id": 14, "col_span": 1, "position": [123, 367, 242, 367, 242, 404, 123, 404], "col": 0, "row": 0}, {"row_span": 1, "type": "cell", "text": "Spike levels/(mg/kg)", "page_id": 14, "col_span": 1, "position": [242, 367, 413, 367, 413, 404, 242, 404], "col": 1, "row": 0}, {"row_span": 1, "type": "cell", "text": "Recovery range/%", "page_id": 14, "col_span": 1, "position": [413, 367, 585, 367, 585, 404, 413, 404], "col": 2, "row": 0}, {"row_span": 1, "type": "cell", "text": "sample ", "page_id": 14, "col_span": 1, "position": [585, 367, 703, 367, 703, 404, 585, 404], "col": 3, "row": 0}, {"row_span": 1, "type": "cell", "text": "Spike levels/(mg/kg)", "page_id": 14, "col_span": 1, "position": [703, 367, 875, 367, 875, 404, 703, 404], "col": 4, "row": 0}, {"row_span": 1, "type": "cell", "text": "Recovery range/%", "page_id": 14, "col_span": 1, "position": [875, 367, 1048, 367, 1048, 404, 875, 404], "col": 5, "row": 0}, {"row_span": 3, "type": "cell", "text": "Fresh longan ", "page_id": 14, "col_span": 1, "position": [123, 404, 242, 404, 242, 511, 123, 511], "col": 0, "row": 1}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 14, "col_span": 1, "position": [242, 404, 413, 404, 413, 441, 242, 441], "col": 1, "row": 1}, {"row_span": 1, "type": "cell", "text": "88.6\\~100.8", "page_id": 14, "col_span": 1, "position": [413, 404, 585, 404, 585, 441, 413, 441], "col": 2, "row": 1}, {"row_span": 3, "type": "cell", "text": "wine ", "page_id": 14, "col_span": 1, "position": [585, 404, 703, 404, 703, 511, 585, 511], "col": 3, "row": 1}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [703, 404, 875, 404, 875, 441, 703, 441], "col": 4, "row": 1}, {"row_span": 1, "type": "cell", "text": "81.9\\~92.1", "page_id": 14, "col_span": 1, "position": [875, 404, 1048, 404, 1048, 441, 875, 441], "col": 5, "row": 1}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [242, 441, 413, 441, 413, 474, 242, 474], "col": 1, "row": 2}, {"row_span": 1, "type": "cell", "text": "91.6\\~103.4", "page_id": 14, "col_span": 1, "position": [413, 441, 585, 441, 585, 474, 413, 474], "col": 2, "row": 2}, {"row_span": 1, "type": "cell", "text": "2.0 ", "page_id": 14, "col_span": 1, "position": [703, 441, 875, 441, 875, 474, 703, 474], "col": 4, "row": 2}, {"row_span": 1, "type": "cell", "text": "84.2\\~95.8", "page_id": 14, "col_span": 1, "position": [875, 441, 1048, 441, 1048, 474, 875, 474], "col": 5, "row": 2}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [242, 474, 413, 474, 413, 511, 242, 511], "col": 1, "row": 3}, {"row_span": 1, "type": "cell", "text": "91.2\\~101.1", "page_id": 14, "col_span": 1, "position": [413, 474, 585, 474, 585, 511, 413, 511], "col": 2, "row": 3}, {"row_span": 1, "type": "cell", "text": "10.0 ", "page_id": 14, "col_span": 1, "position": [703, 474, 875, 474, 875, 511, 703, 511], "col": 4, "row": 3}, {"row_span": 1, "type": "cell", "text": "88.4\\~100.1", "page_id": 14, "col_span": 1, "position": [875, 474, 1048, 474, 1048, 511, 875, 511], "col": 5, "row": 3}, {"row_span": 3, "type": "cell", "text": "canned longan ", "page_id": 14, "col_span": 1, "position": [123, 511, 242, 511, 242, 620, 123, 620], "col": 0, "row": 4}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 14, "col_span": 1, "position": [242, 511, 413, 511, 413, 547, 242, 547], "col": 1, "row": 4}, {"row_span": 1, "type": "cell", "text": "89.0\\~101.2", "page_id": 14, "col_span": 1, "position": [413, 511, 585, 511, 585, 547, 413, 547], "col": 2, "row": 4}, {"row_span": 3, "type": "cell", "text": "Beer ", "page_id": 14, "col_span": 1, "position": [585, 511, 703, 511, 703, 620, 585, 620], "col": 3, "row": 4}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 14, "col_span": 1, "position": [703, 511, 875, 511, 875, 547, 703, 547], "col": 4, "row": 4}, {"row_span": 1, "type": "cell", "text": "88.4\\~100.4", "page_id": 14, "col_span": 1, "position": [875, 511, 1048, 511, 1048, 547, 875, 547], "col": 5, "row": 4}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [242, 547, 413, 547, 413, 584, 242, 584], "col": 1, "row": 5}, {"row_span": 1, "type": "cell", "text": "91.0\\~103.2", "page_id": 14, "col_span": 1, "position": [413, 547, 585, 547, 585, 584, 413, 584], "col": 2, "row": 5}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [703, 547, 875, 547, 875, 584, 703, 584], "col": 4, "row": 5}, {"row_span": 1, "type": "cell", "text": "87.3\\~103.2", "page_id": 14, "col_span": 1, "position": [875, 547, 1048, 547, 1048, 584, 875, 584], "col": 5, "row": 5}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [242, 584, 413, 584, 413, 620, 242, 620], "col": 1, "row": 6}, {"row_span": 1, "type": "cell", "text": "91.8\\~101.6", "page_id": 14, "col_span": 1, "position": [413, 584, 585, 584, 585, 620, 413, 620], "col": 2, "row": 6}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [703, 584, 875, 584, 875, 620, 703, 620], "col": 4, "row": 6}, {"row_span": 1, "type": "cell", "text": "90.0\\~102.1", "page_id": 14, "col_span": 1, "position": [875, 584, 1048, 584, 1048, 620, 875, 620], "col": 5, "row": 6}, {"row_span": 3, "type": "cell", "text": "dried longan ", "page_id": 14, "col_span": 1, "position": [123, 620, 242, 620, 242, 727, 123, 727], "col": 0, "row": 7}, {"row_span": 1, "type": "cell", "text": "2.5 ", "page_id": 14, "col_span": 1, "position": [242, 620, 413, 620, 413, 657, 242, 657], "col": 1, "row": 7}, {"row_span": 1, "type": "cell", "text": "82.5\\~93.0", "page_id": 14, "col_span": 1, "position": [413, 620, 585, 620, 585, 657, 413, 657], "col": 2, "row": 7}, {"row_span": 3, "type": "cell", "text": "Flour ", "page_id": 14, "col_span": 1, "position": [585, 620, 703, 620, 703, 727, 585, 727], "col": 3, "row": 7}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 14, "col_span": 1, "position": [703, 620, 875, 620, 875, 657, 703, 657], "col": 4, "row": 7}, {"row_span": 1, "type": "cell", "text": "89.0\\~100.4", "page_id": 14, "col_span": 1, "position": [875, 620, 1048, 620, 1048, 657, 875, 657], "col": 5, "row": 7}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [242, 657, 413, 657, 413, 690, 242, 690], "col": 1, "row": 8}, {"row_span": 1, "type": "cell", "text": "86.3\\~95.3", "page_id": 14, "col_span": 1, "position": [413, 657, 585, 657, 585, 690, 413, 690], "col": 2, "row": 8}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [703, 657, 875, 657, 875, 690, 703, 690], "col": 4, "row": 8}, {"row_span": 1, "type": "cell", "text": "90.4\\~104.0", "page_id": 14, "col_span": 1, "position": [875, 657, 1048, 657, 1048, 690, 875, 690], "col": 5, "row": 8}, {"row_span": 1, "type": "cell", "text": "25.0 ", "page_id": 14, "col_span": 1, "position": [242, 690, 413, 690, 413, 727, 242, 727], "col": 1, "row": 9}, {"row_span": 1, "type": "cell", "text": "87.8\\~97.0", "page_id": 14, "col_span": 1, "position": [413, 690, 585, 690, 585, 727, 413, 727], "col": 2, "row": 9}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [703, 690, 875, 690, 875, 727, 703, 727], "col": 4, "row": 9}, {"row_span": 1, "type": "cell", "text": "92.0\\~101.0", "page_id": 14, "col_span": 1, "position": [875, 690, 1048, 690, 1048, 727, 875, 727], "col": 5, "row": 9}, {"row_span": 3, "type": "cell", "text": "mango ", "page_id": 14, "col_span": 1, "position": [123, 727, 242, 727, 242, 837, 123, 837], "col": 0, "row": 10}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 14, "col_span": 1, "position": [242, 727, 413, 727, 413, 764, 242, 764], "col": 1, "row": 10}, {"row_span": 1, "type": "cell", "text": "84.6\\~97.4", "page_id": 14, "col_span": 1, "position": [413, 727, 585, 727, 585, 764, 413, 764], "col": 2, "row": 10}, {"row_span": 3, "type": "cell", "text": "Rice ", "page_id": 14, "col_span": 1, "position": [585, 727, 703, 727, 703, 837, 585, 837], "col": 3, "row": 10}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 14, "col_span": 1, "position": [703, 727, 875, 727, 875, 764, 703, 764], "col": 4, "row": 10}, {"row_span": 1, "type": "cell", "text": "86.4\\~99.0", "page_id": 14, "col_span": 1, "position": [875, 727, 1048, 727, 1048, 764, 875, 764], "col": 5, "row": 10}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [242, 764, 413, 764, 413, 800, 242, 800], "col": 1, "row": 11}, {"row_span": 1, "type": "cell", "text": "85.6\\~96.2", "page_id": 14, "col_span": 1, "position": [413, 764, 585, 764, 585, 800, 413, 800], "col": 2, "row": 11}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [703, 764, 875, 764, 875, 800, 703, 800], "col": 4, "row": 11}, {"row_span": 1, "type": "cell", "text": "88.6\\~101.0", "page_id": 14, "col_span": 1, "position": [875, 764, 1048, 764, 1048, 800, 875, 800], "col": 5, "row": 11}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [242, 800, 413, 800, 413, 837, 242, 837], "col": 1, "row": 12}, {"row_span": 1, "type": "cell", "text": "89.7\\~97.6", "page_id": 14, "col_span": 1, "position": [413, 800, 585, 800, 585, 837, 413, 837], "col": 2, "row": 12}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [703, 800, 875, 800, 875, 837, 703, 837], "col": 4, "row": 12}, {"row_span": 1, "type": "cell", "text": "88.6\\~100.6", "page_id": 14, "col_span": 1, "position": [875, 800, 1048, 800, 1048, 837, 875, 837], "col": 5, "row": 12}, {"row_span": 3, "type": "cell", "text": "Mango juice ", "page_id": 14, "col_span": 1, "position": [123, 837, 242, 837, 242, 944, 123, 944], "col": 0, "row": 13}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 14, "col_span": 1, "position": [242, 837, 413, 837, 413, 874, 242, 874], "col": 1, "row": 13}, {"row_span": 1, "type": "cell", "text": "86.4\\~98.2", "page_id": 14, "col_span": 1, "position": [413, 837, 585, 837, 585, 874, 413, 874], "col": 2, "row": 13}, {"row_span": 3, "type": "cell", "text": "milk ", "page_id": 14, "col_span": 1, "position": [585, 837, 703, 837, 703, 944, 585, 944], "col": 3, "row": 13}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 14, "col_span": 1, "position": [703, 837, 875, 837, 875, 874, 703, 874], "col": 4, "row": 13}, {"row_span": 1, "type": "cell", "text": "86.6\\~97.4", "page_id": 14, "col_span": 1, "position": [875, 837, 1048, 837, 1048, 874, 875, 874], "col": 5, "row": 13}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [242, 874, 413, 874, 413, 910, 242, 910], "col": 1, "row": 14}, {"row_span": 1, "type": "cell", "text": "88.5\\~97.6", "page_id": 14, "col_span": 1, "position": [413, 874, 585, 874, 585, 910, 413, 910], "col": 2, "row": 14}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [703, 874, 875, 874, 875, 910, 703, 910], "col": 4, "row": 14}, {"row_span": 1, "type": "cell", "text": "89.4\\~102.0", "page_id": 14, "col_span": 1, "position": [875, 874, 1048, 874, 1048, 910, 875, 910], "col": 5, "row": 14}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [242, 910, 413, 910, 413, 944, 242, 944], "col": 1, "row": 15}, {"row_span": 1, "type": "cell", "text": "90.7\\~100.4", "page_id": 14, "col_span": 1, "position": [413, 910, 585, 910, 585, 944, 413, 944], "col": 2, "row": 15}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [703, 910, 875, 910, 875, 944, 703, 944], "col": 4, "row": 15}, {"row_span": 1, "type": "cell", "text": "89.8\\~100.5", "page_id": 14, "col_span": 1, "position": [875, 910, 1048, 910, 1048, 944, 875, 944], "col": 5, "row": 15}, {"row_span": 3, "type": "cell", "text": "apple juice ", "page_id": 14, "col_span": 1, "position": [123, 944, 242, 944, 242, 1054, 123, 1054], "col": 0, "row": 16}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 14, "col_span": 1, "position": [242, 944, 413, 944, 413, 980, 242, 980], "col": 1, "row": 16}, {"row_span": 1, "type": "cell", "text": "87.4\\~100.4", "page_id": 14, "col_span": 1, "position": [413, 944, 585, 944, 585, 980, 413, 980], "col": 2, "row": 16}, {"row_span": 3, "type": "cell", "text": "pork ", "page_id": 14, "col_span": 1, "position": [585, 944, 703, 944, 703, 1054, 585, 1054], "col": 3, "row": 16}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 14, "col_span": 1, "position": [703, 944, 875, 944, 875, 980, 703, 980], "col": 4, "row": 16}, {"row_span": 1, "type": "cell", "text": "84.4\\~93.6", "page_id": 14, "col_span": 1, "position": [875, 944, 1048, 944, 1048, 980, 875, 980], "col": 5, "row": 16}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [242, 980, 413, 980, 413, 1017, 242, 1017], "col": 1, "row": 17}, {"row_span": 1, "type": "cell", "text": "89.2\\~102.3", "page_id": 14, "col_span": 1, "position": [413, 980, 585, 980, 585, 1017, 413, 1017], "col": 2, "row": 17}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [703, 980, 875, 980, 875, 1017, 703, 1017], "col": 4, "row": 17}, {"row_span": 1, "type": "cell", "text": "87.5\\~96.8", "page_id": 14, "col_span": 1, "position": [875, 980, 1048, 980, 1048, 1017, 875, 1017], "col": 5, "row": 17}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [242, 1017, 413, 1017, 413, 1054, 242, 1054], "col": 1, "row": 18}, {"row_span": 1, "type": "cell", "text": "90.9\\~100.4", "page_id": 14, "col_span": 1, "position": [413, 1017, 585, 1017, 585, 1054, 413, 1054], "col": 2, "row": 18}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [703, 1017, 875, 1017, 875, 1054, 703, 1054], "col": 4, "row": 18}, {"row_span": 1, "type": "cell", "text": "87.8\\~95.0", "page_id": 14, "col_span": 1, "position": [875, 1017, 1048, 1017, 1048, 1054, 875, 1054], "col": 5, "row": 18}, {"row_span": 3, "type": "cell", "text": "mixed fruit and vagetables juice ", "page_id": 14, "col_span": 1, "position": [123, 1054, 242, 1054, 242, 1163, 123, 1163], "col": 0, "row": 19}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 14, "col_span": 1, "position": [242, 1054, 413, 1054, 413, 1090, 242, 1090], "col": 1, "row": 19}, {"row_span": 1, "type": "cell", "text": "84.4\\~98.2", "page_id": 14, "col_span": 1, "position": [413, 1054, 585, 1054, 585, 1090, 413, 1090], "col": 2, "row": 19}, {"row_span": 3, "type": "cell", "text": "Fish ", "page_id": 14, "col_span": 1, "position": [585, 1054, 703, 1054, 703, 1163, 585, 1163], "col": 3, "row": 19}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 14, "col_span": 1, "position": [703, 1054, 875, 1054, 875, 1090, 703, 1090], "col": 4, "row": 19}, {"row_span": 1, "type": "cell", "text": "86.4\\~100.2", "page_id": 14, "col_span": 1, "position": [875, 1054, 1048, 1054, 1048, 1090, 875, 1090], "col": 5, "row": 19}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [242, 1090, 413, 1090, 413, 1127, 242, 1127], "col": 1, "row": 20}, {"row_span": 1, "type": "cell", "text": "86.5\\~96.3", "page_id": 14, "col_span": 1, "position": [413, 1090, 585, 1090, 585, 1127, 413, 1127], "col": 2, "row": 20}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [703, 1090, 875, 1090, 875, 1127, 703, 1127], "col": 4, "row": 20}, {"row_span": 1, "type": "cell", "text": "88.5\\~99.3", "page_id": 14, "col_span": 1, "position": [875, 1090, 1048, 1090, 1048, 1127, 875, 1127], "col": 5, "row": 20}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [242, 1127, 413, 1127, 413, 1163, 242, 1163], "col": 1, "row": 21}, {"row_span": 1, "type": "cell", "text": "91.0\\~100.2", "page_id": 14, "col_span": 1, "position": [413, 1127, 585, 1127, 585, 1163, 413, 1163], "col": 2, "row": 21}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [703, 1127, 875, 1127, 875, 1163, 703, 1163], "col": 4, "row": 21}, {"row_span": 1, "type": "cell", "text": "90.6\\~101.0", "page_id": 14, "col_span": 1, "position": [875, 1127, 1048, 1127, 1048, 1163, 875, 1163], "col": 5, "row": 21}, {"row_span": 3, "type": "cell", "text": "potato ", "page_id": 14, "col_span": 1, "position": [123, 1163, 242, 1163, 242, 1270, 123, 1270], "col": 0, "row": 22}, {"row_span": 1, "type": "cell", "text": "0.5 ", "page_id": 14, "col_span": 1, "position": [242, 1163, 413, 1163, 413, 1200, 242, 1200], "col": 1, "row": 22}, {"row_span": 1, "type": "cell", "text": "84.2\\~97.2", "page_id": 14, "col_span": 1, "position": [413, 1163, 585, 1163, 585, 1200, 413, 1200], "col": 2, "row": 22}, {"row_span": 3, "type": "cell", "text": "", "page_id": 14, "col_span": 1, "position": [585, 1163, 703, 1163, 703, 1270, 585, 1270], "col": 3, "row": 22}, {"row_span": 1, "type": "cell", "text": "", "page_id": 14, "col_span": 1, "position": [703, 1163, 875, 1163, 875, 1200, 703, 1200], "col": 4, "row": 22}, {"row_span": 1, "type": "cell", "text": "", "page_id": 14, "col_span": 1, "position": [875, 1163, 1048, 1163, 1048, 1200, 875, 1200], "col": 5, "row": 22}, {"row_span": 1, "type": "cell", "text": "1.0 ", "page_id": 14, "col_span": 1, "position": [242, 1200, 413, 1200, 413, 1233, 242, 1233], "col": 1, "row": 23}, {"row_span": 1, "type": "cell", "text": "86.7\\~96.2", "page_id": 14, "col_span": 1, "position": [413, 1200, 585, 1200, 585, 1233, 413, 1233], "col": 2, "row": 23}, {"row_span": 1, "type": "cell", "text": "", "page_id": 14, "col_span": 1, "position": [703, 1200, 875, 1200, 875, 1233, 703, 1233], "col": 4, "row": 23}, {"row_span": 1, "type": "cell", "text": "", "page_id": 14, "col_span": 1, "position": [875, 1200, 1048, 1200, 1048, 1233, 875, 1233], "col": 5, "row": 23}, {"row_span": 1, "type": "cell", "text": "5.0 ", "page_id": 14, "col_span": 1, "position": [242, 1233, 413, 1233, 413, 1270, 242, 1270], "col": 1, "row": 24}, {"row_span": 1, "type": "cell", "text": "91.7\\~98.0", "page_id": 14, "col_span": 1, "position": [413, 1233, 585, 1233, 585, 1270, 413, 1270], "col": 2, "row": 24}, {"row_span": 1, "type": "cell", "text": "", "page_id": 14, "col_span": 1, "position": [703, 1233, 875, 1233, 875, 1270, 703, 1270], "col": 4, "row": 24}, {"row_span": 1, "type": "cell", "text": "", "page_id": 14, "col_span": 1, "position": [875, 1233, 1048, 1233, 1048, 1270, 875, 1270], "col": 5, "row": 24}], "sub_type": "bordered"}, {"paragraph_id": 7, "page_id": 14, "tags": [], "outline_level": 2, "text": "9 Precision", "type": "paragraph", "position": [122, 1336, 266, 1336, 266, 1355, 122, 1355], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 8, "page_id": 14, "tags": [], "outline_level": -1, "text": "The absolute difference of the two independent test results obtained under the condition of repetition is not more than 10% of arithmetic mean.", "type": "paragraph", "position": [122, 1415, 1052, 1415, 1052, 1466, 122, 1466], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 14, "tags": [], "outline_level": -1, "text": "12", "type": "paragraph", "position": [151, 1554, 173, 1554, 173, 1567, 151, 1567], "content": 1, "sub_type": "footer"}, {"paragraph_id": 0, "page_id": 15, "tags": [], "outline_level": -1, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [232, 14, 955, 14, 955, 53, 232, 53], "content": 0, "sub_type": "text"}, {"paragraph_id": 1, "page_id": 15, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [896, 145, 1067, 145, 1067, 161, 896, 161], "content": 0, "sub_type": "text"}, {"paragraph_id": 2, "page_id": 15, "tags": [], "outline_level": 1, "text": "AnnexA", "type": "paragraph", "position": [556, 248, 650, 248, 650, 266, 556, 266], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 3, "page_id": 15, "tags": [], "outline_level": -1, "text": "(Informative)", "type": "paragraph", "position": [526, 283, 678, 283, 678, 301, 526, 301], "content": 0, "sub_type": "text"}, {"paragraph_id": 4, "page_id": 15, "tags": [], "outline_level": -1, "text": "Chromatogram of the standard", "type": "paragraph", "position": [431, 317, 776, 317, 776, 338, 431, 338], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 15, "content": 0, "outline_level": -1, "text": "4.00 $\\mathrm {ClO}_{3}^{-}$ -0.30 1.8 5.0 7.5 10.0 12.5 15.0 17.5 20.0 22.5 25.0 27.5 30.0 32.5 35.0 39.5 min", "type": "image", "image_url": "https://web-api.textin.com/ocr_image/external/7f27d2b57932335e.jpg", "position": [260, 388, 949, 390, 949, 561, 260, 561], "caption_id": {"paragraph_id": 6, "page_id": 15}, "sub_type": "chart"}, {"paragraph_id": 6, "page_id": 15, "tags": ["formula"], "outline_level": -1, "text": "Figure A.1-IC chromatogram of the chlorate standard working solution( $2.0mg/L)$ ", "type": "paragraph", "position": [225, 592, 1036, 592, 1036, 612, 225, 612], "content": 0, "sub_type": "image_title"}, {"paragraph_id": 7, "page_id": 15, "tags": [], "outline_level": -1, "text": "<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>", "type": "paragraph", "position": [486, 662, 719, 662, 719, 681, 486, 681], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 15, "tags": [], "outline_level": -1, "text": "13", "type": "paragraph", "position": [1018, 1554, 1038, 1554, 1038, 1567, 1018, 1567], "content": 1, "sub_type": "footer"}, {"paragraph_id": 0, "page_id": 16, "tags": [], "outline_level": -1, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [231, 13, 956, 15, 957, 54, 232, 52], "content": 0, "sub_type": "text"}, {"paragraph_id": 0, "page_id": 17, "tags": [], "outline_level": -1, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [231, 13, 956, 15, 957, 54, 232, 52], "content": 0, "sub_type": "text"}, {"paragraph_id": 0, "page_id": 18, "tags": [], "outline_level": 1, "text": "版权所有·禁止翻制、电子传阅、发售", "type": "paragraph", "position": [231, 13, 956, 15, 957, 54, 232, 52], "content": 0, "sub_type": "text_title"}, {"paragraph_id": 1, "page_id": 18, "tags": [], "outline_level": -1, "text": "tI0z-6t0 J/NS", "type": "paragraph", "position": [1133, 140, 1164, 140, 1162, 364, 1131, 363], "content": 0, "sub_type": "text"}, {"paragraph_id": 3, "page_id": 18, "tags": [], "outline_level": -1, "text": "中华人民共和国出入境检验检疫", "type": "paragraph", "position": [732, 1058, 987, 1058, 987, 1074, 732, 1074], "content": 0, "sub_type": "text"}, {"paragraph_id": 4, "page_id": 18, "tags": [], "outline_level": -1, "text": "行业标准", "type": "paragraph", "position": [783, 1087, 936, 1087, 936, 1106, 783, 1106], "content": 0, "sub_type": "text"}, {"paragraph_id": 5, "page_id": 18, "tags": [], "outline_level": -1, "text": "**出口食品中氯酸盐的测定 离子色谱法**", "type": "paragraph", "position": [679, 1118, 1039, 1118, 1039, 1137, 679, 1137], "content": 0, "sub_type": "text"}, {"paragraph_id": 6, "page_id": 18, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014", "type": "paragraph", "position": [786, 1148, 931, 1148, 931, 1168, 786, 1168], "content": 0, "sub_type": "text"}, {"paragraph_id": 7, "page_id": 18, "tags": [], "outline_level": -1, "text": "*", "type": "paragraph", "position": [850, 1179, 865, 1179, 865, 1194, 850, 1194], "content": 0, "sub_type": "text"}, {"paragraph_id": 8, "page_id": 18, "tags": [], "outline_level": -1, "text": "中国标准出版社出版", "type": "paragraph", "position": [758, 1206, 959, 1206, 959, 1223, 758, 1223], "content": 0, "sub_type": "text"}, {"paragraph_id": 9, "page_id": 18, "tags": [], "outline_level": -1, "text": "北京市朝阳区和平里西街甲2号（100029）", "type": "paragraph", "position": [696, 1230, 1020, 1230, 1020, 1245, 696, 1245], "content": 0, "sub_type": "text"}, {"paragraph_id": 10, "page_id": 18, "tags": [], "outline_level": -1, "text": "北京市西城区三里河北街16号（100045）", "type": "paragraph", "position": [697, 1258, 1018, 1258, 1018, 1274, 697, 1274], "content": 0, "sub_type": "text"}, {"paragraph_id": 11, "page_id": 18, "tags": [], "outline_level": -1, "text": "总编室：(010)68533533", "type": "paragraph", "position": [768, 1287, 951, 1287, 951, 1302, 768, 1302], "content": 0, "sub_type": "text"}, {"paragraph_id": 12, "page_id": 18, "tags": [], "outline_level": -1, "text": "网址 www.spc.net.cn", "type": "paragraph", "position": [765, 1319, 954, 1319, 954, 1334, 765, 1334], "content": 0, "sub_type": "text"}, {"paragraph_id": 13, "page_id": 18, "tags": [], "outline_level": -1, "text": "中国标准出版社秦皇岛印刷厂印刷", "type": "paragraph", "position": [722, 1346, 995, 1346, 995, 1362, 722, 1362], "content": 0, "sub_type": "text"}, {"paragraph_id": 14, "page_id": 18, "tags": [], "outline_level": -1, "text": "*", "type": "paragraph", "position": [852, 1376, 865, 1376, 865, 1390, 852, 1390], "content": 0, "sub_type": "text"}, {"paragraph_id": 15, "page_id": 18, "tags": [], "outline_level": -1, "text": "开本 880x1230 1/16 印张 1.25 字数28千字", "type": "paragraph", "position": [659, 1403, 1057, 1403, 1057, 1420, 659, 1420], "content": 0, "sub_type": "text"}, {"paragraph_id": 16, "page_id": 18, "tags": [], "outline_level": -1, "text": "2016年1月第一版 2016年1月第一次印刷", "type": "paragraph", "position": [681, 1432, 1036, 1432, 1036, 1448, 681, 1448], "content": 0, "sub_type": "text"}, {"paragraph_id": 17, "page_id": 18, "content": 0, "outline_level": -1, "text": "", "type": "image", "position": [109, 1383, 401, 1382, 402, 1520, 110, 1520], "image_url": "https://web-api.textin.com/ocr_image/external/2a2cfce9d9a96821.jpg", "sub_type": "barcode"}, {"paragraph_id": 18, "page_id": 18, "tags": [], "outline_level": -1, "text": "印数1-1 100", "type": "paragraph", "position": [803, 1461, 916, 1461, 916, 1475, 803, 1475], "content": 0, "sub_type": "text"}, {"paragraph_id": 19, "page_id": 18, "tags": [], "outline_level": -1, "text": "*", "type": "paragraph", "position": [852, 1492, 865, 1492, 865, 1505, 852, 1505], "content": 0, "sub_type": "text"}, {"paragraph_id": 20, "page_id": 18, "tags": [], "outline_level": -1, "text": "SN/T 4049-2014 书号：155066·2-29302 定价21.00元", "type": "paragraph", "position": [174, 1516, 1020, 1516, 1020, 1535, 174, 1535], "content": 0, "sub_type": "text"}]}, "x_request_id": "7c0dee0b59f07105ad2b822428b04020", "metrics": [{"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 1, "duration": 608.24346923828, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 2, "duration": 452.32620239258, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 3, "duration": 1474.841796875, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 4, "duration": 1590.525390625, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 5, "duration": 1715.0477294922, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 6, "duration": 2241.2822265625, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 7, "duration": 906.32464599609, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 8, "duration": 560.22576904297, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 9, "duration": 843.03839111328, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 10, "duration": 1276.951171875, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 11, "duration": 792.07897949219, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 12, "duration": 1342.8616943359, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 13, "duration": 1124.6870117188, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 14, "duration": 1324.2001953125, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 15, "duration": 887.39044189453, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 16, "duration": 462.78213500977, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 17, "duration": 430.09042358398, "page_image_width": 1191, "page_image_height": 1684}, {"angle": 0, "status": "Success", "dpi": 144, "image_id": "", "page_id": 18, "duration": 550.92132568359, "page_image_width": 1191, "page_image_height": 1684}], "duration": 2704, "message": "Success", "version": "3.16.9.1"}