<?php

namespace app\common\service\ai\engine;

use app\common\service\ai\BaseEngine;
use app\common\service\ai\BaseClient;
use app\common\service\IpService;
use Exception;
use think\Db;
use \app\admin\model\ai\Msg;
use app\admin\model\ai\Project;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\Queue;
use app\admin\model\ai\ConsoleFile;
use app\admin\model\ai\UrlData;
use app\common\service\BaiduSearchService;
use app\common\library\aliyun\VisionModel;

/** 本地增强模型类 */
class Localenhance extends BaseEngine
{
	# 硅基流动
	// private $url = "https://api.siliconflow.cn/v1/chat/completions";
	// private $token = "sk-xupsiedhwmitdoguckdruegjudnwaovmrznommcwiycqemjo";
	
	# 火山
	// private $url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions";
	// private $token = "87d2f9b6-a4e1-4004-8f79-79ba3676e1d6";

	# 阿里
	private $url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
	private $token = "sk-613ccddd0b0948179c815a0e11cd8ebf";
	private $model = 'deepseek-r1';
	private $references = [];
	private $save_references = [];
	private $lastMsgId;

	private $paramsType = false;
	private $paramsTypeArr = ["farui-plus","chatglm3-6b"]; # 参数类型模型
	public $debug = false;
	private $msg = '';
	public $group_id = 2;

	function generateUUIDv4() {
		$data = random_bytes(16);
		// 设置 UUID 版本 (4) 和 variant bits
		$data[6] = chr(ord($data[6]) & 0x0f | 0x40);
		$data[8] = chr(ord($data[8]) & 0x3f | 0x80);
	
		return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
	}

	/** 链接PGsql数据库 */
	private function pg_connect()
	{
		$request = Db::connect(
			[
				'type' => 'pgsql',
				'hostname' => '127.0.0.1',
				'database' => config('postgres.database'),
				'username' => 'postgres',
				'password' => 'DRrTmhCKrLWs2b34',
				'hostport' => '5432'
			]
		);
		return $request;
	}

	/*
	* 创建上下文缓存获取对话id
	*/
	public function getChatId()
	{
		$uuid = $this->generateUUIDv4();
		return $uuid;
	}


	/**
	 * 聊天方法
	 * @param int $console_type 1=知识库,2=全网
	 * @param int $contextId 对话ID
	 * @param string $msg 用户输入的消息
	 * @param int $robot 机器人ID
	 * @param mixed $card 是否有卡片信息
	 * @param string $lang 语言，默认'cn'
	 * @param mixed $model 模型信息（可选）
	 * @param bool $debug 调试模式
	 * @param array $ext 扩展参数，默认为空
	 * @return void
	 */
	public function chat($console_type, $contextId, $msg, $robot, $card, $lang = 'cn', $model = null, $debug = false, $ext = [])
	{
		$this->msg = $msg;
		$this->debug = $debug;
		$ip = request()->ip();
		$area = IpService::getArea($ip);

		// 获取项目与模型信息
		$project = $this->getProjectInfo($robot);

		$bigModel = $this->getModelInfo($project, $model);
		$this->setBigModelConfig($bigModel);

		$messages = [];

		// 处理系统提示词
		if ($project['system_prompt']) {
			$messages[] = [
				"role" => "system",
				"content" => $project['system_prompt']
			];
		}

		// 获取上下文消息
		$old_messages = $this->buildContextMessages($contextId);
		$messages = array_merge($messages, $old_messages);

		// 存在图片信息时调用视觉理解接口
		if(!empty($ext['image'])){
			$vision = new VisionModel();
			$result = $vision->analyze($ext['image'], $msg);
			if(!empty($result['content']))
			{
				$answerStr = $result['content'];
				// 获取引用内容
				if($console_type == 2){
					$vectorStr = $this->buildReferenceHtml($answerStr);
				}else{
					$vectorStr = $this->buildReferenceContent($project, $answerStr);
				}
				if ($this->references) {
					$this->save_references = $this->references;
				}
				$this->saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr, $ext['image']);
				// 发送消息到前端
				$result = json_encode([
					'text' => $answerStr,
					'reasoning_content' => '',
					'references' => $this->references,
					'msg_id' => $this->lastMsgId
				], JSON_UNESCAPED_UNICODE);
				$this->sendMessage($contextId, $result);
			}

			exit;
		}

		// 获取引用内容
		if($console_type == 2){
			$vectorStr = $this->buildReferenceHtml($msg);
		}else{
			$vectorStr = $this->buildReferenceContent($project, $msg);
		}
		$userPrompt = $this->buildSystemPrompt($msg ?? '', $lang, $vectorStr);
		// 用户输入
		$messages[] = [
			"role" => "user",
			"content" => $userPrompt
		];
		
		if($this->debug){
			dump($messages);exit;
		}

		// 构造请求参数
		$payload = $this->buildPayload($messages);
		$headers = $this->buildHeaders();

		$params = json_encode($payload);

		// 发起CURL请求
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $this->url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

		$answerStr = '';
		// $this->references = [];
		$this->saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr);

		curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) use (
			$lang, $contextId, $robot, $msg, $ip, $area, $card, $debug, &$answerStr
		) {
			// 检测 [DONE] 标识
			if (strpos($data, '[DONE]') !== false) {
				$data = str_replace('[DONE]', '', $data);
				ob_end_flush();
				if ($answerStr) {
					$this->saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr);
					// 异步请求卡片标签处理
					\fast\Http::sendAsyncRequest(request()->domain() . '/api/ai/card/tagExtractionOne', [
						'msg' => $msg,
						'robot' => $robot,
						'msg_id' => $this->lastMsgId ?? 0
					]);
				}
				exit;
			}

			$lines = explode("\n", trim(str_replace('data:', '', $data)));
			foreach ($lines as $line) {
				if (empty($line)) continue;
				$v = json_decode($line, true);
				if (empty($v)) continue;

				// 判断结束标识（针对部分模型返回）
				if ($this->paramsType && !empty($v['output']['choices'][0]['finish_reason'])
					&& $v['output']['choices'][0]['finish_reason'] === 'stop'
				) {
					ob_end_flush();
					if ($answerStr) {
						$this->saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr);
						\fast\Http::sendAsyncRequest(request()->domain() . '/api/ai/card/tagExtractionOne', [
							'msg' => $msg,
							'robot' => $robot,
							'msg_id' => $this->lastMsgId ?? 0
						]);
					}
					exit;
				}

				$reasoningContent = '';
				$text = $this->paramsType
					? ($v['output']['choices'][0]['message']['content'] ?? '')
					: ($v['choices'][0]['delta']['content'] ?? '');
				$reasoningContent = $this->paramsType ? '' : (empty($text) ? ($v['choices'][0]['delta']['reasoning_content'] ?? '') : '');

				// 翻译处理（若非中文）
				if ($lang && $lang != 'cn' && trim($text) !== '\n') {
					$this->translateReferences($lang);
				}

				$text = str_replace(["    ", "~"], ["", "—"], $text);
				if ($this->references) {
					$this->save_references = $this->references;
				}

				// 记录累计返回的内容
				$answerStr .= $text;
				// 发送消息到前端
				$result = json_encode([
					'text' => $text,
					'reasoning_content' => $reasoningContent,
					'references' => $this->references,
					'msg_id' => $this->lastMsgId
				], JSON_UNESCAPED_UNICODE);
				$this->references = [];
				$this->sendMessage($contextId, $result);
			}
			return strlen($data);
		});

		$response = curl_exec($ch);
		if (curl_errno($ch)) {
			echo 'Curl error: ' . curl_error($ch);
		}
		curl_close($ch);
	}

	/** 以下为辅助方法 **/

	/**
	 * 获取项目信息
	 */
	private function getProjectInfo($robot)
	{
		$project = Db::name('ai_project')->where('id', $robot)->find();
		if(!empty($project['console_ids']) && $this->group_id == 3)
		{
			$consoleArr = Db::name('ai_console')->where(['id'=>['in', explode(',', $project['console_ids'])], 'invoking_status'=>1])->column('id');
			$project['console_ids'] = implode(',', $consoleArr);
		}
		return $project;
	}

	/**
	 * 获取模型信息
	 */
	private function getModelInfo($project, $model)
	{
		$where = $model ? ['model' => $model] : ['id' => $project['model_id']];
		return Db::name('ai_bigmodel')->where($where)->find();
	}

	/**
	 * 设置大模型配置信息
	 */
	private function setBigModelConfig($bigModel)
	{
		$this->url = $bigModel['api_url'];
		$this->token = $bigModel['api_key'];
		$this->model = $bigModel['model'];
		$this->paramsType = in_array($this->model, $this->paramsTypeArr);
	}

	/**
	 * 构造对话上下文（取最近三条消息）
	 */
	private function buildContextMessages($contextId)
	{
		$messages = [];
		$msgList = Msg::where('chatId', $contextId)->order('id', 'desc')->limit(3)->select();
		foreach ($msgList as $item) {
			$messages[] = [
				"role" => "user",
				"content" => $item['msg']
			];
			$content = mb_substr($item['content'], 0, 50);
			$messages[] = [
				"role" => "assistant",
				"content" => $content
			];
		}
		return $messages;
	}

	/**
	 * 构造引用内容
	 */
	private function buildReferenceContent($project, $msg)
	{
		$vectorStr = '';
		$vectorId = 1;
		$vectorList = $this->queryVector($project, $msg);
		$vectorScore = $project['vector_score']>0 ? $project['vector_score'] : 0.3;
		$list = [];
		foreach ($vectorList as &$item) {
			if ((isset($item['cosine_similarity']) && $item['cosine_similarity'] > $vectorScore) || (isset($item['relevance_score']) && $item['relevance_score'] > $vectorScore)) {
				$consoleFile = ConsoleFile::where('id', $item['file_id'])->find();
				if($consoleFile)
				{
					$file_number = $consoleFile['file_number'] ?? 0;
					if(!empty($consoleFile['type']) && $consoleFile['type'] == 5){
						$urlData = UrlData::where('file_number', $file_number)->find();
						$fileName = !empty($urlData['title']) ? $urlData['title'] : $consoleFile['name'];
						$filePath = !empty($urlData['url']) ? $urlData['url'] : cdnurl($consoleFile['file'], true);
					}else{
						$fileName = !empty($consoleFile['name']) ? $consoleFile['name'] : '';
						$filePath = !empty($consoleFile['file'])? cdnurl($consoleFile['file'], true) : '';
					}
					$item['title'] = $fileName;
					$item['file'] = $filePath;
					$item['file_id'] = $item['file_id'];
					$item['file_number'] = $file_number;
					$list[] = $item;
				}
			}
		}
		$query = $this->pg_connect();
		// 尝试获取相邻切片以恢复完整内容
		if($list)
		{
			$list = $this->retrieveAdjacentSlices($list, $query, $project);
		
			// 按相似度排序
			$list = $this->reorderReferences($list);
			$vectorNum = $project['vector_num'] > 0 ? $project['vector_num'] : 6;
			$list = array_slice($list, 0, $vectorNum);

			$mergedReferences = [];

			foreach ($list as $reference) {
				$fileId = $reference['file_id'];

				if (isset($mergedReferences[$fileId])) {
					// 如果已存在相同的 file_id，则合并 content
					$mergedReferences[$fileId]['content'] .= "\n" . $reference['content'];
				} else {
					// 否则直接存储
					$mergedReferences[$fileId] = $reference;
				}
			}

			// 转换为索引数组（去除键名）
			$list = array_values($mergedReferences);

			foreach ($list as $item) {
				$vectorStr .= "[{$vectorId}] " . $item['title'] . "：" . $item['content'] . "\n";
				$slice = $item['content'];
				$this->references[] = [
					'title'   => $item['title'],
					'content' => $slice,
					'file'    => $item['file'],
					'cosine_similarity' => $item['cosine_similarity'] ?? 0,
					'relevance_score' => $item['relevance_score'] ?? 0,
					'document_id' => $item['file_number'],
					'file_id' => $item['file_id']
				];
				$vectorId++;
			}
		}
		return $vectorStr;
	}

	/**
	 * 构造引用网页
	 */
	private function buildReferenceHtml($query)
	{
		$baiduSearch = new BaiduSearchService();
        try {
            $result = $baiduSearch->search($query, [
                'site' => [],
                'time_range' => 'year'
            ]);
			$vectorStr = '';
			$vectorId = 1;
			foreach($result['references'] as $item)
			{
				$vectorStr .= "[{$vectorId}] " . $item['title'] . "：" . $item['content'] . "\n";
				$this->references[] = [
					'title'   => $item['title'],
					'content' => $item['content'],
					'file'    => $item['url'],
					'cosine_similarity' => 0,
					'relevance_score' => 0
				];
				$vectorId++;
			}
			return $vectorStr;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
	}

	/**
	 * 构造系统提示词
	 */
	private function buildSystemPrompt($basePrompt, $lang, $vectorStr)
	{
		if ($lang && $lang != 'cn') {
			$basePrompt .= " 请只用英文回答。不要重复问题。提供一个直接的答案。";
		}

		if ($vectorStr) {
			// 构建基础规则
			$rules = [
				"只使用参考资料中的信息来回答。",
				"不要直接引用或重复原文内容。",
				"不要在答案中包含任何推理或指导文本。",
				"忽略参考内容中的图片、视频或链接。"
			];
			
			// 只有当 group_id 不为 3 时才添加引用格式说明
			if ($this->group_id != 3) {
				$rules[] = "根据下面的编号参考，回答用户的问题。";
				$rules[] = "在你的答案中，用带数字的方括号指出来源，例如，^[1]^,^[2]^。";
			}
			
			$rulesStr = implode("\n", array_map(function($rule) {
				return "					" . $rule;
			}, $rules));

			return <<<PROMPT
				任务和规则:
					{$rulesStr}
				以下是检索到的相关文档内容:
					{$vectorStr}
				提问问题：
				{$basePrompt}
			PROMPT;
		} elseif ($basePrompt) {
			return $basePrompt;
		}
		return '';
	}


	/**
	 * 构造请求payload
	 */
	private function buildPayload($messages)
	{
		$payload = [
			"model"              => $this->model,
			"messages"           => $messages,
			"stream"             => true,
			"max_tokens"         => 2096,
			"stop"               => ["null"],
			"temperature"        => 0.2,
			"top_p"              => 0.2,
			"top_k"              => 20,
			"frequency_penalty"  => 1,
			"n"                  => 1,
			"response_format"    => ["type" => "text"],
			// "usage"              => [
			// 	"prompt_tokens"         => 3019,
			// 	"completion_tokens"     => 104,
			// 	"total_tokens"          => 3123,
			// 	"prompt_tokens_details" => [
			// 		"cached_tokens" => 2048
			// 	]
			// ]
		];
		if ($this->paramsType) {
			$payload = array_merge($payload, [
				"parameters" => [
					"result_format"      => "message",
					"incremental_output" => true
				],
				"input" => [
					"messages" => $messages
				]
			]);
		}
		return $payload;
	}

	/**
	 * 构造请求头
	 */
	private function buildHeaders()
	{
		$headers = [
			'Content-Type:application/json',
			'Authorization:Bearer ' . $this->token
		];
		if ($this->paramsType) {
			$headers[] = 'X-DashScope-SSE:enable';
		}
		return $headers;
	}

	/**
	 * 翻译引用内容
	 */
	private function translateReferences($targetLang)
	{
		$Translation = new \app\common\library\Translation();
		foreach ($this->references as $rKey => &$rValue) {
			$rValue['title'] = $Translation->xfyun($rValue['title'], 'cn', $targetLang);
			$rValue['content'] = $Translation->xfyun($rValue['content'], 'cn', $targetLang);
		}
	}

	/**
	 * 保存问答记录
	 */
	private function saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr, $image = '')
	{
		$referencesList = base64_encode(json_encode($this->save_references ?? []));
		$msgShort = mb_substr($msg, 0, 150);
		if($this->lastMsgId)
		{
			Db::name('ai_msg')->where('id', $this->lastMsgId)->update([
				'content' => $answerStr,
				'referrer'=> $referencesList,
			]);
		}else{
			$this->lastMsgId = Db::name('ai_msg')->insertGetId([
				'robot'   => $robot,
				'chatId'  => $contextId,
				'msg'     => $msgShort,
				'time'    => time(),
				'ip'      => $ip,
				'content' => $answerStr,
				'city'    => $area,
				'referrer'=> $referencesList,
				'card'    => $card,
				'lang'    => $lang,
				'model'   => $this->model,
				'images'  => $image
			]);
		}
	}

	/** 
	 * 调用生成向量数组匹配对应关系
	 */
	function getEmbeddingsNew($arr) {
		$input = json_encode(['input' => $arr]);

		$embeddings = new \app\common\library\Embeddings;
		$embeddingsStr = $embeddings->run($input);
		$embeddingsArr = json_decode($embeddingsStr, true);

		// 检查 data 键是否存在
		if (!isset($embeddingsArr['data'])) {
			usleep(500000); // 停顿 0.5 秒
			$embeddingsStr = $embeddings->run($input); // 重试
			$embeddingsArr = json_decode($embeddingsStr, true);
		}

		return $embeddingsArr['data'] ?? []; // 如果 data 不存在，返回空数组或其他默认值
	}


	/** 查询向量库 */
	private function queryVector($project, $text)
	{
		$vectorNum = 100;
		$query = $this->pg_connect();
		$text = mb_strcut($text, 0, 384);
		$vectorArr = $this->getEmbeddingsNew([$text]);

		$vectorContent = json_encode(['content'=>$vectorArr[0]['embedding']]);
		
		$vectorList = json_decode($vectorContent, true);
		$list = [];
		
		if (!empty($vectorList['content'])) {
			$queryVector = json_encode($vectorList['content']);
			
			// 先从slice_qa表查询相关切片
			$query->query("
				SET hnsw.ef_search = 50;
			");
			$qaSlices = $query->query("
				SELECT 
					vdu.id,
					vdu.file_id,
					vdu.content,
					1 - (slice_qa.embedding <=> '{$queryVector}') AS cosine_similarity,
					slice_qa.extracted_question,
					slice_qa.extracted_answer
				FROM slice_qa
				JOIN vector_data_upgrades vdu ON vdu.id = slice_qa.slice_id
				WHERE vdu.console_id IN ({$project['console_ids']})
				AND vdu.delete_time IS NULL
				ORDER BY slice_qa.embedding <=> '{$queryVector}'
				LIMIT 50;
			");
			
			if($this->debug && false){
				dump("
					SET hnsw.ef_search = 100;
					SELECT 
						vdu.id,
						vdu.file_id,
						vdu.content,
						1 - (slice_qa.embedding <=> '{$queryVector}') AS cosine_similarity,
						slice_qa.extracted_question,
						slice_qa.extracted_answer
					FROM slice_qa
					JOIN vector_data_upgrades vdu ON vdu.id = slice_qa.slice_id
					WHERE vdu.console_id IN ({$project['console_ids']})
					AND vdu.delete_time IS NULL
					ORDER BY slice_qa.embedding <=> '{$queryVector}'
					LIMIT 50;
				");

				dump($qaSlices);
			}

			// 如果qa结果不足，补充原始切片
			if (count($qaSlices) < $vectorNum) {
				$remainingNum = $vectorNum - count($qaSlices);
				$existingIds = array_column($qaSlices, 'id');
				$excludeIds = empty($existingIds) ? "0" : implode(',', $existingIds);
				$query->query("SET hnsw.ef_search = 100;");
				$regularSlices = $query->query("
					SELECT 
						v.id,
						v.file_id,
						v.content,
						1 - (v.embedding <=> '{$queryVector}') AS cosine_similarity,
						NULL AS extracted_question,
						NULL AS extracted_answer
					FROM vector_data_upgrades v
					LEFT JOIN (
						SELECT unnest(array[{$excludeIds}]) AS id_to_exclude
					) ex ON v.id = ex.id_to_exclude
					WHERE v.console_id IN ({$project['console_ids']})
						AND ex.id_to_exclude IS NULL
						AND v.delete_time IS NULL
					ORDER BY v.embedding <=> '{$queryVector}'
					LIMIT {$remainingNum};
				");

				if($this->debug && false){
					dump("
						SET hnsw.ef_search = 100;

						SELECT 
							v.id,
							v.file_id,
							v.content,
							1 - (v.embedding <=> '{$queryVector}') AS cosine_similarity,
							NULL AS extracted_question,
							NULL AS extracted_answer
						FROM vector_data_upgrades v
						LEFT JOIN (
							SELECT unnest(array[{$excludeIds}]) AS id_to_exclude
						) ex ON v.id = ex.id_to_exclude
						WHERE v.console_id IN ({$project['console_ids']})
							AND ex.id_to_exclude IS NULL
							AND v.delete_time IS NULL
						ORDER BY v.embedding <=> '{$queryVector}'
						LIMIT {$remainingNum};
				");
	
					dump($regularSlices);
				}

				// 合并结果
				$list = array_merge($qaSlices, $regularSlices);
			} else {
				$list = $qaSlices;
			}
			
			
			// 记录日志
			if ($this->debug) {
				$logData = [
					'query' => $text,
					'vector' => $queryVector,
					'results' => $list,
					'time' => date('Y-m-d H:i:s')
				];
				error_log(
					date("Y-m-d H:i:s") . "|vector_query:" . print_r($logData, true) . "\r\n",
					3,
					ROOT_PATH . "/runtime/log/" . date("Ym") . "/vector_query.log"
				);
			}
		}

		return $this->processResults($list);
	}

	

	/**
	 * 重排引用内容
	 */
	private function reorderReferences($references, $msg = '')
	{
		$batchSize = 40;
		$path = '/mnt/sdc/wwwroot/ai-master/python/qianwen/rerank.py';
		$msgEscaped = str_replace(['"', "'"], ['“', "’"], $msg?$msg:$this->msg);

		$sortedReferences = []; // 存储排序结果（引用键 => relevance_score）
		$unusedKeys = [];       // 未参与排序的 keys（没有出现在 rerank 结果中）

		$batches = array_chunk($references, $batchSize, true);
		foreach ($batches as $batch) {
			$vectorArr = [];
			$keyMap = [];

			foreach ($batch as $originalKey => $v) {
				$content = mb_substr((isset($v['title'])?$v['title'].":":'').$v['content'], 0, 2000);
				$escapedContent = str_replace(['"', "'", "(", ")"], ['“', "’", "（", "）"], $content);
				$vectorArr[] = $escapedContent;
				$keyMap[] = $originalKey; // 映射 index => 原始 key
			}

			$vectorStr = implode('@@', $vectorArr);
			$cmd = $this->pythonPath . ' ' . $path . ' "%s" "%s" ';
			$return = [];
			exec(sprintf($cmd, $msgEscaped, $vectorStr), $return, $returnCode);

			$usedKeys = [];

			if (!empty($return[0])) {
				$arr = json_decode($return[0], true);
				if (!empty($arr['output']['results'])) {
					foreach ($arr['output']['results'] as $result) {
						$indexInBatch = $result['index'];
						if(!empty($keyMap[$indexInBatch]))
						{
							$key = $keyMap[$indexInBatch];
							$sortedReferences[] = [
								'key' => $key,
								'relevance_score' => $result['relevance_score']
							];
							$usedKeys[] = $key;
						}
					}
				}
			}

			// 剩下没被返回的 key，加入 unused 列表
			$unusedKeys = array_merge($unusedKeys, array_diff($keyMap, $usedKeys));
		}

		// 根据排序结果重建 references 顺序
		$final = [];

		foreach ($sortedReferences as $item) {
			$key = $item['key'];
			$references[$key]['relevance_score'] = $item['relevance_score'];
			$final[] = $references[$key];
		}

		// 追加未排序的
		foreach ($unusedKeys as $key) {
			$references[$key]['relevance_score'] = 0;
			$final[] = $references[$key];
		}
		// 按 relevance_score 倒序排序
		usort($final, function ($a, $b) {
			return ($b['relevance_score'] ?? 0) <=> ($a['relevance_score'] ?? 0);
		});

		return $final;
	}

	/**
	 * 获取相邻切片以恢复完整内容
	 * @param array $slices 原始切片结果
	 * @param object $query 数据库连接
	 * @param array $project 项目信息
	 * @return array 增强后的切片结果
	 */
	private function retrieveAdjacentSlices($slices, $query, $project)
	{
		$enhancedSlices = [];
		$processedIds = [];
		
		foreach ($slices as $slice) {
			// 如果已处理过该切片，跳过
			if (in_array($slice['id'], $processedIds)) {
				continue;
			}
			
			$processedIds[] = $slice['id'];
			
			// 检查是否包含关键词，表明内容可能被截断
			$isIncomplete = $this->isIncompleteContent($slice['content']);
			
			// 如果内容看起来不完整或包含步骤关键词，尝试获取相邻切片
			if ($isIncomplete || $this->containsStepKeywords($slice['content'])) {
				// 直接查询相邻ID的切片（前后各2个）
				$adjacentSlices = $query->query("
					SELECT id, content, file_id
					FROM vector_data_upgrades
					WHERE file_id = {$slice['file_id']}
					AND console_id IN ({$project['console_ids']})
					AND delete_time IS NULL
					AND id BETWEEN {$slice['id']} - 1 AND {$slice['id']} + 1
					ORDER BY id ASC
				");
				
				// 如果找不到相邻切片，使用原始切片
				if (empty($adjacentSlices)) {
					$enhancedSlices[] = $slice;
					continue;
				}
				
				// 合并相邻切片内容
				$combinedContent = '';
				foreach ($adjacentSlices as $adjacentSlice) {
					$combinedContent .= $adjacentSlice['content'] . "\n";
					// 将处理过的切片ID添加到已处理列表
					$processedIds[] = $adjacentSlice['id'];
				}
				
				// 创建新的合并切片
				$newSlice = $slice;
				$newSlice['content'] = $this->cleanupCombinedContent($combinedContent);
				$newSlice['is_combined'] = true;
				
				$enhancedSlices[] = $newSlice;
			} else {
				$enhancedSlices[] = $slice;
			}
		}
		
		return $enhancedSlices;
	}

	/**
	 * 清理合并后的内容，去除重复段落和格式化
	 * @param string $content 合并后的内容
	 * @return string 清理后的内容
	 */
	private function cleanupCombinedContent($content)
	{
		// 按行分割
		$lines = explode("\n", $content);
		$uniqueLines = [];
		$seenLines = [];
		
		foreach ($lines as $line) {
			$trimmedLine = trim($line);
			if (empty($trimmedLine)) continue;
			
			// 使用MD5作为行内容的唯一标识
			$lineHash = md5($trimmedLine);
			
			// 如果这行内容之前没见过，添加到结果中
			if (!isset($seenLines[$lineHash])) {
				$uniqueLines[] = $trimmedLine;
				$seenLines[$lineHash] = true;
			}
		}
		
		// 重新组合内容
		return implode("\n", $uniqueLines);
	}

	/**
	 * 判断内容是否不完整
	 * @param string $content 切片内容
	 * @return bool 是否不完整
	 */
	private function isIncompleteContent($content)
	{
		// 检查内容是否以数字+括号结尾，表示可能被截断的步骤
		if (preg_match('/\d+[\)）]\s*$/', $content)) {
			return true;
		}
		
		// 检查内容是否以不完整的句子结尾
		if (preg_match('/[,;，；]\s*$/', $content)) {
			return true;
		}
		
		// 检查是否以数字结尾，可能是被截断的编号
		if (preg_match('/\d+\s*$/', $content)) {
			return true;
		}
		
		// 检查是否包含"步骤"、"开车"、"停车"等关键词，但内容较短
		if ($this->containsStepKeywords($content) && mb_strlen($content) < 300) {
			return true;
		}
		
		// 检查是否包含序号但数量少于3个，表示可能是不完整的列表
		preg_match_all('/\d+[\)）]/', $content, $matches);
		if (!empty($matches[0]) && count($matches[0]) < 3) {
			return true;
		}
		
		// 检查是否包含数值范围表达式但被截断
		if (preg_match('/~\s*\d+(\.\d+)?[a-zA-Z]*\s*$/', $content)) {
			return true;
		}
		
		// 检查是否包含单位但没有后续内容，可能是被截断的测量值
		if (preg_match('/\d+(\.\d+)?\s*[a-zA-Z]+\s*$/', $content)) {
			return true;
		}
		
		// 检查是否包含开车/停车步骤但步骤数量少于预期
		if ($this->containsStepKeywords($content)) {
			preg_match_all('/\d+[\)）]/', $content, $stepMatches);
			$lastStepNumber = !empty($stepMatches[0]) ? (int)preg_replace('/[^\d]/', '', end($stepMatches[0])) : 0;
			
			// 如果最后一个步骤编号小于10且内容不长，可能是不完整的
			if ($lastStepNumber > 0 && $lastStepNumber < 10 && mb_strlen($content) < 800) {
				return true;
			}
		}
		
		return false;
	}
	
	/**
	 * 检查内容是否包含步骤相关关键词
	 * @param string $content 切片内容
	 * @return bool 是否包含步骤关键词
	 */
	private function containsStepKeywords($content)
	{
		$keywords = ['步骤', '开车', '停车', '操作', '流程', '顺序', '方法', '程序', '启动', '关闭'];
		foreach ($keywords as $keyword) {
			if (strpos($content, $keyword) !== false) {
				return true;
			}
		}
		return false;
	}
	
	/**
	 * 判断两个切片是否是连续的步骤
	 * @param string $prevContent 前一个切片内容
	 * @param string $nextContent 后一个切片内容
	 * @return bool 是否是连续步骤
	 */
	private function isSequentialStep($prevContent, $nextContent)
	{
		// 提取前一个切片中的最后一个步骤编号
		preg_match_all('/(\d+)[\)）]/', $prevContent, $prevMatches);
		// 提取后一个切片中的第一个步骤编号
		preg_match('/(\d+)[\)）]/', $nextContent, $nextMatch);
		
		if (!empty($prevMatches[1]) && !empty($nextMatch[1])) {
			$lastPrevNum = (int)end($prevMatches[1]);
			$firstNextNum = (int)$nextMatch[1];
			
			// 如果编号连续，则认为是连续步骤
			if ($firstNextNum == $lastPrevNum + 1) {
				return true;
			}
		}
		
		// 检查是否有其他连续性指标
		// 例如，前一个切片以不完整的句子结尾，后一个切片是其延续
		if (preg_match('/[,;，；]\s*$/', $prevContent)) {
			return true;
		}
		
		return false;
	}

	/**
	 * 处理查询结果
	 */
	private function processResults($results)
	{
		foreach ($results as &$result) {
			// 如果存在QA信息，增强内容
			if (!empty($result['extracted_question']) && !empty($result['extracted_answer'])) {
				$result['content'] = sprintf(
					"问题：%s\n回答：%s\n原文：%s",
					$result['extracted_question'],
					$result['extracted_answer'],
					$result['content']
				);
			}
			
			// 清理不需要的字段
			unset($result['extracted_question'], $result['extracted_answer']);
		}
		return $results;
	}

	/*
	* 非流式调用模型对话，直接返回结果
	*/
	public function chatAndReturn($modelId,$conversation_id,$msg,$robot)
	{
		# 获取项目信息
		$project = Db::name('ai_project')->where('id',$robot)->find();
		$console_ids = $project['console_ids'];

		# 获取模型信息
		$bigmodel = Db::name('ai_bigmodel')->where('id', $project['model_id'])->find();
		$this->url = $bigmodel['api_url'];
		$this->token = $bigmodel['api_key'];
		$this->model = $bigmodel['model'];
		$this->paramsType = in_array($this->model, $this->paramsTypeArr);

		/** 上下文 */
		$messages = [];
		// if(!in_array($this->model, $this->contextCacheArr)){
		// 	$msgList = Msg::where('chatId',$contextId)->order('id', 'desc')->limit(3)->select();
		// 	foreach($msgList as $item){
		// 		$messages[] = [
		// 			"role" => "user",
		// 			"content" => $item['msg']
		// 		];
		// 		$content = mb_substr($item['content'], 0, 50);
		// 		$messages[] = [
		// 			"role" => "assistant",
		// 			"content" => $content
		// 		];
		// 	}
		// }
		$messages[] = [
			"role" => "user",
			"content" => $msg
		];

		# 查询切片
		// $vectorList = $this->queryVector($console_ids, $msg);
		// dump($vectorList);exit;
		// $vectorStr = '';
		// $vectorId = 1;
		// // $this->references = [];
		// foreach($vectorList as $item){

		// 	if (count($this->references) > 2) {
		// 		break;
		// 	}

		// 	if($item['cosine_similarity']>0.3){
		// 		$vectorStr .= "[{$vectorId}] " . $item['content'] . "\n";
		// 		$vectorId++;
		// 		$consoleFile = Db::name('ai_console_file')->where('id',$item['file_id'])->find();
		// 		$slice = $robot == 61? '' : $item['content'];
		// 		$this->references[] = [
		// 			'title' => $consoleFile['name'] ?? '',
		// 			'content' => $slice,
		// 			'file' => !empty($consoleFile['file']) ? cdnurl($consoleFile['file'], true) : ''
		// 		];
		// 	}
		// }
		$systemPrompt = $project['system_prompt'] ?? '';

		# 区分上下文关系，是否需要引用上文内容
		$systemPrompt = "首先需要分析用户问题是否和上文相关，如果无相关联，请不要引用上文内容，直接回答用户问题。" . $systemPrompt;
        // if ($lang && $lang != 'cn') {
        //     $systemPrompt .= "前提：请用英文回答，不能使用汉字。不要复述问题。直接的回答。" ;
        // }

		// /** 系统提示词 */
		// if($vectorStr)
		// {
		// 	$messages[] = [
		// 		"role" => "system",
		// 		"content" => <<<jjj
		// 			{$systemPrompt}
		// 			请基于以下内容整理并回答用户问题；如果下述内容中无相关信息，请自行作答。回答时不得展示或引用任何下述内容内部信息，且仅在最终答案末尾标注。
					
		// 			要求：
		// 			1. 回答内容只针对用户问题，不展示提示词或下述内容内部信息。
		// 			2. 内部推理过程仅用于生成答案，最终回答中不包含任何内部思考细节或提示词内容。
		// 			3. 出现图片、视频等信息时，请忽略相关内容。
		// 			4. 回答仅依据问题核心进行独立分析，不依赖或重复提示词说明。
		// 			----
		// 			{$vectorStr}
		// 			----
		// 		jjj
		// 	];
		// } else if($systemPrompt) {
		// 	$messages[] = [
		// 		"role" => "system",
		// 		"content" => <<<jjj
		// 			{$systemPrompt}
		// 		jjj
		// 	];
		// }

		// dump($messages);


		// $model = "deepseek-r1";
		$stream = false;
		$maxTokens = 2096;
		$stop = ["null"];
		$temperature = 0.3;
		$topP = 0.3;
		$topK = 50;
		$frequencyPenalty = 1;
		$n = 1;
		$responseFormat = ["type" => "text"];
		$tools = [
			[
				"type" => "function",
				"function" => [
					"description" => "<string>",
					"name" => "<string>",
					"parameters" => [],
					"strict" => false
				]
			]
		];

		$payload = [
			"model" => $this->model,
			"messages" => $messages,
			"stream" => $stream,
			"max_tokens" => $maxTokens,
			"stop" => $stop,
			"temperature" => $temperature,
			"top_p" => $topP,
			"top_k" => $topK,
			"frequency_penalty" => $frequencyPenalty,
			"n" => $n,
			"response_format" => $responseFormat,
			"tools" => $tools,
			"usage" => [
				"prompt_tokens" => 3019,
				"completion_tokens" => 104,
				"total_tokens" => 3123,
				"prompt_tokens_details" => [
					"cached_tokens" => 2048
				]
			]
			// "tools" => $tools
		];
		if($this->paramsType){
			$payload = array_merge($payload,[
				"parameters" => [
					"result_format" => "message",
					"incremental_output" => true
				],
				"input" => [
					"messages" => $messages
				]
			]);
		}

		// dump($payload);die;

		$headers = [
			'Content-Type:application/json',
			'Authorization:Bearer ' . $this->token
		];

		if($this->paramsType){
			$headers = array_merge($headers,[
				'X-DashScope-SSE:enable'
			]);
		}

		$answerStr = '';
		$ip = request()->ip();
		$area = IpService::getArea($ip);
		$params = json_encode($payload);
		// dump($payload);
		// dump($headers);
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $this->url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		
		$response = curl_exec($ch);
		if (curl_errno($ch)) {
		    echo 'Curl error: ' . curl_error($ch);
		}
		curl_close($ch);

		$res = json_decode($response,true);
		if (!$res) {
            exception('接口响应错误:' . $response);
		}
		// if($this->paramsType){
		// 	$text = $res['output']['choices'][0]['message']['content'] ?? '';
		// }else{
			$text = $res['choices'][0]['message']['content'] ?? '';
			// $reasoning_content = $res['choices'][0]['delta']['reasoning_content'] ?? '';
		// }
		return json(['answer' => $text,'references' => $this->references]);

		// return $text;
	}
}
