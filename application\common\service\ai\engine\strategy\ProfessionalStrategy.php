<?php

namespace app\common\service\ai\engine\strategy;

use app\admin\model\ai\ConsoleFile;

/**
 * 专业策略类 - 对应原 Localprofessional.php 的功能
 */
class ProfessionalStrategy extends EnhanceStrategy
{
    private $vectorCacheList = [];
    
    /**
     * 构造引用内容 - 专业版本，支持查询增强和术语库
     */
    public function buildReferenceContent($project, $msg, $engine)
    {
        $vectorStr = '';
        $vectorId = 1;
        $query = $engine->getPgConnect();

        $originalQuery = $msg;
        $augmentedQuery = $originalQuery;
        $termMap = $originalArr = $fileArr = $augmentedArr =  [];
        
        if($project['query_augmentation']==1){
            $translator = new \app\common\library\QwenTranslator();
            $termMap = $this->loadTermMap($project['id']);
            $augmentedQuery .= $translator->translate($engine->getMsg(), $termMap);
        }
        
        // 标题级向量召回
        $fileArr = $this->getDocumentSlicesByTitle($augmentedQuery, $engine);
        // 双路召回+融合
        $originalArr = $this->queryVector($project, $originalQuery);
        $augmentedArr = ($augmentedQuery !== $originalQuery) ? $this->queryVector($project, $augmentedQuery) : [];

        // 用于存储已存在的 id，防止重复
        $existingIds = [];

        // 初始化最终结果数组
        $vectorList = [];

        // 合并并按 id 去重
        foreach (array_merge($originalArr, $augmentedArr, $fileArr) as $item) {
            if (!empty($item['id']) && !isset($existingIds[$item['id']])) {
                $existingIds[$item['id']] = true;
                $consoleFile = ConsoleFile::where('id', $item['file_id'])->find();
                if($consoleFile)
                {
                    $fileName = !empty($consoleFile['file']) ? cdnurl($consoleFile['file'], true) : '';
                    $file_number = $consoleFile['file_number'] ?? 0;
                    $slice = $item['content'];
                    $vectorList[] = [
                        'id' => $item['id'],
                        'file_id' => $item['file_id'],
                        'title' => $consoleFile['name'] ?? '',
                        'content' => $slice,
                        'file' => $fileName,
                        'cosine_similarity' => $item['cosine_similarity'] ?? 0,
                        'relevance_score' => $item['relevance_score'] ?? 0,
                        'document_id' => $file_number
                    ];
                }
            }
        }
        
        // 按相似度排序
        $vectorList = $this->reorderReferences($vectorList, $augmentedQuery);
        $vectorScore = $project['vector_score']>0 ? $project['vector_score'] : 0.3;
        
        $references = []; // 清空之前的引用内容

        // 初始相似度阈值
        $minScore = 0.15; // 最低允许的相似度
        $step = 0.1;    // 每次降档步长
        $targetCount = 6; // 目标引用数量

        foreach ($vectorList as $item) {
            if ((isset($item['relevance_score']) && $item['relevance_score'] > $vectorScore)) {
                $references[] = $item;
            }
        }

        // 如果引用不足，逐步降低阈值补充
        while (count($references) < $targetCount && $vectorScore > $minScore) {
            $vectorScore -= $step;

            foreach ($vectorList as $item) {
                // 排除已添加的引用
                $exists = false;
                foreach ($references as $ref) {
                    if ($ref['id'] == $item['id']) {
                        $exists = true;
                        break;
                    }
                }
                if ($exists) continue;

                if (isset($item['relevance_score']) && $item['relevance_score'] > $vectorScore) {
                    $references[] = $item;
                }

                // 达到目标数量后跳出循环
                if (count($references) >= $targetCount) break;
            }
        }	
        
        // 尝试获取相邻切片以恢复完整内容
        $references = $this->retrieveAdjacentSlices($references, $query, $project);

        $vectorNum = $project['vector_num'] > 0 ? $project['vector_num'] : 6;
        $references = array_slice($references, 0, $vectorNum);

        $mergedReferences = [];

        foreach ($references as $reference) {
            $fileId = $reference['file_id'];

            if (isset($mergedReferences[$fileId])) {
                // 如果已存在相同的 file_id，则合并 content
                $mergedReferences[$fileId]['content'] .= "\n" . $reference['content'];
            } else {
                // 否则直接存储
                $mergedReferences[$fileId] = $reference;
            }
        }

        // 转换为索引数组（去除键名）
        $references = array_values($mergedReferences);

        foreach ($references as $item) {
            $content = mb_substr($item['content'], 0, 1500);
            $vectorStr .= "[{$vectorId}] " . $item['title'] . "：" . $content . "\n";
            $vectorId++;
        }
        
        $engine->setReferences($references);
        return $vectorStr;
    }
    
    /**
     * 构造请求payload - 专业版本
     */
    public function buildPayload($messages, $engine)
    {
        return [
            "model"              => $engine->model,
            "messages"           => $messages,
            "stream"             => true,
            "max_tokens"         => 4096,
            "stop"               => ["null"],
            "temperature"        => 0.2,
            "top_p"              => 0.2,
            "top_k"              => 20,
            "frequency_penalty"  => 1,
            "n"                  => 1,
            "response_format"    => ["type" => "text"],
            "enable_thinking"    => true
        ];
    }
    
    /**
     * 查询向量库 - 专业版本
     */
    protected function queryVector($project, $text)
    {
        $vectorNum = 60;
        $query = $this->engine->getPgConnect();

        if(!isset($this->vectorCacheList[md5($text)]))
        {
            $text = mb_strcut($text, 0, 384);
            $vectorArr = $this->getEmbeddingsNew([$text]);
            usleep(500000);
        }else{
            $vectorArr = $this->vectorCacheList[md5($text)];
        }

        $vectorContent = json_encode(['content'=>$vectorArr[0]['embedding']]);
        
        $vectorList = json_decode($vectorContent, true);
        $list = [];
        
        if (!empty($vectorList['content'])) {
            $queryVector = json_encode($vectorList['content']);
            
            // 先从slice_qa表查询相关切片
            $query->query("SET hnsw.ef_search = 100;");
            $qaSlices = $query->query("
                SELECT 
                    vdu.id,
                    vdu.file_id,
                    vdu.content,
                    1 - (slice_qa.embedding <=> '{$queryVector}') AS cosine_similarity,
                    slice_qa.extracted_question,
                    slice_qa.extracted_answer
                FROM slice_qa
                JOIN vector_data_upgrades vdu ON vdu.id = slice_qa.slice_id
                WHERE vdu.console_id IN ({$project['console_ids']})
                AND vdu.delete_time IS NULL
                ORDER BY slice_qa.embedding <=> '{$queryVector}'
                LIMIT 30;
            ");

            // 如果qa结果不足，补充原始切片
            if (count($qaSlices) < $vectorNum) {
                $remainingNum = $vectorNum - count($qaSlices);
                $existingIds = array_column($qaSlices, 'id');
                $excludeIds = empty($existingIds) ? "0" : implode(',', $existingIds);
                $query->query("SET hnsw.ef_search = 100;");
                $regularSlices = $query->query("
                    SELECT 
                        v.id,
                        v.file_id,
                        v.content,
                        1 - (v.embedding <=> '{$queryVector}') AS cosine_similarity,
                        NULL AS extracted_question,
                        NULL AS extracted_answer
                    FROM vector_data_upgrades v
                    LEFT JOIN (
                        SELECT unnest(array[{$excludeIds}]) AS id_to_exclude
                    ) ex ON v.id = ex.id_to_exclude
                    WHERE v.console_id IN ({$project['console_ids']})
                        AND ex.id_to_exclude IS NULL
                        AND v.delete_time IS NULL
                    ORDER BY v.embedding <=> '{$queryVector}'
                    LIMIT {$remainingNum};
                ");
                // 合并结果
                $list = array_merge($qaSlices, $regularSlices);
            } else {
                $list = $qaSlices;
            }
        }

        return $this->processResults($list);
    }
    
    /**
     * 根据标题查询并返回该文档下的所有切片内容
     */
    public function getDocumentSlicesByTitle($text, $engine, $limit = 2)
    {
        try {
            // 获取 PGSQL 连接
            $pg = $engine->getPgConnect();

            // 调用 Embeddings 接口生成标题向量
            $text = mb_strcut($text, 0, 384);
            $vectorArr = $this->getEmbeddingsNew([$text]);
            usleep(500000);
            $this->vectorCacheList[md5($text)] = $vectorArr;

            $vectorData = json_encode(['content'=>$vectorArr[0]['embedding']]);
            
            $vectorList = json_decode($vectorData, true);

            if(empty($vectorList['content']))
            {
                throw new \Exception("向量生成失败或格式错误: " . $vectorData);
            }

            $queryVector = json_encode($vectorList['content']);

            // 构建 SQL 查询：找到最相似的标题
            $sql = <<<SQL
                SELECT 
                    dt.doc_id,
                    dt.title,
                    dt.title content,
                    dt.knowledge_base_id,
                    1 - (dt.title_embedding <=> '{$queryVector}') AS title_similarity
                FROM public.document_titles dt
                ORDER BY dt.title_embedding <=> '{$queryVector}'
                LIMIT 10
            SQL;

            $titleResults = $pg->query($sql);
            $titleResults = $this->reorderReferences($titleResults);
    
            $titleResults = array_slice($titleResults, 0, $limit);

            if (empty($titleResults)) {
                return [];
            }

            // 收集 doc_id 列表
            $docIds = array_column($titleResults, 'doc_id');

            // 查询每个匹配标题对应的全部切片内容
            $slicesSql = <<<SQL
                SELECT 
                    id,
                    file_id,
                    content,
                    word_count
                FROM public.vector_data_upgrades
                WHERE file_id = ANY(?) AND delete_time IS NULL
                ORDER BY file_id, id
                LIMIT 50
            SQL;

            $sliceResults = $pg->query($slicesSql, ['{' . implode(',', $docIds) . '}']);

            // 按照 doc_id 分组返回
            $result = [];

            foreach($sliceResults as $sliceRow){
                $result[] = [
                    'id' => $sliceRow['id'],
                    'file_id' => $sliceRow['file_id'],
                    'content' => $sliceRow['content'],
                    'cosine_similarity' => 0,
                ];
            }

            return $result;

        } catch (\Exception $e) {
            error_log(date("Y-m-d H:i:s") . "|getTitleSlices:" . print_r($e->getMessage(), true) . "\r\n", 3, ROOT_PATH . "/runtime/log/" . date("Ym") . "/localprofessional.log");
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * 获取相邻切片以恢复完整内容 - 专业版本（强制开启）
     */
    protected function retrieveAdjacentSlices($slices, $query, $project)
    {
        $enhancedSlices = [];
        $processedIds = [];
        
        foreach ($slices as $slice) {
            // 如果已处理过该切片，跳过
            if (in_array($slice['id'], $processedIds)) {
                continue;
            }
            
            $processedIds[] = $slice['id'];
            
            // 专业版本强制开启相邻切片获取
            $isIncomplete = true;
            
            // 如果内容看起来不完整或包含步骤关键词，尝试获取相邻切片
            if ($isIncomplete || $this->containsStepKeywords($slice['content'])) {
                // 直接查询相邻ID的切片（前后各2个）
                $adjacentSlices = $query->query("
                    SELECT id, content, file_id
                    FROM vector_data_upgrades
                    WHERE file_id = {$slice['file_id']}
                    AND console_id IN ({$project['console_ids']})
                    AND delete_time IS NULL
                    AND id BETWEEN {$slice['id']} - 1 AND {$slice['id']} + 1
                    ORDER BY id ASC
                ");
                
                // 如果找不到相邻切片，使用原始切片
                if (empty($adjacentSlices)) {
                    $enhancedSlices[] = $slice;
                    continue;
                }
                
                // 合并相邻切片内容
                $combinedContent = '';
                foreach ($adjacentSlices as $adjacentSlice) {
                    $combinedContent .= $adjacentSlice['content'] . "\n";
                    // 将处理过的切片ID添加到已处理列表
                    $processedIds[] = $adjacentSlice['id'];
                }
                
                // 创建新的合并切片
                $newSlice = $slice;
                $newSlice['content'] = $this->engine->cleanupCombinedContent($combinedContent);
                $newSlice['is_combined'] = true;
                
                $enhancedSlices[] = $newSlice;
            } else {
                $enhancedSlices[] = $slice;
            }
        }
        
        return $enhancedSlices;
    }
    
    /**
     * 处理查询结果 - 专业版本（不增强QA内容）
     */
    protected function processResults($results)
    {
        foreach ($results as &$result) {
            // 专业版本不增强QA内容，直接清理字段
            unset($result['extracted_question'], $result['extracted_answer']);
        }
        return $results;
    }
    
    /**
     * 术语库
     */
    private function loadTermMap($robot){
        $termMap = \app\admin\model\ai\Term::where('robot', $robot)->column("chinese_term,english_term");
        return $termMap;
    }
}
