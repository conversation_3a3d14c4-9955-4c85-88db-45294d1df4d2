<?php

namespace app\common\service;
use Smalot\PdfParser\Parser;
use PhpOffice\PhpWord\IOFactory;


class DocParsingService
{
	

	/*
	 *解析文件内容*
	*/
	public static function parsing(string $filePath): string
	{

		if (!is_file($filePath)) {
			return '';
		}

		$content = '';

		$ext = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

		switch ($ext) {
			case 'txt':
				$content = file_get_contents($filePath);
				break;

			case 'pdf':
				$content = self::getPdfContent($filePath);
				break;

			case 'doc':
				$content = self::getDocContent($filePath);
				break;
			case 'docx':
				$content = self::getDocxContent($filePath);
				break;

		}

		
		
		return $content;
	}	


	protected static function getPdfContent($filePath)
	{
		$parser = new Parser();
        $pdf = $parser->parseFile($filePath);
        $text = $pdf->getText();
        // //转换utf-8编码
        return mb_convert_encoding($text,'utf-8');
	}


	protected static function getDocContent($filePath)
	{
		// 执行 catdoc 命令
		$content = shell_exec("catdoc -w -d utf-8 " . $filePath);
        return $content;
	  
	}


	protected static function getDocxContent($filePath)
	{

		$word = IOFactory::load($filePath);
		$return_text = '';
        // $return_html = [];
        //分解部分
        foreach ($word->getSections() as $section) {

            if ($section instanceof \PhpOffice\PhpWord\Element\Section) {
                //分解元素
                foreach ($section->getElements() as $element) {
                    //文本元素
                    if ($element instanceof \PhpOffice\PhpWord\Element\TextRun) {
                        $item_text = '';
                        // $item_html = '<p>';
                        foreach ($element->getElements() as $ele) {
                            $arr = self::getTextNode($ele);
                            $item_text .= $arr['text'];
                            // $item_html .= $arr['html'];
                        }
                        // $item_html .= '</p>';
                        $return_text .= $item_text;
                        // $return_html[] = $item_html;
                    } //表格元素
                    else if ($element instanceof \PhpOffice\PhpWord\Element\Table) {
                        $item_text = '表格';
                        // $item_html = '<table width="100%" border="1" cellspacing="0" cellpadding="5">';
                        // $item_htmlaaaa = array();
                        foreach ($element->getRows() as $ele) {
                            $arr = self::getTableNode($ele);
                            $item_text .= $arr['text'] ? implode(',', $arr['text']) : '';
//                            $item_html .= $arr['html'] ? implode(',', $arr['html']) : '';
                            // $item_htmlaaaa[] = $arr['html'];
                        }
                        // if (count($item_htmlaaaa) > 0) {
                        //     foreach ($item_htmlaaaa as $rstr) {
                        //         $item_html .= '<tr>';
                        //         foreach ($rstr as $rstd) {
                        //             $item_html .= '<td>' . $rstd . '</td>';
                        //         }
                        //         $item_html .= '</tr>';
                        //     }
                        // }

                        // $item_html .= '</table>';
                        $return_text .= $item_text;
                        // $return_html[] = $item_html;
                    } //处理链接
                    else if ($element instanceof \PhpOffice\PhpWord\Element\Link) {
                        $item_text = $element->getText();
                        // $item_html = '<a href="' . $element->getSource() . '">' . $item_text . '</a>';
                        $return_text .= $item_text;
                        // $return_html[] = $item_html;
                    } //保留文本元素
                    else if ($element instanceof \PhpOffice\PhpWord\Element\PreserveText) {
                        //当是预留文本的时候
                        $item_text = '';
                        // $item_html = '<p>';
                        foreach ($element->getText() as $ele => $value) {
                            $item_text .= $value;
                            // $item_html .= $value;
                        }
                        // $item_html .= '</p>';
                        $return_text .= $item_text;
                        // $return_html[] = $item_html;
                    }
                }
            }
        }
        // return array('text' => $return_text, 'html' => $return_html);
        return $return_text;
	}

	/**
     * 获取文档节点内容
     * @param $node
     * @return array
     */
    public static function getTextNode($node)
    {
        $return_text = '';
        $return_html = '';
        //处理文本
        if ($node instanceof \PhpOffice\PhpWord\Element\Text) {
            $style = $node->getFontStyle();
            $fontFamily = $style->getName();
            $fontSize = $style->getSize();
            $fontColor = $style->getColor();
            $isBold = $style->isBold();
            $isItalic = $style->isItalic();                 //斜体
            $isStrikethrough = $style->isStrikethrough();   //删除线
            $isUnderline = $style->getUnderline();
            $styleString = '';
            $fontFamily && $styleString .= "font-family:{$fontFamily};";
            $fontSize && $styleString .= "font-size:{$fontSize}px;";
            $fontColor && $styleString .= "color:{$fontColor};";
            $isBold && $styleString .= "font-weight:bold;";
            $isItalic && $styleString .= "font-style:italic;";
            if ($isStrikethrough) {
                $styleString .= "text-decoration:line-through;";
            } else if ($isUnderline != 'none') {
                $styleString .= "text-decoration:underline;";
            }
            //echo '<pre>';
            //print_r($styleString);
            //exit;
            // $html = $styleString ? "<span style='{$styleString}'>{$node->getText()}</span>" : $node->getText();
            // if ($isUnderline == 'single') {
            //     $html = "<u>{$html}</u>";
            // }
            $return_text .= $node->getText();
            // $return_html .= $html;
        } //处理图片
        else if ($node instanceof \PhpOffice\PhpWord\Element\Image) {
            // $return_text .= self::pic2file($node);
            // $return_html .= self::pic2file($node);
        } //处理文本元素
        else if ($node instanceof \PhpOffice\PhpWord\Element\TextRun) {
            foreach ($node->getElements() as $ele) {
                $arr = self::getTextNode($ele);
                $return_text .= $arr['text'];
                // $return_html .= $arr['html'];
            }
        } //处理保留文本
        else if ($node instanceof \PhpOffice\PhpWord\Element\PreserveText) {
            $data = $node->getText();
            $find = array('{', 'HYPERLINK', '}', ' ', '"', 'f', 'g');
            $replace = '';
            $resText = str_replace($find, $replace, $data);
            $return_text .= $resText[0];
            // $return_html .= $resText[0];
        }
        //echo '<pre>';
        //print_r(array('text' => $return_text, 'html' => $return_html));
        //exit;
        return array('text' => $return_text, 'html' => $return_html);
    }

    /**
     * 获取表格节点内容
     * @param $node
     * @return array
     */
    public static function getTableNode($node)
    {
        $return_arr = [];
        $return_text = [];
        $return_html = [];
        //处理行
        if ($node instanceof \PhpOffice\PhpWord\Element\Row) {
            foreach ($node->getCells() as $ele) {
                //$return_arr[] = self::getTableNode($ele);
                $arr = self::getTableNode($ele);
                $return_text[] = $arr['text'];
                // $return_html[] = $arr['html'];
            }
        } //处理列
        else if ($node instanceof \PhpOffice\PhpWord\Element\Cell) {
            foreach ($node->getElements() as $ele) {
                $return_arr = self::getTextNode($ele);
                $return_text = $return_arr['text'];
                // $return_html = $return_arr['html'];
            }
        }
        //return $return_arr;
        return array('text' => $return_text, 'html' => $return_html);
    }




    /*
    * 根据文件目录将文件切分
    */
    public static function sliceDocBycatalog($content, $catalog)
    {   

        $result = [];
        //大模型会简单处理文本，省略掉一些不必要的字符例如
        //数字、特殊字符两边的空格会省略
        //环视断言正则引擎会不停的回溯检查
        // $content = preg_replace('/(?<=\d)\s+|\s+(?=\d)/', '', $content);
        //使用直接匹配数字空格的组合,替换为不带空格的数字
        $content = preg_replace('/(\d)\s+(\d)|\s+(\d)\s+|\s+(\d)|(\d)\s+|(—)\s+/', '$1$2$3$4$5$6', $content);

        //两个空格合并成一个
        $content = str_replace(["  "],[' '],$content);
        dump($catalog);
        // dump($content);
        foreach ($catalog as $k => $v) {
            $arr = explode($v,$content);
            // dump($v);
            // dump($arr);

            //如果数组元素大于2个，有可能文件中有目录信息，选择第3个元素
            // if ($k == 0 && count($arr) > 2) {
            //     $content = $arr[2];
            // } else {
            //     $content = $arr[1];
            // }

            //舍弃掉第一段，遍历结束后，添加上最后一段
            // if ($k > 0) {
            //     $result[] = $arr[0];                
            // }

        }

        dump($content);
        die;
        $result[] = $content;


        // dump($catalog);
        // dump($result);die;
    }




}