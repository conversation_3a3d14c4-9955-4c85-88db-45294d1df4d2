<?php

namespace app\common\service\ai\engine;

/**
 * 本地引擎工厂类
 * 用于创建不同策略的本地引擎实例
 */
class LocalEngineFactory
{
    /**
     * 创建本地引擎实例
     * @param string $type 引擎类型：basic, enhance, professional
     * @return LocalEngine
     */
    public static function create($type = 'basic')
    {
        switch (strtolower($type)) {
            case 'enhance':
                return new LocalEngine(LocalEngine::STRATEGY_ENHANCE);
            case 'professional':
                return new LocalEngine(LocalEngine::STRATEGY_PROFESSIONAL);
            default:
                return new LocalEngine(LocalEngine::STRATEGY_BASIC);
        }
    }
    
    /**
     * 根据项目配置自动选择引擎类型
     * @param array $project 项目配置
     * @return LocalEngine
     */
    public static function createByProject($project)
    {
        // 根据项目配置自动判断使用哪种策略
        if (!empty($project['query_augmentation']) && $project['query_augmentation'] == 1) {
            return self::create('professional');
        } elseif (!empty($project['enable_enhancement']) && $project['enable_enhancement'] == 1) {
            return self::create('enhance');
        } else {
            return self::create('basic');
        }
    }
    
    /**
     * 获取所有可用的引擎类型
     * @return array
     */
    public static function getAvailableTypes()
    {
        return [
            'basic' => '基础引擎',
            'enhance' => '增强引擎',
            'professional' => '专业引擎'
        ];
    }
}
