<?php

namespace app\common\library\aliyun;

use think\Exception;
use GuzzleHttp\Client;

class VisionModel
{
    protected $baseUrl = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions';
    protected $apiKey;
    protected $model = 'qwen-vl-plus';

    public function __construct($apiKey = null)
    {
        $this->apiKey = $apiKey ?: 'sk-613ccddd0b0948179c815a0e11cd8ebf';
    }

    /**
     * 图片理解分析
     *
     * @param string $imageUrl 图片URL
     * @param string $text 问题文本
     * @param string $model 模型名称
     * @return array|false
     * @throws Exception
     */
    public function analyze($imageUrl, $text = '这是什么', $model = null)
    {
        $client = new Client();
        
        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json'
        ];

        $data = [
            'model' => $model ?: $this->model,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => $imageUrl
                            ]
                        ],
                        [
                            'type' => 'text',
                            'text' => $text
                        ]
                    ]
                ]
            ]
        ];

        try {
            $response = $client->post($this->baseUrl, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json'
                ],
                'json' => $data,
                'timeout' => 30
            ]);

            $result = json_decode($response->getBody()->getContents(), true);
            
            if (isset($result['choices'][0]['message']['content'])) {
                return [
                    'success' => true,
                    'content' => $result['choices'][0]['message']['content'],
                    'usage' => $result['usage'] ?? null
                ];
            }
            
            return [
                'success' => false,
                'error' => '响应格式错误',
                'raw' => $result
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 批量分析多张图片
     *
     * @param array $imageUrls 图片URL数组
     * @param string $text 问题文本
     * @return array
     */
    public function batchAnalyze($imageUrls, $text = '这是什么')
    {
        $results = [];
        foreach ($imageUrls as $imageUrl) {
            $results[] = $this->analyze($imageUrl, $text);
        }
        return $results;
    }

    /**
     * 设置API密钥
     */
    public function setApiKey($apiKey)
    {
        $this->apiKey = $apiKey;
        return $this;
    }

    /**
     * 设置模型
     */
    public function setModel($model)
    {
        $this->model = $model;
        return $this;
    }
}