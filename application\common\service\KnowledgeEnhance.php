<?php
namespace app\common\service;

use think\Db;
use think\Exception;

/**
 * 知识增强服务类
 * 负责处理文档切片的知识增强，包括切片处理和问答对生成
 */
class KnowledgeEnhance
{
    // 数据库表名
    protected $pgTable = 'public.slice_qa';

    // Python路径
    protected $pythonPath = '/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9';

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 初始化
    }

    /**
     * 连接PostgreSQL数据库
     */
    protected function pg_connect()
    {
		$request = Db::connect(
			[
				'type' => 'pgsql',
				'hostname' => '127.0.0.1',
				'database' => config('postgres.database'),
				'username' => 'postgres',
				'password' => 'DRrTmhCKrLWs2b34',
				'hostport' => '5432'
			]
		);
		return $request;
    }

    /**
     * 处理文件的知识增强
     *
     * @param int $file_id 文件ID
     * @return bool 是否成功
     */
    public function enhanceFile($file_id)
    {
        try {
            $query = $this->pg_connect();

            // 检查文件是否已经被增强
            $existingData = $query->table($this->pgTable)
                                ->where(['file_id' => $file_id])
                                ->find();
            if (!empty($existingData)) {
                error_log(date("Y-m-d H:i:s")."|文件ID: {$file_id} 已经完成增强处理\r\n", 3,
                         ROOT_PATH."/runtime/log/".date("Ym")."/buildKnowledgeEnhance.log");
                \app\admin\model\ai\ConsoleFile::where('id', $file_id)->update(['enhance_status' => 1]);
                return true;
            }

            // 获取文件的所有切片 - 从原始切片表获取
            $fileRows = $query->table('public.vector_data_upgrades')->where(['file_id' => $file_id])->select();
            if (empty($fileRows)) {
                error_log(date("Y-m-d H:i:s")."|文件ID: {$file_id} 没有找到原始切片\r\n", 3,
                         ROOT_PATH."/runtime/log/".date("Ym")."/buildKnowledgeEnhance.log");
                return false;
            }

            $totalInserted = 0;
            $batchSize = 50;
            $insertData = [];

            foreach ($fileRows as $value) {
                try {
                    // 检查切片是否包含content字段
                    if (!isset($value['content']) || empty($value['content'])) {
                        error_log(date("Y-m-d H:i:s")."|文件ID: {$file_id}, 切片ID: {$value['id']} 处理失败: 切片缺少content字段或内容为空\r\n", 3,
                                 ROOT_PATH."/runtime/log/".date("Ym")."/buildKnowledgeEnhance.log");
                        continue;
                    }
                    
                    // 检查该切片是否已经存在记录，如果存在则跳过
                    $existingSlice = $query->table($this->pgTable)
                                    ->where(['slice_id' => $value['id']])
                                    ->find();
                    if (!empty($existingSlice)) {
                        error_log(date("Y-m-d H:i:s")."|文件ID: {$file_id}, 切片ID: {$value['id']} 已存在记录，跳过处理\r\n", 3,
                                 ROOT_PATH."/runtime/log/".date("Ym")."/buildKnowledgeEnhance.log");
                        continue;
                    }

                    // 1. 获取切片内容和向量
                    $sliceContent = $this->sliceContent($value['content']);

                    // 2. 获取问答对内容和向量
                    $qaContent = $this->qaPairMining($value['content']);

                    // 3. 处理切片数据
                    foreach ($sliceContent as $slice) {
                        if (empty($slice['content']) || empty($slice['vector'])) {
                            continue;
                        }

                        $insertData[] = [
                            'slice_id' => $value['id'],
                            'extracted_question' => $slice['content'],
                            'extracted_answer' => '',  // 切片类型无答案
                            'embedding' => $slice['vector'],
                            'knowledge_base_id' => isset($value['console_id']) ? $value['console_id'] : 0,
                            'file_id' => $file_id,
                            'type' => 1  // 1表示切片类型
                        ];

                        // 当达到批处理大小时执行插入
                        if (count($insertData) >= $batchSize) {
                            $insertResult = $this->batchInsertSliceQA($insertData, $query);
                            if ($insertResult) {
                                $totalInserted += count($insertData);
                            }
                            $insertData = [];
                        }
                    }

                    // 4. 处理问答对数据
                    foreach ($qaContent as $qa) {
                        if (empty($qa['question']) || empty($qa['answer']) || empty($qa['vector'])) {
                            continue;
                        }

                        $insertData[] = [
                            'slice_id' => $value['id'],
                            'extracted_question' => $qa['question'],
                            'extracted_answer' => $qa['answer'],
                            'embedding' => $qa['vector'],
                            'knowledge_base_id' => isset($value['console_id']) ? $value['console_id'] : 0,
                            'file_id' => $file_id,
                            'type' => 2  // 2表示问答对类型
                        ];

                        // 当达到批处理大小时执行插入
                        if (count($insertData) >= $batchSize) {
                            $insertResult = $this->batchInsertSliceQA($insertData, $query);
                            if ($insertResult) {
                                $totalInserted += count($insertData);
                            }
                            $insertData = [];
                        }
                    }
                } catch (\Exception $e) {
                    // 记录单个切片处理失败，但继续处理其他切片
                    error_log(date("Y-m-d H:i:s")."|文件ID: {$file_id}, 切片ID: {$value['id']} 处理失败: ".$e->getMessage()."\r\n", 3,
                             ROOT_PATH."/runtime/log/".date("Ym")."/buildKnowledgeEnhance.log");
                    continue;
                }
            }

            // 处理剩余的数据
            if (!empty($insertData)) {
                $insertResult = $this->batchInsertSliceQA($insertData, $query);
                if ($insertResult) {
                    $totalInserted += count($insertData);
                }
            }

            // 记录处理结果
            error_log(date("Y-m-d H:i:s")."|文件ID: {$file_id} 增强处理完成，共插入 {$totalInserted} 条记录\r\n", 3,
                     ROOT_PATH."/runtime/log/".date("Ym")."/buildKnowledgeEnhance.log");

            // 更新文件状态
            \app\admin\model\ai\ConsoleFile::where('id', $file_id)->update(['enhance_status' => 1]);
            return true;

        } catch (\Exception $e) {
            error_log(date("Y-m-d H:i:s")."|文件ID: {$file_id} 处理失败: ".$e->getMessage()."\r\n", 3,
                     ROOT_PATH."/runtime/log/".date("Ym")."/buildKnowledgeEnhance.log");
            return false;
        }
    }

    /**
     * 检查数据库连接是否有效
     */
    protected function isConnectionValid($query)
    {
        try {
            $query->query('SELECT 1');
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    

    /**
     * 批量插入数据
     *
     * @param array $data 要插入的数据
     * @param object $query 数据库连接对象
     *
     * @return bool 是否成功
     */
    protected function batchInsertSliceQA($data, $query)
    {

        if (!$this->isConnectionValid($query)) {
            $query->close();
            $query = $this->pg_connect();
        }
        if (empty($data)) {
            return false;
        }

        $columns = ['slice_id', 'extracted_question', 'extracted_answer',
                  'embedding', 'knowledge_base_id', 'file_id', 'type'];

        $values = [];
        foreach ($data as $row) {
            $values[] = "(" . implode(",", [
                "'" . $row['slice_id'] . "'",
                "'" . pg_escape_string($row['extracted_question']) . "'",
                "'" . pg_escape_string($row['extracted_answer']) . "'",
                "'" . $row['embedding'] . "'",
                $row['knowledge_base_id'],
                $row['file_id'],
                $row['type']
            ]) . ")";
        }

        $sql = "INSERT INTO {$this->pgTable} (" . implode(",", $columns) . ") VALUES " .
               implode(",", $values);

        try {
            $result = $query->execute($sql);
            return $result !== false;
        } catch (\Exception $e) {
            error_log(date("Y-m-d H:i:s")."|批量插入失败: ".$e->getMessage()."\r\n", 3,
                     ROOT_PATH."/runtime/log/".date("Ym")."/buildKnowledgeEnhance.log");
            return false;
        }
    }

    /**
     * 优化的原文切分方法，添加重试机制和错误处理
     */
    protected function sliceContent($content) {
        // 定义分隔符数组
        $arr = ["。", "！", "？", ".", "!", "?", "……", "|\n"];

        // 将所有分隔符统一替换为统一标记
        $unified_content = str_replace($arr, "<SPLIT>", $content);

        // 按统一标记分割文本
        $segments = array_filter(explode("<SPLIT>", $unified_content), function($segment) {
            return trim($segment) !== '';
        });

        // 检查每个片段，如果超过384字符则重新拆分
        $newSegments = [];
        foreach ($segments as $segment) {
            $segment = trim($segment);
            if (mb_strlen($segment) > 384) {
                // 对超长片段进行重新拆分
                $length = mb_strlen($segment);
                for ($i = 0; $i < $length; $i += 384) {
                    $subSegment = mb_substr($segment, $i, 384);
                    if (!empty(trim($subSegment))) {
                        $newSegments[] = $subSegment;
                    }
                }
            } else {
                if (!empty(trim($segment))) {
                    $newSegments[] = $segment;
                }
            }
        }

        // 如果没有分隔符，或分割后只有一段，则按384字符进行切分
        if (empty($newSegments)) {
            $content = trim($content);
            $length = mb_strlen($content);
            for ($i = 0; $i < $length; $i += 384) {
                $segment = mb_substr($content, $i, 384);
                if (!empty(trim($segment))) {
                    $newSegments[] = $segment;
                }
            }
        }

        $result = [];

        // 减小批次大小，避免API限流
        $batchSize = 5;

        // 分批处理片段
        $batches = array_chunk($newSegments, $batchSize);

        foreach ($batches as $batch) {
            $validSegments = [];

            // 过滤掉太短的片段
            foreach ($batch as $segment) {
                $segment = trim($segment);
                if (mb_strlen($segment) >= 30) {
                    $validSegments[] = $segment;
                }
            }

            if (empty($validSegments)) {
                continue;
            }

            // 添加重试机制
            $maxRetries = 3;
            $retryCount = 0;
            $success = false;
            $vectorArr = [];

            while (!$success && $retryCount < $maxRetries) {
                try {
                    // 批量获取向量
                    $vectorArr = $this->getEmbeddingsWithRetry($validSegments);

                    // 检查返回结果是否有效
                    if (!empty($vectorArr)) {
                        $success = true;
                    } else {
                        $retryCount++;
                        // 指数退避策略，每次重试等待时间增加
                        $sleepTime = pow(2, $retryCount) * 100000; // 0.5秒, 1秒, 2秒
                        usleep($sleepTime);
                    }
                } catch (\Exception $e) {
                    error_log(date("Y-m-d H:i:s")."|向量化失败(重试 {$retryCount}): ".$e->getMessage()."\r\n", 3,
                             ROOT_PATH."/runtime/log/".date("Ym")."/vectorization.log");
                    $retryCount++;
                    // 指数退避策略
                    $sleepTime = pow(2, $retryCount) * 100000;
                    usleep($sleepTime);
                }
            }

            // 处理向量结果
            if ($success) {
                foreach ($validSegments as $key => $text) {
                    if (isset($vectorArr[$key]) && !empty($vectorArr[$key]['embedding'])) {
                        $result[] = [
                            'content' => $text,
                            'vector' => json_encode($vectorArr[$key]['embedding'])
                        ];
                    }
                }
            } else {
                // 记录最终失败
                error_log(date("Y-m-d H:i:s")."|向量化最终失败，跳过当前批次\r\n", 3,
                         ROOT_PATH."/runtime/log/".date("Ym")."/vectorization.log");
            }

            // 批次间增加更长的等待时间
            usleep(100000); // 0.1秒
        }

        return $result;
    }

    /**
     * 问答对生成方法，添加文本长度限制和分段处理
     */
    protected function qaPairMining($content) {
        // 添加重试机制
        $maxRetries = 3;
        $retryCount = 0;
        $return = "";
        
        while ($retryCount < $maxRetries) {
            try {
                // 调用Python脚本获取问答对
                $path = ' /mnt/sdc/wwwroot/ai-master/python/appbuilder/qa_pair_mining.py "%s" ';
                $c = $this->pythonPath . ' ' . $path;
                
                // 设置执行超时时间为30秒
                $descriptorspec = [
                    0 => ["pipe", "r"],  // 标准输入
                    1 => ["pipe", "w"],  // 标准输出
                    2 => ["pipe", "w"]   // 标准错误输出
                ];
                
                $process = proc_open(sprintf($c, $content), $descriptorspec, $pipes, null, null);
                
                if (is_resource($process)) {
                    // 从标准输出读取数据
                    $output = stream_get_contents($pipes[1]);
                    // 从标准错误读取数据
                    $error = stream_get_contents($pipes[2]);
                    
                    // 关闭管道
                    fclose($pipes[0]);
                    fclose($pipes[1]);
                    fclose($pipes[2]);
                    
                    // 获取进程退出状态
                    $status = proc_close($process);
                    
                    if ($status === 0 && !empty($output)) {
                        $return = $output;
                        break; // 成功获取输出，跳出循环
                    } else {
                        // 记录错误
                        error_log(date("Y-m-d H:i:s")."|问答对生成失败(重试 {$retryCount}): 状态码 {$status}, 错误: {$error}\r\n", 3,
                                 ROOT_PATH."/runtime/log/".date("Ym")."/qaPairMining.log");
                    }
                }
                
                $retryCount++;
                if ($retryCount < $maxRetries) {
                    // 指数退避策略
                    $sleepTime = pow(2, $retryCount) * 100000;
                    usleep($sleepTime);
                }
            } catch (\Exception $e) {
                error_log(date("Y-m-d H:i:s")."|问答对生成异常(重试 {$retryCount}): ".$e->getMessage()."\r\n", 3,
                         ROOT_PATH."/runtime/log/".date("Ym")."/qaPairMining.log");
                $retryCount++;
                if ($retryCount < $maxRetries) {
                    $sleepTime = pow(2, $retryCount) * 100000;
                    usleep($sleepTime);
                }
            }
        }
        
        // 如果所有重试都失败，返回空数组
        if (empty($return)) {
            error_log(date("Y-m-d H:i:s")."|问答对生成最终失败，返回空结果\r\n", 3,
                     ROOT_PATH."/runtime/log/".date("Ym")."/qaPairMining.log");
            return [];
        }
        
        $qaPairs = explode("问题：", $return);
        
        $result = [];
        $batchSegments = [];
        $batchCount = 0;
        $qaMapping = [];

        // 处理每个问答对
        foreach ($qaPairs as $pair) {
            if (empty(trim($pair))) continue;

            // 分离问题和答案
            $parts = explode("答案：", $pair);
            if (count($parts) != 2) continue;

            $question = trim($parts[0]);
            $answer = trim($parts[1]);

            // 限制问题和答案的长度，防止token过长
            if (mb_strlen($question) > 200) {
                $question = mb_substr($question, 0, 200);
            }

            if (mb_strlen($answer) > 300) {
                $answer = mb_substr($answer, 0, 300);
            }

            // 组合问答对
            $combinedQA = "问题：" . $question . "\n答案：" . $answer;

            // 检查组合后的文本长度
            if (mb_strlen($combinedQA) > 500) {
                // 如果太长，跳过这个问答对
                error_log(date("Y-m-d H:i:s")."|跳过过长的问答对: ".mb_substr($combinedQA, 0, 50)."...\r\n", 3,
                         ROOT_PATH."/runtime/log/".date("Ym")."/qaPairMining.log");
                continue;
            }

            // 收集组合后的问答对用于批量向量化
            $batchSegments[] = $combinedQA;
            $qaMapping[] = [
                'question' => $question,
                'answer' => $answer
            ];
            $batchCount++;

            // 当达到5条或是最后一批时进行处理
            if ($batchCount >= 5) {
                // 处理当前批次
                $this->processBatch($batchSegments, $qaMapping, $result);

                // 重置批次
                $batchSegments = [];
                $qaMapping = [];
                $batchCount = 0;

                // 避免接口调用过于频繁
                usleep(200000); // 0.5秒
            }
        }

        // 处理剩余的问答对
        if (!empty($batchSegments)) {
            $this->processBatch($batchSegments, $qaMapping, $result);
        }

        return $result;
    }

    /**
     * 处理一批问答对
     *
     * @param array $batchSegments 问答对文本
     * @param array $qaMapping 问答对映射
     * @param array &$result 结果数组（引用传递）
     */
    protected function processBatch($batchSegments, $qaMapping, &$result) {
        // 添加重试机制
        $maxRetries = 3;
        $retryCount = 0;
        $success = false;

        while (!$success && $retryCount < $maxRetries) {
            try {
                // 批量获取向量
                $vectorArr = $this->getEmbeddingsNew($batchSegments);

                // 检查返回结果是否有效
                if (!empty($vectorArr)) {
                    // 处理向量结果
                    foreach ($qaMapping as $i => $qa) {
                        if (isset($vectorArr[$i]) && !empty($vectorArr[$i]['embedding'])) {
                            $result[] = [
                                'question' => $qa['question'],
                                'answer' => $qa['answer'],
                                'vector' => json_encode($vectorArr[$i]['embedding'])
                            ];
                        }
                    }
                    $success = true;
                } else {
                    $retryCount++;
                    // 指数退避策略
                    $sleepTime = pow(2, $retryCount) * 100000;
                    usleep($sleepTime);
                    error_log(date("Y-m-d H:i:s")."|向量化失败，重试 {$retryCount}/3\r\n", 3,
                             ROOT_PATH."/runtime/log/".date("Ym")."/qaPairMining.log");
                }
            } catch (\Exception $e) {
                $retryCount++;
                $sleepTime = pow(2, $retryCount) * 100000;
                usleep($sleepTime);
                error_log(date("Y-m-d H:i:s")."|向量化异常: ".$e->getMessage()."，重试 {$retryCount}/3\r\n", 3,
                         ROOT_PATH."/runtime/log/".date("Ym")."/qaPairMining.log");
            }
        }

        if (!$success) {
            error_log(date("Y-m-d H:i:s")."|向量化最终失败，跳过当前批次\r\n", 3,
                     ROOT_PATH."/runtime/log/".date("Ym")."/qaPairMining.log");
        }
    }

    /**
     * 带重试机制的向量获取方法
     *
     * @param array $segments 文本片段数组
     * @return array 向量结果
     */
    protected function getEmbeddingsWithRetry($segments) {
        $maxRetries = 3;
        $retryCount = 0;
        $result = [];

        while ($retryCount < $maxRetries) {
            try {
                $result = $this->getEmbeddingsNew($segments);
                if (!empty($result)) {
                    return $result;
                }

                $retryCount++;
                $sleepTime = pow(2, $retryCount) * 100000; // 0.5秒, 1秒, 2秒
                usleep($sleepTime);

            } catch (\Exception $e) {
                error_log(date("Y-m-d H:i:s")."|向量化异常(重试 {$retryCount}): ".$e->getMessage()."\r\n", 3,
                         ROOT_PATH."/runtime/log/".date("Ym")."/vectorization.log");
                $retryCount++;
                $sleepTime = pow(2, $retryCount) * 100000;
                usleep($sleepTime);
            }
        }

        return $result;
    }

    /**
     * 安全的向量获取方法，处理可能的错误
     */
    protected function getEmbeddingsNew($arr) {
        if (empty($arr)) {
            return [];
        }

        try {
            // 检查每个文本的长度，如果总长度过大则记录警告
            $totalLength = 0;
            foreach ($arr as $text) {
                $totalLength += mb_strlen($text);
            }

            if ($totalLength > 4000) {
                error_log(date("Y-m-d H:i:s")."|警告：向量化文本总长度为 {$totalLength} 字符，可能超出API限制\r\n", 3,
                         ROOT_PATH."/runtime/log/".date("Ym")."/vectorization.log");

                // 如果总长度过大，尝试减少批次大小
                if (count($arr) > 1) {
                    $halfSize = ceil(count($arr) / 2);
                    $firstHalf = array_slice($arr, 0, $halfSize);
                    $secondHalf = array_slice($arr, $halfSize);

                    // 递归处理两半
                    $result1 = $this->getEmbeddingsNew($firstHalf);
                    usleep(100000); // 等待0.1秒避免API限流
                    $result2 = $this->getEmbeddingsNew($secondHalf);

                    // 合并结果
                    return array_merge($result1, $result2);
                }

                // 如果只有一个元素但仍然太长，则截断
                if (count($arr) == 1 && mb_strlen($arr[0]) > 2000) {
                    $arr[0] = mb_substr($arr[0], 0, 2000);
                    error_log(date("Y-m-d H:i:s")."|文本过长，已截断至2000字符\r\n", 3,
                             ROOT_PATH."/runtime/log/".date("Ym")."/vectorization.log");
                }
            }

            $input = json_encode(['input' => $arr]);

            $embeddings = new \app\common\library\Embeddings;
            $embeddingsStr = $embeddings->run($input);
            $embeddingsArr = json_decode($embeddingsStr, true);

            // 检查是否有错误响应
            if (isset($embeddingsArr['error_code'])) {
                error_log(date("Y-m-d H:i:s")."|向量化API错误: 代码 {$embeddingsArr['error_code']}, 消息: {$embeddingsArr['error_msg']}\r\n", 3,
                         ROOT_PATH."/runtime/log/".date("Ym")."/vectorization.log");

                // 如果是token过长错误，记录详细信息以便调试
                if ($embeddingsArr['error_code'] == 336003) {
                    error_log(date("Y-m-d H:i:s")."|Token过长错误，输入内容: ".substr(json_encode($arr), 0, 500)."...\r\n", 3,
                             ROOT_PATH."/runtime/log/".date("Ym")."/vectorization.log");

                    // 如果是token过长错误且有多个元素，尝试减少批次大小
                    if (count($arr) > 1) {
                        $halfSize = ceil(count($arr) / 2);
                        $firstHalf = array_slice($arr, 0, $halfSize);
                        $secondHalf = array_slice($arr, $halfSize);

                        // 递归处理两半
                        $result1 = $this->getEmbeddingsNew($firstHalf);
                        usleep(100000); // 等待0.1秒避免API限流
                        $result2 = $this->getEmbeddingsNew($secondHalf);

                        // 合并结果
                        return array_merge($result1, $result2);
                    }

                    // 如果只有一个元素但仍然太长，则截断
                    if (count($arr) == 1) {
                        $arr[0] = mb_substr($arr[0], 0, mb_strlen($arr[0]) / 2);
                        error_log(date("Y-m-d H:i:s")."|文本过长，已截断至一半长度\r\n", 3,
                                 ROOT_PATH."/runtime/log/".date("Ym")."/vectorization.log");
                        return $this->getEmbeddingsNew($arr); // 递归尝试
                    }
                }

                return [];
            }

            // 检查返回结果是否包含data字段
            if (!isset($embeddingsArr['data'])) {
                error_log(date("Y-m-d H:i:s")."|向量化返回结果缺少data字段: ".print_r($embeddingsArr, true)."\r\n", 3,
                         ROOT_PATH."/runtime/log/".date("Ym")."/vectorization.log");
                return [];
            }

            return $embeddingsArr['data'];
        } catch (\Exception $e) {
            error_log(date("Y-m-d H:i:s")."|向量化异常: ".$e->getMessage()."\r\n", 3,
                     ROOT_PATH."/runtime/log/".date("Ym")."/vectorization.log");
            return [];
        }
    }
}

