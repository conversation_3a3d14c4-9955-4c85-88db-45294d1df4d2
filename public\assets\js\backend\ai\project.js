define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'ai/project/index' + location.search,
                    add_url: 'ai/project/add',
                    edit_url: 'ai/project/edit',
                    del_url: 'ai/project/del',
                    multi_url: 'ai/project/multi',
                    import_url: 'ai/project/import',
                    table: 'ai_project',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('编号'), operate: false},
                        {field: 'unit.name', title: __('所属单位'), operate: false},
                        {field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'createtime', title: __('Time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        // {field: 'memo', title: __('Memo'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        // {field: 'console_key', title: __('Console_key'), operate: 'LIKE'},
                        {field: 'url', title: __('url'),formatter: Table.api.formatter.url,operate: false},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        stat: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'ai/project/stat' + location.search,
                    table: 'ai_project',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'question_num',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('编号'), operate: false, sortable: true},
                        {field: 'unit.name', title: __('所属单位'), operate: false},
                        {field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'memory_count', title: __('总内存'), operate: false, sortable: true},
                        {field: 'file_num', title: __('文件总数'), operate: false, sortable: true},
                        {field: 'question_num', title: __('问答总数'), operate: false, sortable: true},
                        {field: 'card_num', title: __('多模态回答'), operate: false, sortable: true},
                        {field: 'font_num', title: __('文字回答'), operate: false, sortable: true},
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        stat_console: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'ai/project/stat_console' + location.search,
                    table: 'ai_project',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'question_num',
                fixedColumns: true,
                fixedRightNumber: 1,
                searchFormVisible: true,
                searchFormTemplate: 'customformtpl',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('编号'), operate: false, sortable: true},
                        {field: 'unit_name', title: __('所属单位'),operate: false, table: table, width: 100,class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'project_name', title: __('所属项目'),operate: false, table: table, width: 100,class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'name', title: __('知识库名'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content, width: 100},
                        {field: 'memory_count', title: __('总内存'), operate: false, sortable: true},
                        {field: 'file_num', title: __('文件总数'), operate: false, sortable: true},
                        {field: 'question_num', title: __('问答总数'), operate: false, sortable: true},
                        {field: 'card_num', title: __('多模态回答'), operate: false, sortable: true},
                        {field: 'font_num', title: __('文字回答'), operate: false, sortable: true},
                        {field: 'video_num', title: __('视频数量'), operate: false, sortable: true},
                        {field: 'image_num', title: __('图片数量'), operate: false, sortable: true},
                        {field: 'vrhot_num', title: __('场景热点数量'), operate: false, sortable: true},
                        {field: 'user_name', title: __('账号'),operate: false, table: table, sortable: true, width: 150, class: 'autocontent', formatter: Table.api.formatter.content},
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
