<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use app\common\service\Convert;

/**
 * API-AI问答配置
 */
class Disposition extends Api
{
    // protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     * UserUrl模型对象
     * @var \app\admin\model\ai\UserUrl
     */
    protected $model = null;
    protected $embeddings = null;

    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\Assistant;
    }
    

    /**
     * 列表
     */
    public function index()
    {
        $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->find();
        $assistant = $this->model->whereRaw('status = 1 OR (status=2 AND FIND_IN_SET('.$project['id'].',project_ids))')->order('sort desc')->select();
        foreach($assistant as &$aval){
            // if($project['assistant'] && $project['assistant_name'] && $project['assistant'] == $aval['id']){
            //     $aval['name'] = $project['assistant_name'];
            // }
            $aval['image'] = cdnurl($aval['image'], true);
        }
        $voice = \app\admin\model\ai\Voice::whereRaw('status = 1 OR (status=2 AND FIND_IN_SET('.$project['id'].',project_ids))')->order('sort desc')->select();
        foreach($voice as &$vval){
            // if($project['voice'] && $project['assistant_name'] && $project['voice'] == $vval['id']){
            //     $vval['name'] = $project['assistant_name'];
            // }
            $vval['voice_path'] = cdnurl($vval['voice_path'], true);
        }
        $gender = $this->model->where('id',$project['assistant'])->value('gender');
        $result = [
            'lang_type'=> $project['lang_type'],
            'assistant' => $assistant,
            'assistant_id'=> $project['assistant'],
            'gender'=> $gender,
            'voice'=> $voice,
            'voice_id'=> $project['voice'],
            'assistant_name'=> $project['assistant_name'],
            'assistant_name_en'=> $project['assistant_name_en'],
        ];
        return json($result);
    }

    /**
     * 保存
     */
    public function save(){
        $lang_type = $this->request->post('lang_type');
        $assistant = $this->request->post('assistant_id');
        $voice = $this->request->post('voice_id');
        $assistant_name = $this->request->post('assistant_name');
        $assistant_name_en = $this->request->post('assistant_name_en');
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $data = [
            'lang_type' => $lang_type,
            'assistant' => $assistant,
            'voice' => $voice,
            'assistant_name' => $assistant_name,
            'assistant_name_en' => $assistant_name_en,
        ];
        $return = \app\admin\model\ai\Project::where('id', $project_id)->update($data);
        if($return){
            $this->success("保存成功!");
        }else{
            $this->error("保存失败!");
        }
    }
}
