<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use app\common\service\Convert;

/**
 * API-问答对管理
 */
class Multimodel extends Api
{
    // protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     * UserUrl模型对象
     * @var \app\admin\model\ai\UserUrl
     */
    protected $model = null;
    protected $embeddings = null;

    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\Multimodel;
    }
    

    /**
     * 列表
     */
    public function index()
    {
        $limit = $this->request->post('limit') ?? 10;
        $page = $this->request->post('page') ?? 1;
        $this->request->get([config('paginate.var_page') => $page]);
        $sort = 'id';
        $order = 'DESC';
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $where = ['robot'=>$project_id];
        if($this->request->post('question')){
            $where['q|a'] = ['like',"%{$this->request->post('question')}%"];
        }
        $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
        foreach ($list as $row) {
            $row->visible(['id','q','a','a1','createtime']);
            $row['createtime'] = date("Y-m-d H:i",$row['createtime']);
            $row['a1'] = Convert::tag($row['a']);
            $row['a1'] = str_ireplace("autoplay","",$row['a1']);
        }

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
        
    }

    /**
     * 创建
     */
    public function create(){
        $console_id = $this->request->post('console_id');
        $url = $this->request->post('url');
        
        $result = false;
        Db::startTrans();
        try {
            // $consoleList = \app\admin\model\ai\Console::where('FIND_IN_SET('.$this->auth->id.',user_id)')->column('id');
            $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
            $params['robot'] = $project_id;
            $params['console_id'] = $console_id;
            $params['url'] = $url;
            $params['createtime'] = time();
            $params['status'] = 0;
            $params['user_id'] = $this->auth->id;
            $result = $this->model->allowField(true)->insertGetId($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        
        $this->success("创建成功!");
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }
}
