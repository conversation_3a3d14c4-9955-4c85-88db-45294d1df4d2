<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Robot')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-robot" data-rule="required" data-source="ai/project/index" data-field="name" class="form-control selectpage" name="row[robot]" type="text" value="{$row.robot|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('图标')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-icon_image" class="form-control" size="50" name="row[icon_image]" type="text" value="{$row.icon_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-icon_image" class="btn btn-danger faupload" data-input-id="c-icon_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-icon_image" data-params='{"category":"icon"}'><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-icon_image" class="btn btn-primary fachoose" data-input-id="c-icon_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-icon_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-icon_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Show_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-show_name" class="form-control" name="row[show_name]" type="text" value="{$row.show_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Question_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-question_name" class="form-control" name="row[question_name]" type="text" value="{$row.question_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('类型')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[type]', ['1'=>__('提问'),'2' => '植物鉴别'],$row['type'])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">排序:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sort" class="form-control" name="row[sort]" type="number" value="{$row.sort|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
