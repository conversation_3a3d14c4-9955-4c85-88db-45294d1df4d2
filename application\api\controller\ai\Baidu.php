<?php
namespace app\api\controller\ai;

use app\common\controller\Api;



class Baidu extends Api
{
	// 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];


	/** 
	 * 调用同步数据到百度BCE
	 */
	function index(){
		$robot = 22;
        $Msg = new \app\admin\model\ai\Msg;
		$askedNumberTotal = $Msg->where('robot',$robot)->count();
		$askedNumberToday = $Msg->where(['robot'=>$robot,'time'=>['>',strtotime(date("Y-m-d"))]])->count();
		$imgProvidedNumber = 100;
		$imgProvidedNumber += $Msg->where(['robot'=>$robot])->whereRaw('content LIKE "%jpg%" OR content LIKE "%png%"')->count();
		$cardNumber = $Msg->where(['robot'=>$robot,'card'=>1])->count();
		$videoProvidedNumber = 10;
		$videoProvidedNumber += $Msg->where(['robot'=>$robot])->whereRaw('content LIKE "%mp4%"')->count();
		$textProvidedNumber = $askedNumberTotal - $imgProvidedNumber - $videoProvidedNumber - $cardNumber;
		$apiUrl = 'https://ck5qvtd2e50hm.cfc-execute.bj.baidubce.com/dataCenter/saveRowByKey';
		$top_list = [];
		$question_typearr = collection(\app\admin\model\ai\Tagextraction::where(['robot'=>$robot,'name'=>['not like','%问%']])->whereRaw('CHAR_LENGTH(`name`)>2')->order('num','desc')->limit(6)->select())->toArray();
        $question_typenum = 0;
        foreach($question_typearr as $item){
            $question_typenum+=$item['num'];
        }
        foreach($question_typearr as $item){
            $key = str_replace("\r","",$item['name']);
            $proportion = $item['num']>0?intval(($item['num']/$question_typenum)*100):0;
			$top_list[] = [
				"name"=> $key,
				"count"=> strval($item['num']),
				"proportion"=> strval($proportion)
			];
        }
		$top_json = json_encode($top_list);
		/** 高频问题排行 */
        $question_listarr = collection(\app\admin\model\ai\Tagextraction::where(['robot'=>$robot,'msg_id'=>['<>',''],'name'=>['not like','%问%']])->whereRaw('CHAR_LENGTH(`name`)>2')->order('num','desc')->limit(100)->select())->toArray();
        $top_questiondata = $top_questionlist = [];
        foreach($question_listarr as $key=>$item){
            if(in_array($item['msg_id'],$top_questiondata)){
                continue;
            }
            $top_questiondata[] = $item['msg_id'];
            $msg = $Msg->where(['robot'=>$robot,'id'=>$item['msg_id']])->find();
            $msg['content'] = str_ireplace("autoplay","",$msg['content']);
            if(empty($msg)){
                continue;
            }
            if(count($top_questionlist)>=10)break;
            $top_questionlist[] = 
                [
                    'name'=>$msg['msg'],
                    'count'=>$item['num'],
                ];
        }
		$mostPopularQuestions = json_encode($top_questionlist);
		$data = [
			"rowkey" => 'FCC2-AI-columnInfo',
			"rowdata" => [
				[
					"column"=> "askedNumberTotal",
					"value"=> strval($askedNumberTotal)
				],
				[
					"column"=> "askedNumberToday",
					"value"=> strval($askedNumberToday)
				],
				[
					"column"=> "textProvidedNumber",
					"value"=> strval($textProvidedNumber)
				],
				[
					"column"=> "imgProvidedNumber",
					"value"=> strval($imgProvidedNumber+$cardNumber)
				],
				[
					"column"=> "videoProvidedNumber",
					"value"=> strval($videoProvidedNumber+$cardNumber)
				],
				[
					"column"=> "mostPopularWords6",
					"value"=> $top_json
				],
				[
					"column"=> "mostPopularQuestions",
					"value"=> $mostPopularQuestions
				]
			],
			"tableName"=> "project_data"
		];
		$headers = [
			'Content-Type: application/json'
		];
		
		$options = [
			'http' => [
				'header' => implode("\r\n", $headers),
				'method' => 'POST',
				'content' => json_encode($data),
				'timeout' => 30
			]
		];
		
		$context = stream_context_create($options);
		$result = @file_get_contents($apiUrl, false, $context);
		$list = [];
		if ($result !== false) {
			$data = json_decode($result, true);
			$list = isset($data['msg'])?$data['msg']:[];
		} else {
			trace(date("Y-m-d H:i:s")."|error:".print_r(error_get_last(),1));
		}
		return $list;
	}


	/*
	* 植物识别
	*/
	public function medicineIdentifySteamApi()
	{
		
		$url = input('url');

		if (empty($url)) {
			$this->error('参数错误');
		}
		$res = $this->runApi(['url' => $url]);
		$res = json_decode($res,true);
		if (!$res['result'] || !$res['result'][0]) {
			$this->error('识别失败');
		}

		$this->success('success',$res['result']);

	}

	public function runApi($params) {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://aip.baidubce.com/rest/2.0/image-classify/v1/plant?access_token={$this->getAccessToken()}",
            CURLOPT_TIMEOUT => 30,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER  => false,
            CURLOPT_SSL_VERIFYHOST  => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query($params),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded',
                'Accept: application/json'
            ),

        ));
        $response = curl_exec($curl);
        curl_close($curl);
        return $response;
    }
    
    /**
     * 获取文件base64编码
     * @param string  $path 文件路径
     * @return string base64编码信息，不带文件头
     */
    private function getFileContentAsBase64($path){
        
          return base64_encode(file_get_contents($path));
        
    }
    
    /**
     * 使用 AK，SK 生成鉴权签名（Access Token）
     * @return string 鉴权签名信息（Access Token）
     */
    private function getAccessToken(){
        $curl = curl_init();
        $postData = array(
            'grant_type' => 'client_credentials',
            'client_id' => config('baidu.api_key'),
            'client_secret' => config('baidu.secret_key')
        );
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://aip.baidubce.com/oauth/2.0/token',
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_SSL_VERIFYPEER  => false,
            CURLOPT_SSL_VERIFYHOST  => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POSTFIELDS => http_build_query($postData)
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $rtn = json_decode($response);
        return $rtn->access_token;
    }





}