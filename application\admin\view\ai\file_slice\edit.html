<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Slice_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-slice_id" data-rule="required" data-source="slice/index" class="form-control selectpage" name="row[slice_id]" type="text" value="{$row.slice_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-type" class="form-control" name="row[type]" type="text" value="{$row.type|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Knowledgebaseid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-knowledgeBaseId" class="form-control" name="row[knowledgeBaseId]" type="text" value="{$row.knowledgeBaseId|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Documentid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-documentId" class="form-control" name="row[documentId]" type="text" value="{$row.documentId|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-content" class="form-control" name="row[content]" type="text" value="{$row.content|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Enabled')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-enabled" class="form-control selectpicker" name="row[enabled]">
                {foreach name="enabledList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.enabled"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Wordcount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-wordCount" class="form-control" name="row[wordCount]" type="number" value="{$row.wordCount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tokencount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tokenCount" class="form-control" name="row[tokenCount]" type="number" value="{$row.tokenCount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Statusmessage')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-statusMessage" class="form-control" name="row[statusMessage]" type="text" value="{$row.statusMessage|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Createtime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-createTime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[createTime]" type="text" value="{:$row.createTime?datetime($row.createTime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Updatetime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-updateTime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[updateTime]" type="text" value="{:$row.updateTime?datetime($row.updateTime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Deletetime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-deleteTime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[deleteTime]" type="text" value="{:$row.deleteTime?datetime($row.deleteTime):''}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
