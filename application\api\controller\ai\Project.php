<?php
namespace app\api\controller\ai;

use app\common\controller\Api;
use think\Db;

class project extends Api
{
	// 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];
    
	/**
	 * 获取AI项目列表
	 */
    public function list()
    {
        $pageNumber = input('pageNumber');
        $pageSize = input('pageSize');
        $q_word = input('q_word/a') ? input('q_word/a')[0] : [];
        $where = [];
        if(!empty(input('keyValue'))) {
            $where[input('keyField')] = input('keyValue');
        }
        if (!empty($q_word)) {
            $where['name'] = ['like','%'.$q_word.'%'];
        }
        $list = \app\admin\model\ai\Project::where($where)->field('id,name')
        ->page($pageNumber,$pageSize)
        ->order('id desc')->select();

        $total = \app\admin\model\ai\Project::where($where)->count();
        return json(['list' => $list,'total' => $total]);
    }
	
}