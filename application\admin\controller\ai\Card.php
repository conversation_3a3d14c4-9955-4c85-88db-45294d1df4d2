<?php

namespace app\admin\controller\ai;

use app\common\controller\Backend;
use app\admin\library\Auth;
use app\admin\model\ai\Project;
use Exception;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\db\exception\BindParamException;
use think\exception\PDOException;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx as Xlsx1;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use think\Db;
use think\exception\ValidateException;
use app\common\service\ai\AiService;
use think\Queue;


/**
 * 卡片管理
 *
 * @icon fa fa-circle-o
 */
class Card extends Backend
{

    /**
     * Card模型对象
     * @var \app\admin\model\ai\Card
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\Card;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['project','console'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','q','a','createtime','console_id','lang']);
                $row->visible(['project']);
				$row->getRelation('project')->visible(['name']);
                $row->visible(['console']);
				$row->getRelation('console')->visible(['name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $newData = $this->getEmbeddingsNew([$params['q']]);
            $vectorization = base64_encode(json_encode($newData[0]));
            $params['vectorization'] = $vectorization;
            
            $params['robot'] = \app\admin\model\ai\Project::where('FIND_IN_SET('.$params['console_id'].',console_ids)')->value('id');
            $result = $this->model->allowField(true)->save($params);
            Db::commit();

            //推入训练队列中
            Queue::push('app\queue\job\CardToPGsql', [
                'type' => 1
            ],'card_to_pgsql');
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    

    /**
     * 批量添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function batch()
    {
        setlocale(LC_ALL, 'zh_CN.UTF-8');

        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $fileList = explode(',',$params['file']);

            $segmentedArray = array_chunk($fileList, 15);
            foreach($segmentedArray as $sval)
            {
                $newData = $data = [];
                foreach($sval as $key=>$item)
                {
                    $data[] = pathinfo($item, PATHINFO_FILENAME);
                }
                $newData = $this->getEmbeddingsNew($data);
                $insert = [];
                foreach($sval as $key1=>&$item1)
                {
                    // 通过文件路径查询attachment表获取原始文件名
                    $originalFilename = \app\common\model\Attachment::where('url', $item1)->value('filename');
                    $q = $originalFilename ? pathinfo($originalFilename, PATHINFO_FILENAME) : pathinfo($item1, PATHINFO_FILENAME);
                    $source = cdnurl($item1, true);
                    if(strpos($item1,'.mp4')!==false)
                    {
                        $fileNew = str_replace(request()->domain(),'',$item1);
                        $cover = str_replace('.mp4','.jpg',$fileNew);
                        $this->extractVideoCover(ROOT_PATH ."public" . $fileNew, ROOT_PATH ."public" . $cover);
                        $cover = cdnurl($cover, true);
                    }else{
                        $cover = $source;
                    }
                    $a = "[![{$q}]({$cover})]({$source})";
                    $code = md5($a);
                    $vectorization = base64_encode(json_encode($newData[$key1]));
                    
                    $robot = \app\admin\model\ai\Project::where('FIND_IN_SET('.$params['console_id'].',console_ids)')->value('id');
                    $insert[] = [
                        'robot' => $robot,
                        'console_id' => $params['console_id'],
                        'lang' => $params['lang'],
                        'code' => $code,
                        'q' => $q,
                        'a' => $a,
                        'vectorization' => $vectorization,
                        'createtime' => time()
                    ];
                }
                $result = $this->model->saveAll($insert);
            }


            /*
            $insert = [];
            foreach($fileList as $item)
            {
                $q = pathinfo($item, PATHINFO_FILENAME);
                $source = cdnurl($item, true);
                if(strpos($item,'.mp4')!==false)
                {
                    $fileNew = str_replace(request()->domain(),'',$item);
                    $cover = str_replace('.mp4','.jpg',$fileNew);
                    $this->extractVideoCover(ROOT_PATH ."public" . $fileNew, ROOT_PATH ."public" . $cover);
                }else{
                    $cover = $source;
                }
                $a = "[![{$q}]({$cover})]({$source})";
                $code = md5($a);
                $newData = $this->getEmbeddingsNew([$q]);
                $vectorization = base64_encode(json_encode($newData[0]));
                $insert[] = [
                    'robot' => $params['robot'],
                    'code' => $code,
                    'q' => $q,
                    'a' => $a,
                    'vectorization' => $vectorization,
                    'createtime' => time()
                ];
            }
            dump($insert);exit;
            $result = $this->model->insertAll($insert);
            */
            Db::commit();

            //推入训练队列中
            Queue::push('app\queue\job\CardToPGsql', [
                'type' => 1
            ],'card_to_pgsql');
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }
    
    /**
     * 提取视频第一帧并保存为图片
     */
    function extractVideoCover($videoPath, $outputImagePath) {
        // FFmpeg命令，提取视频第一帧并保存为图片
        $command = "ffmpeg -i '{$videoPath}' -ss 00:00:01.000 -vframes 1 '{$outputImagePath}'";
        // 使用shell_exec执行命令
        $output = shell_exec($command);
    
        // 检查命令执行是否成功
        // if ($output === null) {
        //     throw new Exception("Failed to extract video cover.");
        // }
    
        return true; // 成功
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $newData = $this->getEmbeddingsNew([$params['q']]);
            $vectorization = base64_encode(json_encode($newData[0]));
            $params['vectorization'] = $vectorization;
            $params['sync'] = 0;
            
            $params['robot'] = \app\admin\model\ai\Project::where('FIND_IN_SET('.$params['console_id'].',console_ids)')->value('id');
            $result = $row->allowField(true)->save($params);
            Db::commit();

            //推入训练队列中
            Queue::push('app\queue\job\CardToPGsql', [
                'type' => 1
            ],'card_to_pgsql');
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            $aiEngine = AiService::getEngine('local');
            $aiEngine->deleteVectorData(1,$ids);
            foreach ($list as $item) {
                $count += $item->delete();
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }

    /**
     * 导入
     *
     * @return void
     * @throws PDOException
     * @throws BindParamException
     */
    public function import()
    {
        $file = $this->request->request('file');
        if (!$file) {
            $this->error(__('Parameter %s can not be empty', 'file'));
        }
        $filePath = ROOT_PATH . DS . 'public' . DS . $file;
        if (!is_file($filePath)) {
            $this->error(__('No results were found'));
        }
        //实例化reader
        $ext = pathinfo($filePath, PATHINFO_EXTENSION);
        if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
            $this->error(__('Unknown data format'));
        }
        if ($ext === 'csv') {
            $file = fopen($filePath, 'r');
            $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
            $fp = fopen($filePath, 'w');
            $n = 0;
            while ($line = fgets($file)) {
                $line = rtrim($line, "\n\r\0");
                $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                if ($encoding !== 'utf-8') {
                    $line = mb_convert_encoding($line, 'utf-8', $encoding);
                }
                if ($n == 0 || preg_match('/^".*"$/', $line)) {
                    fwrite($fp, $line . "\n");
                } else {
                    fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                }
                $n++;
            }
            fclose($file) || fclose($fp);

            $reader = new Csv();
        } elseif ($ext === 'xls') {
            $reader = new Xls();
        } else {
            $reader = new Xlsx();
        }
        //加载文件
        $insert = [];
        try {
            if (!$PHPExcel = $reader->load($filePath)) {
                $this->error(__('Unknown data format'));
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表
            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行
            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            $fields = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[] = $val;
                }
            }
            $robot = Project::where('name','like',trim($fields[0]))->value('id');
            if($robot<1){
                throw new Exception('请填写正确的项目名称!');
            }
            $fields = [];
            for ($currentRow = 2; $currentRow <= 2; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[] = $val;
                }
            }
            if(!($fields[0]=='所属知识库' && $fields[1]=='标签' && $fields[2]=='封面图片链接' && $fields[3]=='答案链接' && $fields[4]=='所属语言')){
                throw new Exception('模版格式不正确!');
            }

            for ($currentRow = 3; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $values[] = is_null($val) ? '' : $val;
                }
                if(empty($values[0]))continue;
                $row = [];
                $row['robot'] = $robot;
                $console_id = \app\admin\model\ai\Console::where('name',trim($values[0]))->value('id');
                if($console_id<1){
                    throw new Exception('请填写正确的知识库名称!行：'.$currentRow);
                }
                $row['console_id'] = $console_id;
                $row['q'] = "{$values[1]}";
                $row['a'] = "[![{$values[1]}]({$values[2]})]({$values[3]})";
                $row['code'] = md5($row['a']);
                $row['lang'] = $values[4]=='英文'?'en':'zh';
                $result = $this->model->where(['robot'=>$robot,'code'=>$row['code']])->find();
                if($result || empty($row['q']))
                {
                    continue;
                    // $this->model->where('id',$result['id'])->update($row);
                }else{
                    if ($row) {
                        $insert[] = $row;
                    }
                }
            }
        } catch (Exception $exception) {
            $this->error($exception->getMessage());
        }
        if (!$insert) {
            $this->error(__('No rows were updated'));
        }
        try {
            $segmentedArray = array_chunk($insert, 15);
            foreach($segmentedArray as $sval)
            {
                $newData = $data = [];
                foreach($sval as $key=>$item)
                {
                    $data[] = $item['q'];
                }
                $newData = $this->getEmbeddingsNew($data);
                foreach($sval as $key1=>&$item1)
                {
                    $vectorization = base64_encode(json_encode($newData[$key1]));
                    $item1['vectorization'] = $vectorization;
                }
                $this->model->saveAll($sval);
            }
            //推入训练队列中
            Queue::push('app\queue\job\CardToPGsql', [
                'type' => 1
            ],'card_to_pgsql');
        } catch (PDOException $exception) {
            $msg = $exception->getMessage();
            if (preg_match("/.+Integrity constraint violation: 1062 Duplicate entry '(.+)' for key '(.+)'/is", $msg, $matches)) {
                $msg = "导入失败，包含【{$matches[1]}】的记录已存在";
            };
            $this->error($msg);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success();
    }

	/** 
	 * 调用生成向量数组匹配对应关系
	 */
	function getEmbeddingsNew($arr){
		$input = json_encode(['input'=>$arr]);
		
		$embeddings = new \app\common\library\Embeddings;
		$embeddingsStr = $embeddings->run($input);
		$embeddingsArr = json_decode($embeddingsStr,true);
		return $embeddingsArr['data'];
	}

    /**
     * 导出
     */
    public function export(){

        $where = [];
        $name = '卡片导出-'.date("Ymd");
        $list = $this->model
                ->where($where)
                ->field('*')
                ->select();
        $data = [];
        foreach ($list as $row) {
            # [![【赤泥沉降分离】洗涤正常停车（仿真）](https://vtrs.jw100.com.cn/upload/static/AI/kunye/guandrc3.JPG)](https://vtrs.jw100.com.cn/upload/static/AI/kunye/guandrc3.mp4)
            preg_match('/\[(.*?)\]\((.*?)\)\]\((.*?)\)/',$row['a'],$matches);
            if(isset($matches[2]))
            {
                $title = Project::where('id',$row['robot'])->value('name');
                $console_name = \app\admin\model\ai\Console::where('id',$row['console_id'])->value('name');
                $data[] = [
                    'project' => $title,
                    'console_name' => $console_name,
                    'name' => $row['q'],
                    'thum' => $matches[2],
                    'path' => $matches[3]
                ];
            }

        }
        
        $spreadsheet = new Spreadsheet();

        $this->opsheet($spreadsheet,0,$data,"导出全部");

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $name . '.xlsx"');
        header('Cache-Control: max-age=0');
        $writer = new Xlsx1($spreadsheet);
        $writer->save('php://output');
        //删除清空：
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);
        exit;
    }

    // 处理表格
    public function opsheet($spreadsheet,$n, $data,$name){
        $spreadsheet->createSheet();//创建sheet
        $objActSheet = $spreadsheet->setActiveSheetIndex($n);//设置当前的活动sheet
        

        $sheet = $spreadsheet->getActiveSheet($n)->setTitle('sheet');//设置sheet的名称

        $spreadsheet->getActiveSheet($n)->mergeCells('A1:E1'); //合并单元格

        //$spreadsheet->getActiveSheet($n)->getStyle('A1')->getFont()->setSize(20); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('A')->getFont()->setSize(10); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('B')->getFont()->setSize(10); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('C')->getFont()->setSize(10); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('D')->getFont()->setSize(10); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('E')->getFont()->setSize(10); //设置title的字体大小

        $spreadsheet->getActiveSheet($n)->getStyle('A')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('A')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('B')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('B')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('C')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('C')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('D')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('D')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('E')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('E')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中

        $spreadsheet->getActiveSheet($n)->getColumnDimension('A')->setWidth(40); //固定列宽
        $spreadsheet->getActiveSheet($n)->getColumnDimension('B')->setWidth(60); //固定列宽
        $spreadsheet->getActiveSheet($n)->getColumnDimension('C')->setWidth(80); //固定列宽
        $spreadsheet->getActiveSheet($n)->getColumnDimension('D')->setWidth(80); //固定列宽
        $spreadsheet->getActiveSheet($n)->getColumnDimension('E')->setWidth(80); //固定列宽

        $spreadsheet->getActiveSheet($n)->getDefaultRowDimension()->setRowHeight(20);

        $styleArray1 = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' =>  \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN //细边框
                ]
            ]
        ];

        $objActSheet->setCellValue('A1', $name);

        //表头
        $objActSheet->setCellValue('A2', '项目名称');
        $objActSheet->setCellValue('B2', '知识库名称');
        $objActSheet->setCellValue('C2', '标签');
        $objActSheet->setCellValue('D2', '封面图片链接');
        $objActSheet->setCellValue('E2', '答案链接');

        //数据
        if(!empty($data)){
            $i = 3;
            foreach ($data as $k => $v)
            {
                $objActSheet->setCellValue('A'.$i, $v['project']);
                $objActSheet->setCellValue('B'.$i, $v['console_name']);
                $objActSheet->setCellValue('C'.$i, $v['name']);
                $objActSheet->setCellValue('D'.$i, $v['thum']);
                $objActSheet->setCellValue('E'.$i, $v['path']);
                $i++;
            }
            $spreadsheet->getActiveSheet($n)->getStyle('A1'.':'.'E'.($i-1))->applyFromArray($styleArray1);
        }
    }
}
