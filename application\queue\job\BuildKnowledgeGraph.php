<?php

namespace app\queue\job;

use app\common\service\BuildGraphService;
use app\common\service\ai\AiService;

use think\Queue\Job;
use think\Db;


class BuildKnowledgeGraph
{


    /**
     *
     * 自动上传文件到知识库中（豆包+百度)
     * 
     **/
    public function fire(Job $job, $data)
    {

        try {
            $filepath = $data['file_path'];
            $console_file_id = $data['console_file_id'];

            echo '知识图谱文档切分开始执行：' . $filepath . "\n";
            echo '文件id：'.$console_file_id."\n";

            if (!is_file($filepath)) {
                $job->delete();
                return;
            }

            Db::connect([],true);

            $file = Db::name('ai_console_file')->where('id',$console_file_id)->where('deletetime',null)->value('id');
            if (!$file) {
                $job->delete();
                return;
            }

            //重试超过3次
            if ($job->attempts() > 3) {
                //通过这个方法可以检查这个任务已经重试了几次了
                $job->delete();
            }

            $res = BuildGraphService::splitDocByTitle($filepath,$console_file_id);

            // $engine = AiService::getEngine('baidu');
            // $res = $engine->splitDocByTitle($filepath);
            // $res = json_decode($res,true);

            // if (!$res) {
            //     //修改文件目录提取状态失败
            //     \app\admin\model\ai\ConsoleFile::where("id", $console_file_id)->update(['catalog_status'=>2]);
            // }

            // $r = BuildGraphService::parseContent($console_file_id,$res);

            echo '执行结果：' . json_encode($res,JSON_UNESCAPED_UNICODE) . "\n";
            echo date('Y-m-d H:i:s') . "\n";
            echo '======执行成功======' . "\n";
                
            $job->delete();
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
        

    }


    public function failed($data){
        // ...任务达到最大重试次数后，失败了
    }
}