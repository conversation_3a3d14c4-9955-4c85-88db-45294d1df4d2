<?php

namespace app\api\controller\callback;

use app\common\controller\Api;
use app\common\service\BuildGraphService;

use PhpOffice\PhpWord\Shared\ZipArchive;
use think\Db;

class Mineru extends Api
{

	protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

	public function index()
	{
		$data = file_get_contents('php://input');
		file_put_contents(ROOT_PATH . 'public/mineruData.txt',json_encode($data),FILE_APPEND);
// 		$data = '{"content":"{\"batch_id\":\"3bff32d5-9469-47d4-a5d3-04fb756560a0\",\"extract_result\":[{\"file_name\":\"dc1025f55a69eaa68722f1abee38f7d9.pdf\",\"state\":\"done\",\"err_msg\":\"\",\"full_zip_url\":\"https://cdn-mineru.openxlab.org.cn/pdf/dbe92d2e-289c-4ce6-99c5-7fb66dd998be.zip\"}]}","checksum":"4b5a6cf1ba2ea24be87e4f48fe12efac749f9c150837c6bfdbea856ff2179d82"}';
		// $data = "{\"content\":\"{\\\"batch_id\\\":\\\"959b25d3-c351-4e08-857b-ca8fb7318374\\\",\\\"extract_result\\\":[{\\\"file_name\\\":\\\"83b98a689678f005eafd272b897c9955.docx\\\",\\\"state\\\":\\\"done\\\",\\\"err_msg\\\":\\\"\\\",\\\"full_zip_url\\\":\\\"https:\/\/cdn-mineru.openxlab.org.cn\/pdf\/0866444b-9f40-4ccf-8245-f86893c385d1.zip\\\"}]}\",\"checksum\":\"1952f7907f9d55651d4d92b6586005f75960f6890ff4df9e8310fba555c5ec0f\"}";
		$data = json_decode($data,true);
		$content = json_decode($data['content'],true);
		if (!$content || !isset($content['extract_result'][0]) || empty($content['extract_result'][0]['full_zip_url'])) {
			echo '数据错误，没有完成的任务';die;
		}
		$filename = pathinfo($content['extract_result'][0]['file_name'],PATHINFO_FILENAME);
		$console_file_id = cache('mineru-analyze-' . $filename);
		if (!$console_file_id) {
			echo 'console_file_id不存在';die;
		}

		$downloadUrl = $content['extract_result'][0]['full_zip_url'];
		$extractPath = ROOT_PATH . 'public/mineru_unzip_dir/' . $filename; // 替换为实际的解压目录

		if (!$this->downloadAndExtractZip($downloadUrl, $extractPath)) {
			echo '下载解压失败';die;
		}

		$dirHandle = opendir($extractPath);
		$file = $extractPath . '/';
		while (($item = readdir($dirHandle)) !== false) {
			// 跳过当前目录和上级目录
			if ($item === '.' || $item === '..') {
				continue;
			}
			
			if (strpos($item,'_content_list') !== false) {
				$file .= $item;
				break;
			}
		}

		$jsonData = json_decode(file_get_contents($file),true);
		if (!$jsonData) {
			echo 'json解析失败';die;
		}

		(new BuildGraphService)->mineruSaveData($jsonData,$console_file_id);

	}


	// 定义下载和解压函数
	private function downloadAndExtractZip($url, $destinationPath) {
		// 创建临时文件
		$tempFile = tempnam(sys_get_temp_dir(), 'zip_');
		$zipResource = fopen($tempFile, 'w');

		// 初始化cURL会话
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_FAILONERROR, true);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
		curl_setopt($ch, CURLOPT_AUTOREFERER, true);
		curl_setopt($ch, CURLOPT_BINARYTRANSFER, true);
		curl_setopt($ch, CURLOPT_TIMEOUT, 10);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
		curl_setopt($ch, CURLOPT_FILE, $zipResource);

		// 执行cURL请求
		$page = curl_exec($ch);

		// 检查错误
		if (curl_errno($ch)) {
			$error = curl_error($ch);
			curl_close($ch);
			fclose($zipResource);
			unlink($tempFile);
			throw new \Exception("下载失败: $error");
		}

		// 获取HTTP响应码
		$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		curl_close($ch);
		fclose($zipResource);

		// 检查HTTP响应码
		if ($httpCode != 200) {
			unlink($tempFile);
			throw new \Exception("下载失败: HTTP响应码 $httpCode");
		}

		// 创建目标目录（如果不存在）
		if (!file_exists($destinationPath)) {
			mkdir($destinationPath, 0777, true);
		}

	
		// 初始化ZipArchive
		$zip = new ZipArchive();

		// 打开ZIP文件
		$result = $zip->open($tempFile);
		if ($result === true) {
			// 解压文件到目标目录
			$zip->extractTo($destinationPath);
			
			// 关闭ZIP文件
			$zip->close();
			
			// echo "解压成功！文件已解压到: $destinationPath";
		} else {
			return false;
		}
		unlink($tempFile);

		return true;
	}



}