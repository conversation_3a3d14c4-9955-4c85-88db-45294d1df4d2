<?php

namespace app\admin\service\ai;

use think\Db;
use think\Log;
use think\Queue;
use app\admin\model\ai\Console;
use app\admin\model\ai\ConsoleFile;
use app\admin\model\ai\Card;
use think\exception\ValidateException;
use think\exception\PDOException;

class ConsoleCopyService
{
	private $pgTable = 'public.vector_data_upgrades';
    public function copy($ids)
    {
        # 查询知识库数据
        $console = Console::get($ids);
        $trainType = $console['type'] != 'local';

        Db::startTrans();
        $fileNum = $cardNum = $knowNum = 0;
        try {
            #-----------------------复制知识库数据-----------------------
            $consoleId = Console::insertGetId([
                'name' => $console['name'] . ' 副本',
                'console_number' => $this->generateUUIDv4(),
                'image' => $console['image'],
                'sort' => $console['sort'],
                'type' => 'local',
                'memo' => $console['memo'],
                'admin_id' => $console['admin_id'],
                'createtime' => time(),
                'updatetime' => time(),
                'user_id' => $console['user_id'],
            ]);
            #-----------------------复制知识库数据-----------------------

            #-----------------------复制文件数据-----------------------
            $consoleFile = ConsoleFile::where('console_id', $ids)->select();
            $fileInsert = [];
            $user_id = 1;
            foreach ($consoleFile as &$file) {
                $fileInsert[] = [
                    'console_id' => $consoleId,
                    'name' => $file['name'],
                    'createtime' => time(),
                    'updatetime' => time(),
                    'display_status' => 0,
                    'file_number' => $this->generateUUIDv4(),
                    'source_id' => $file['id'],
                    'type' => $file['type'],
                    'file_size' => $file['file_size'],
                    'catalog_status' => $file['catalog_status'],
                    'build_graph_status' => $file['build_graph_status'],
                    'user_id' => $user_id,
                    'file' => $file['file'],
                    'display_status' => $trainType ? 0 : $file['display_status'],
                    'word_count' => $trainType ? 0 : $file['word_count'],
                    'file_md5' => $file['file_md5'],

                ];
                $fileNum++;
            }
            $fileResult = ConsoleFile::insertAll($fileInsert);
            if ($fileResult) {
                if ($trainType) {
                    # 存入队列训练
                    $insertSelect = ConsoleFile::where(['console_id' => $consoleId, 'display_status' => 0])->select();
                    foreach ($insertSelect as $item) {
                        // 推入训练队列中
                        Queue::push('app\queue\job\UploadFileToConsole', [
                            'url' => request()->domain() . $item['file'],
                            'console_number' => $item['console_number'],
                            'console_file_id' => $item['id'],
                            'filename' => $item['name'],
                            'engine_type' => 'local',
                            'user_id' => 1,
                        ], config('task.file'));
                    }
                } else {
                    #直接复制PGSQL切片数据
                    $query = $this->pg_connect();
                    $sql = "SELECT content,embedding,md5,word_count,file_id FROM {$this->pgTable} where console_id=" . $ids;
                    $list = $query->query($sql);
                    $sliceInsert = [];
                    foreach ($list as $item) {
                        $new_file_id = ConsoleFile::where(['console_id' => $consoleId, 'source_id' => $item['file_id']])->value('id');
                        $sliceInsert[] = [
                            'console_id' => $consoleId,
                            'file_id' => $new_file_id,
                            'content' => $item['content'],
                            'embedding' => $item['embedding'],
                            'md5' => $item['md5'],
                            'word_count' => $item['word_count'],
                        ];
                        if (count($sliceInsert) > 1000) {
                            $sql = '';
                            foreach ($sliceInsert as $v) {
                                $sql .= "({$v['console_id']},{$v['file_id']},'{$v['content']}','{$v['embedding']}','{$v['md5']}',{$v['word_count']}),";
                            }
                            $sql = rtrim($sql, ',');

                            $sql1 = "INSERT INTO {$this->pgTable} (console_id,file_id,content,embedding,md5,word_count) VALUES " . $sql;
                            $return = $query->execute($sql1);
                            if (!$return) {
                                error_log(date("Y-m-d H:i:s") . "|sql1:" . print_r($sql1, 1) . "|return:" . print_r($return, 1) . "\r\n", 3, ROOT_PATH . "/runtime/log/" . date("Ym") . "/copy.log");
                            }
                            $sliceInsert = [];
                        }
                    }
                    if ($sliceInsert) {
                        $sql = '';
                        foreach ($sliceInsert as $v) {
                            $sql .= "({$v['console_id']},{$v['file_id']},'{$v['content']}','{$v['embedding']}','{$v['md5']}',{$v['word_count']}),";
                        }
                        $sql = rtrim($sql, ',');

                        $sql1 = "INSERT INTO {$this->pgTable} (console_id,file_id,content,embedding,md5,word_count) VALUES " . $sql;
                        $return = $query->execute($sql1);
                        if (!$return) {
                            error_log(date("Y-m-d H:i:s") . "|sql1:" . print_r($sql1, 1) . "|return:" . print_r($return, 1) . "\r\n", 3, ROOT_PATH . "/runtime/log/" . date("Ym") . "/copy.log");
                        }
                    }
                    # 更新文件状态
                    $consoleFileList = ConsoleFile::where(['console_id' => $consoleId, 'display_status' => 0])->select();
                    foreach($consoleFileList as $item1){
                        $sql = "SELECT sum(word_count) word_count FROM {$this->pgTable} where file_id=" . $item1['id'];
                        $word_count = $query->query($sql);
                        ConsoleFile::where(['id'=>$item1['id']])->update(['display_status'=>2, 'word_count'=>$word_count[0]['word_count']]);
                    }
                }
                #-----------------------复制文件数据-----------------------

                # -----------------------复制知识图谱数据-----------------------
                $insertSelect = ConsoleFile::where(['console_id' => $consoleId])->select();
                foreach ($insertSelect as $item) {
                    // 推入知识图谱生成队列
                    Queue::push('app\queue\job\BuildKnowledgeGraph', [
                        'file_path' => ROOT_PATH . "public" . $item['file'],
                        'console_file_id' => $item['id']
                    ], 'ai_file_buildgraph');
                }
                if (false) {
                    $fileNewArr = ConsoleFile::where(['console_id' => $consoleId])->select();
                    foreach ($fileNewArr as $fval) {
                        #切片目录表
                        $fileSliceCateLog = Db::name('ai_file_slice_catalog')->where(['file_id' => $fval['source_id']])->select();
                        $fileSliceCateLogInsert = [];
                        foreach ($fileSliceCateLog as $fval1) {
                            $fileSliceCateLogInsert[] = [
                                'file_id' => $fval['id'],
                                'name' => $fval1['name'],
                                'pid' => $fval1['pid'],
                                'level' => $fval1['level'],
                                'createtime' => time()
                            ];
                        }
                        if ($fileSliceCateLogInsert) {
                            Db::name('ai_file_slice_catalog')->insertAll($fileSliceCateLogInsert);
                        }

                        #切片表
                        $fileSlice = Db::name('ai_file_slice')->where(['file_id' => $fval['source_id']])->select();
                        $fileSliceInsert = [];
                        foreach ($fileSlice as $fval2) {
                            $catalog_id = Db::name('ai_file_slice_catalog')->where(['file_id' => $fval['id']])->value('id');
                            $fileSliceInsert[] = [
                                'file_id' => $fval['id'],
                                'content' => $fval2['content'],
                                'catalog_id' => $catalog_id,
                                'createtime' => time()
                            ];
                        }
                        if ($fileSliceInsert) {
                            Db::name('ai_file_slice')->insertAll($fileSliceInsert);
                        }
                    }

                    #文件目录分类表
                    $fileSliceCate = Db::name('ai_file_slice_catalog_cate')->where(['console_id' => $ids])->select();
                    $fileSliceCateInsert = [];
                    foreach ($fileSliceCate as $fval3) {
                        $catalog_id = Db::name('ai_file_slice_catalog')->where(['file_id' => $fval['id']])->value('id');
                        $fileSliceCateInsert[] = [
                            'console_id' => $consoleId,
                            'pid' => $fval3['pid'],
                            'level' => $fval3['level'],
                            'name' => $fval3['name'],
                            'catalog_id' => $fval3['catalog_id'],
                            'createtime' => time()
                        ];
                    }
                    if ($fileSliceCateInsert) {
                        Db::name('ai_file_slice_catalog_cate')->insertAll($fileSliceCateInsert);
                    }
                }
                # -----------------------复制知识图谱数据-----------------------

            }

            #-----------------------复制卡片数据-----------------------
            if(false)
            {
                $card = Card::where('console_id', $ids)->select();
                $cardInsert = [];
                foreach ($card as &$item) {
                    $item['console_id'] = $consoleId;
                    $item['createtime'] = time();
                    $item['updatetime'] = time();
                    $cardInsert[] = $item;
                    if (count($cardInsert) > 1000) {
                        Card::insertAll($cardInsert);
                        $cardInsert = [];
                    }
                    $cardNum++;
                }
                if ($cardInsert) {
                    Card::insertAll($cardInsert);
                }
            }
            #-----------------------复制卡片数据-----------------------

            Db::commit();
            Log::info("复制知识库成功,Source:{$ids},Target:{$consoleId}");
            return true;
        } catch (ValidateException|PDOException|\Exception $e) {
            error_log(date("Y-m-d H:i:s") . "| Error：" . print_r($e->getMessage(),1) . "\r\n", 3, ROOT_PATH . "/runtime/log/" . date("Ym") . "/copy.log");
            return false;
        }
    }

    function generateUUIDv4()
    {
        $data = random_bytes(16);
        // 设置 UUID 版本 (4) 和 variant bits
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }

    /** 链接PGsql数据库 */
    private function pg_connect()
    {
        $request = Db::connect(
            [
                'type' => 'pgsql',
                'hostname' => '127.0.0.1',
                'database' => config('postgres.database'),
                'username' => 'postgres',
                'password' => 'DRrTmhCKrLWs2b34',
                'hostport' => '5432'
            ]
        );
        return $request;
    }
}