<?php

namespace app\admin\controller\ai;

use app\admin\model\ai\Console;
use app\admin\model\ai\ConsoleFile as AiConsoleFile;
use app\common\controller\Backend;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use PhpOffice\PhpWord\IOFactory;
use think\Queue;
use app\common\service\ai\AiService;

/**
 * 文件管理
 *
 * @icon fa fa-circle-o
 */
class ConsoleFile extends Backend
{

    /**
     * ConsoleFile模型对象
     * @var \app\admin\model\ai\ConsoleFile
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\ConsoleFile;

        $this->dataLimit = true;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $whereNew = [];
            if($this->request->get('ids'))
            {
                $whereNew['console_id'] = $this->request->get('ids');
            }
            $list = $this->model
                    ->with(['console'])
                    ->where($whereNew)
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);
            $sync = true;
            foreach ($list as $row) {
                $row->visible(['id','name','createtime','display_status','word_count']);
                $row->visible(['console']);
				$row->getRelation('console')->visible(['name']);
                /** 状态为-处理中 去百度同步 */
                // if($row['display_status']=='1'  && $sync){
                //     $this->model->sync(['id'=>$row['console_id']]);
                //     $sync = false;
                // }
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $Console = new \app\admin\model\ai\Console;
        $fileList = explode(',', $params['file']);
        $console = $Console::where('id', $params['console_id'])->find();
        $result = true;
        Db::startTrans();
        try {
            $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->find();
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            foreach($fileList as $item)
            {
                $params['console_id'] = $params['console_id'];
                $params['file_number'] = '';
                $file1 = $item;
                $filename = \app\common\model\Attachment::where('url', $item)->value('filename');
                $params['name'] = $filename;
                $params['display_status'] = 0;
                $params['file'] = $item;
                $params['user_id'] = $this->auth->id;
                $suffix = strtolower(pathinfo($file1, PATHINFO_EXTENSION));
                switch ($suffix) {
                    case 'docx':
                    case 'doc':
                        $type = 1;
                        break;
                    case 'pdf':
                        $type = 2;
                        break;
                    case 'txt':
                        $type = 3;
                        break;
                    default:
                        $type = 0;
                        break;
                }
                $params['type'] = $type;
                $params['createtime'] = time();
                $params['updatetime'] = time();
                $params['file_size'] = filesize(ROOT_PATH."public" . $file1);
                $params['file_md5'] = md5_file(ROOT_PATH . "public" . $file1);
                $console_file_id = $this->model->allowField(true)->insertGetId($params);
                // 推入训练队列中
                $file2 = cdnurl($file1, true);
                Queue::push('app\queue\job\UploadFileToConsole', [
                    'url' => $file1,
                    'console_number' => $console['console_number'],
                    'console_file_id' => $console_file_id,
                    'filename' => $params['name'],
                    'engine_type' => $console['type'] ?? 'baidu',
                    'user_id' => 1
                ], config('task.file'));

                // 推入知识图谱生成队列
                Queue::push('app\queue\job\BuildKnowledgeGraph', [
                    'file_path' => ROOT_PATH . "public" . $file1,
                    'console_file_id' => $console_file_id
                ], 'ai_file_buildgraph');

                $aiEngine = AiService::getEngine($console['type']);
                $aiEngine->vectorFileName($params['console_id'],$console_file_id,$params['name']);
            }
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            
            $console = Db::name('ai_console')->where('id',$row['console_id'])->find();
            $aiEngine = AiService::getEngine($console['type']);
            $aiEngine->vectorFileName($row['console_id'],$ids,$params['name']);

            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            $Console = new \app\admin\model\ai\Console;
            foreach ($list as $item) {
                $console_number = $Console->where('id',$item['console_id'])->value('console_number');
                $python = '/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/console_dataset.py "%s" 3 "%s"';
                $return = exec(sprintf($python,$console_number,$item['file_number']));
                // dump(sprintf($python,$console_number,$item['file_number']));exit;

                $count += $item->delete();
                \app\admin\model\ai\ConsoleFileLog::insert(['user_id'=> 1, 'action'=>3, 'filename'=> $item['name'], 'createtime' => time(), 'updatetime' => time()]);
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }

    /**
     * 复制
     */
    public function copy(){
        $ids = $this->request->post("ids");
        $target_id = $this->request->post("target_id");
        if($target_id){
            
        }
        return $this->view->fetch();
    }

    /**
     * test
     */
    public function copy_test(){
        $source_id = 28;
        $target_id = 2;
        $list = $this->model->where('console_id', $target_id)->select();
        $insert = [];
        $user_id = 21;
        foreach($list as $item){
            $insert[] = [
                'console_id' => $source_id,
                'file_number' => '',
                'name' => $item['name'],
                'createtime' => time(),
                'updatetime' => time(),
                'display_status' => 0,
                'word_count' => $item['word_count'],
                'admin_id' => 1,
                'file' => $item['file'],
                'user_id' => $user_id,
                'type' => $item['type'],
                'file_size' => $item['file_size']
            ];
        }
        if($insert)
        {
            $this->model->insertAll($insert);
        }
        $this->success();
    }
}
