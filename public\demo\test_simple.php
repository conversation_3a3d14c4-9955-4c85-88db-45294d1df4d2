<?php
/**
 * 简单测试 - 直接包含文件
 */

// 模拟 ThinkPHP 环境
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', __DIR__);
}

// 模拟一些必要的函数和类
if (!function_exists('cdnurl')) {
    function cdnurl($url, $domain = false) {
        return $domain ? 'https://example.com/' . $url : $url;
    }
}

if (!class_exists('Db')) {
    class Db {
        public static function name($table) {
            return new self();
        }
        public function where($field, $value) {
            return $this;
        }
        public function find() {
            return ['id' => 1, 'name' => 'test', 'console_ids' => '1,2,3'];
        }
        public function insertGetId($data) {
            return 1;
        }
        public function update($data) {
            return true;
        }
    }
}

if (!function_exists('request')) {
    function request() {
        return new class {
            public function ip() { return '127.0.0.1'; }
            public function domain() { return 'http://localhost'; }
        };
    }
}

// 模拟其他必要的类
class IpService {
    public static function getArea($ip) {
        return '本地';
    }
}

class VisionModel {
    public function analyze($image, $msg) {
        return ['content' => '这是一个测试响应'];
    }
}

class BaiduSearchService {
    public function search($query, $options = []) {
        return [
            'references' => [
                ['title' => '测试标题', 'content' => '测试内容', 'url' => 'http://test.com']
            ]
        ];
    }
}

// BaseLocal 类将从真实文件中加载

try {
    echo "开始简单测试...\n\n";

    // 包含基础引擎文件
    include_once 'application/common/service/ai/BaseEngine.php';
    include_once 'application/common/service/ai/engine/BaseLocal.php';

    // 直接包含策略接口
    include_once 'application/common/service/ai/engine/strategy/StrategyInterface.php';
    
    // 包含策略类
    include_once 'application/common/service/ai/engine/strategy/BasicStrategy.php';
    include_once 'application/common/service/ai/engine/strategy/EnhanceStrategy.php';
    include_once 'application/common/service/ai/engine/strategy/ProfessionalStrategy.php';
    
    // 包含主引擎类
    include_once 'application/common/service/ai/engine/LocalEngine.php';
    
    // 包含工厂类
    include_once 'application/common/service/ai/engine/LocalEngineFactory.php';
    
    echo "✓ 所有文件加载成功\n\n";
    
    // 测试创建引擎
    echo "测试创建基础引擎...\n";
    $basicEngine = \app\common\service\ai\engine\LocalEngineFactory::create('basic');
    echo "✓ 基础引擎创建成功\n\n";
    
    echo "测试创建增强引擎...\n";
    $enhanceEngine = \app\common\service\ai\engine\LocalEngineFactory::create('enhance');
    echo "✓ 增强引擎创建成功\n\n";
    
    echo "测试创建专业引擎...\n";
    $professionalEngine = \app\common\service\ai\engine\LocalEngineFactory::create('professional');
    echo "✓ 专业引擎创建成功\n\n";
    
    echo "🎉 所有测试通过！修复成功！\n";
    echo "现在可以在你的 ThinkPHP 应用中正常使用新的整合架构了。\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "❌ 致命错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
?>
