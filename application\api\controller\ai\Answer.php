<?php
namespace app\api\controller\ai;

use app\common\controller\Api;
use think\Db;
use PHPExcel;
use PHPExcel_IOFactory;
use app\common\service\IpService;
use app\common\service\Convert;
use app\common\service\TagExtraction;
use app\common\service\ai\AiService;




class Answer extends Api
{
	// 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];

	protected $lang = 'cn';

	/** 读取问题列表 */
	function question_list() {
		$robot = input('robot');
		$this->lang = input('lang');
		$return = [];
	
		// 获取初始问题列表
		$return['question_list'] = $this->getInitialQuestionList($robot);
	
		// 获取项目信息及统计
		$project = $this->getProjectInfo($robot);
		$wordCount = $this->getWordCount($project);
		$memoryCount = $this->getMemoryCount($project);
		$groupArr1 = $this->getGroupData($project);
		$memoryCount = $this->calculateTotalMemory($memoryCount, $groupArr1, $project);
	
		// 格式化总内存大小
		$size = $this->getformatSize($memoryCount);
		$return['page'] = $this->getPageInfo($wordCount, $size, $robot);
	
		// 获取问答数量信息
		$numText = $this->getQuestionCount($robot);
		$return['question'] = ['num' => $numText, 'question' => '当前最热门的问题是什么？'];
	
		// 记录日志
		$this->logUserActivity($robot);
	
		
		//工具栏信息
		$return['toolbar_list'] = $this->getToolbarList($robot);
		
		// 其他配置信息
		$return = $this->addConfigurations($return, $project);
	
		// 处理多语言翻译
		if ($this->lang && $this->lang != 'cn') {
			$return = $this->translateContent($return, $robot);
		}

		// 获取高频问题排行
		$return['top_questionlist'] = $this->getTopQuestions($robot);
	
		// 获取虚拟人和发音人信息
		$return['assistant'] = $this->getAssistantName($project);
		$voice = $this->getVoiceInfo($project);
		$return = array_merge($return, $voice);
	
		// 获取顶部工具栏信息
		$return['top_tool'] = $this->getTopToolInfo($project, $return);

		unset($return['top_title'], $return['top_change']);

		$this->success('success', $return);
	}
	
	private function getInitialQuestionList($robot) {
		$questionList = \app\admin\model\ai\Question::where('robot', $robot)->column('question');
		return empty($questionList) ? ['我可以问你什么问题？ ', '你的知识库有哪些？'] : $questionList;
	}
	
	private function getProjectInfo($robot) {
		return Db::name('ai_project')->where('id', $robot)->find();
	}
	
	private function getWordCount($project) {
		return $project ? \app\admin\model\ai\ConsoleFile::where(['console_id' => ['in', explode(',', $project['console_ids'])]])->sum('word_count') : 0;
	}
	
	private function getMemoryCount($project) {
		return $project ? \app\admin\model\ai\ConsoleFile::where(['console_id' => ['in', explode(',', $project['console_ids'])]])->sum('file_size') : 0;
	}
	
	private function getGroupData($project) {
		$groupList1 = collection(\app\admin\model\ai\UserCard::where(['console_id' => ['in', explode(',', $project['console_ids'])]])
			->field('type, count(1) num, sum(file_size) file_size, sum(duration) duration')
			->group('type')
			->select())
			->toArray();
		$groupArr1 = [];
		foreach ($groupList1 as $item) {
			$groupArr1[$item['type'] ?? 0] = $item;
		}
		return $groupArr1;
	}
	
	private function calculateTotalMemory($memoryCount, $groupArr1, $project) {
		for ($i = 1; $i < 4; $i++) {
			$fileSizeFrist = 0;
			if ($i == 1) {
				$masterUploadImageNum = \app\admin\model\ai\Card::where(['console_id' => ['in', explode(',', $project['console_ids'])], 'a' => ['not like', "%mp4%"]])->count();
				$fileSizeFrist = $masterUploadImageNum * 55600;
			} elseif ($i == 2) {
				$masterUploadVideoNum = \app\admin\model\ai\Card::where(['console_id' => ['in', explode(',', $project['console_ids'])], 'a' => ['like', "%mp4%"]])->count();
				$fileSizeFrist = $masterUploadVideoNum * 55600000;
			}
			$memoryCount += $fileSizeFrist + ($groupArr1[$i]['file_size'] ?? 0);
		}
		return $memoryCount;
	}
	
	private function getformatSize($memoryCount) {
		return $memoryCount ? format_bytes($memoryCount) : '0B';
	}
	
	private function getPageInfo($wordCount, $size, $robot) {
		$page = ceil($wordCount / 500);
		if ($robot == 2) {
			$size = '5.2G';
			$page *= 20;
		} elseif ($robot == 18) {
			$size = '5.6G';
			$page *= 100;
		} elseif ($robot == 37) {
			$size = '106G';
			$page = 400 * 100;
		}
		if($this->lang && $this->lang!='cn')
		{
			$return = ['num' => $page . "页", 'size' => $size, 'question' => '你的知识库有哪些？'];
		}else{
			$return = ['num' => $page . "页文档", 'size' => $size . "知识库", 'question' => '你的知识库有哪些？'];
		}
		return $return;
	}
	
	private function getQuestionCount($robot) {
		$num = \app\admin\model\ai\Msg::where(['robot' => $robot])->count();
		if($this->lang && $this->lang!='cn')
		{
			$return = $num . "次";
		}else{
			$return = $num . "次问答";
		}
		return $return;
	}
	
	private function logUserActivity($robot) {
		$ip = request()->ip();
		$area = IpService::getArea($ip);
		$userAgent = request()->header()['user-agent'] ?? 0;
		$referrer = $_SERVER['HTTP_REFERER'] ?? '';
		$source = request()->isMobile($userAgent) ? 2 : 1;
		\app\admin\model\ai\Logs::insert([
			'robot' => $robot,
			'ip' => $ip,
			'area' => $area,
			'user_agent' => $userAgent,
			'referrer' => $referrer,
			'source' => $source,
			'createtime' => time()
		]);
	}
	
	private function getAssistantName($project) {
		return \app\admin\model\ai\Assistant::where('id', $project['assistant'])->value('assistant_name') ?? 'yaoboshi';
	}
	
	private function getVoiceInfo($project) {
		$voiceList = \app\admin\model\ai\Voice::where('id', $project['voice'])->find();
		$voicePath = !empty($voiceList['voice_path']) ? urldecode($voiceList['voice_path']) : '';
		return [
			'voice' => $voiceList['voice_name'] ?? 'aisjiuxu',
			'voice_type' => $voiceList['type'] ?? 1,
			'voice_path' => str_replace("text=您好，欢迎使用AI伴学知识库管理平台!&", "", $voicePath)
		];
	}
	
	private function getToolbarList($robot) {
		$toolbarList = \app\admin\model\ai\Toolbarlist::where('robot', $robot)->order('sort','desc')->select();

		foreach($toolbarList as &$tval){
			$tval['icon'] = cdnurl($tval['icon_image'], true);
			$tval['type'] = $tval['type'] == 1 ? 'ask' : ($tval['type'] == 2 ? 'medicine_identification' : $tval['type']);
		}


		return $toolbarList;
	}
	
	private function getTopQuestions($robot) {
		$questionListArr = collection(\app\admin\model\ai\Tagextraction::where(['robot' => $robot, 'msg_id' => ['<>', ''], 'name' => ['not like', '%问%']])
			->order('num', 'desc')
			->limit(100)
			->select())
			->toArray();
		$topQuestionData = $topQuestionList = [];
		$num1 = 0;
		foreach ($questionListArr as $key => $item) {
			if (in_array($item['msg_id'], $topQuestionData)) {
				continue;
			}
			$topQuestionData[] = $item['msg_id'];
			$msg = Db::name('ai_msg')->where(['robot' => $robot, 'id' => $item['msg_id'], 'lang'=> $this->lang])->find();
			if (empty($msg)) {
				continue;
			}
			$msg['content'] = str_ireplace("autoplay", "", $msg['content']);
			if (count($topQuestionList) >= 20) {
				break;
			}
			$num1++;
			$refererList = $msg['referrer'] ? json_decode(base64_decode($msg['referrer']), true) : [
				[
					'title' => '内部知识库.xlsx',
					'content' => $msg['content'],
				]
			];
			$type = (stripos($msg['content'], '.mp4') || stripos($msg['content'], '.jpg') || stripos($msg['content'], '.png')) ? '多模态' : '纯文本';
			$topQuestionList[] = [
				'no' => $num1,
				'name' => $msg['msg'],
				'num' => $item['num'],
				'content' => $msg['content'],
				'type' => $type,
				'referer' => $refererList,
				'show_name' => $msg['msg'],
				'question_name' => $msg['msg'],
			];
		}
		return $topQuestionList;
	}
	
	private function addConfigurations($return, $project) {
		$return['lang_list'] = ['cn' => "简体中文", "en" => "English"];
		$return['input_placeholder'] = '请输入你想要问的问题……';
		$return['source'] = '来源';
		$return['source_answer'] = '该回答来源于内部知识库.';
		$return['btn_text'] = '按住说话';
		$return['loading_text'] = '语音识别中...';
		$return['more_text'] = '更多功能';
		$return['hot_text'] = '热门问题';
		$return['toolbar_more'] = '更多问题';
		$return['top_title'] = '你可以这样问我';
		$return['top_change'] = '换一换';
		$return['more_list'] = [
			'more' => '更多',
			'share' => '分享',
			'lang' => '多语言切换',
			'style' => '样式切换',
			'fontsize' => '字号大小',
			'fontsize_big' => '大',
			'fontsize_small' => '小',
			'link_share' => '链接分享',
			'copy' => '复制',
			'qrcode' => '二维码分享',
			'download' => '下载',
			'tips' => '以链接被打开的次数计算浏览量，获取更多浏览授权联系我们',
			'email' => '<EMAIL>',
			'share_title' => '智能问答分享'
		];

		$return['chatId'] = '';
		$return['chat_name'] = $project['assistant_name']?$project['assistant_name']:"AI小智";
		$return['chat_name_en'] = $project['assistant_name_en']?$project['assistant_name_en']:"AI xiaozhi";
		$return['welcome'] = $project['welcome']?$project['welcome']:"你好，我是{$return['chat_name']}，有什么可以帮助你的吗？";
		$return['lang_type'] = $project['lang_type']?$project['lang_type']:0;
		$return['open_video'] = $project['open_video']? cdnurl($project['open_video'], true):'';
		$return['pc_open_video'] = $project['pc_open_video']? cdnurl($project['pc_open_video'], true):'';
		$return['openvideo_type'] = $project['openvideo_type'] ?? 0;
		$return['openvideo_welcome'] = !empty($project['openvideo_welcome']) ? $project['openvideo_welcome'] : '欢迎使用AI问答机器人，请问您有什么需要帮助的？';

		return $return;
	}
	
	private function translateContent($return, $robot) {
		$fileKey = md5($robot . "_" . $this->lang);
		if (\think\Cache::tag('lang_file')->has($fileKey)) {
			return json_decode(\think\Cache::tag('lang_file')->get($fileKey), true);
		}
	
		$translation = new \app\common\library\Translation();
		/** 静态输出缓存 */
		$fileKey = md5($robot . "_" . $this->lang);
		if (\think\Cache::tag('initialize_data')->has($fileKey)) {
			$return =  json_decode(\think\Cache::tag('initialize_data')->get($fileKey), true);
		}else{
			$return['more_list']['lang'] = '语言';
			$return['welcome'] = $translation->xfyun($return['welcome'], 'cn', $this->lang);
			$return['btn_text'] = $translation->xfyun($return['btn_text'], 'cn', $this->lang);
			$return['loading_text'] = $translation->xfyun($return['loading_text'], 'cn', $this->lang);
			$return['more_text'] = $translation->xfyun($return['more_text'], 'cn', $this->lang);
			$return['hot_text'] = $translation->xfyun($return['hot_text'], 'cn', $this->lang);
			$return['input_placeholder'] = $translation->xfyun($return['input_placeholder'], 'cn', $this->lang);
			$return['page']['num'] = $translation->xfyun($return['page']['num'], 'cn', $this->lang);
			$return['page']['size'] = $translation->xfyun($return['page']['size'], 'cn', $this->lang);
			$return['page']['question'] = $translation->xfyun($return['page']['question'], 'cn', $this->lang);
			$return['question']['num'] = $translation->xfyun($return['question']['num'], 'cn', $this->lang);
			$return['question']['question'] = $translation->xfyun($return['question']['question'], 'cn', $this->lang);
			$return['chat_name'] = $return['chat_name_en'] ?? $translation->xfyun($return['chat_name'], 'cn', $this->lang);
			$return['source'] = $translation->xfyun($return['source'], 'cn', $this->lang);
			$return['source_answer'] = $translation->xfyun($return['source_answer'], 'cn', $this->lang);
		
		
			foreach ($return['more_list'] as $qKey => $qItem) {
				$return['more_list'][$qKey] = $translation->xfyun($qItem, 'cn', $this->lang);
			}
		
			foreach ($return['toolbar_list'] as $tKey => $tItem) {
				$return['toolbar_list'][$tKey]['show_name'] = $translation->xfyun($tItem['show_name'], 'cn', $this->lang);
				$return['toolbar_list'][$tKey]['question_name'] = $translation->xfyun($tItem['question_name'], 'cn', $this->lang);
			}
		
			$return['toolbar_more'] = $translation->xfyun($return['toolbar_more'], 'cn', $this->lang);
			$return['top_title'] = $translation->xfyun($return['top_title'], 'cn', $this->lang);
			$return['top_change'] = $translation->xfyun($return['top_change'], 'cn', $this->lang);

			\think\Cache::tag('initialize_data')->set($fileKey, json_encode($return), 86400 * 30);
		}


		/** 动态数据 */
		foreach ($return['question_list'] as $qKey => $qItem) {
			$return['question_list'][$qKey] = $translation->xfyun($qItem, 'cn', $this->lang);
		}
	
		// foreach ($return['top_questionlist'] as $tKey1 => $tItem1) {
		// 	$return['top_questionlist'][$tKey1]['question_name'] = $return['top_questionlist'][$tKey1]['show_name'] = $translation->xfyun($tItem1['name'], 'cn', $this->lang);
		// }
	
		switch ($this->lang) {
			case 'ru':
				$return['voice'] = 'x2_RuRu_Keshu';
				$return['assistant'] = 'xiaoyan';
				break;
			case 'fr':
				$return['voice'] = 'x2_FrRgM_Lisa';
				$return['assistant'] = 'xiaoyan';
				break;
			default:
				// $return['voice'] = 'x4_lingxiaolu_en';
				// $return['assistant'] = 'xiaoyan';
				break;
		}
	
		\think\Cache::tag('lang_file')->set($fileKey, json_encode($return), 86400 * 15);
		return $return;
	}
	
	private function getTopToolInfo($project, $return) {
		$questionList = [];
		foreach($return['top_questionlist'] as $tval){
			$questionList[] = $tval['name'];
		}
		$questionList = array_merge($questionList, $return['question_list']);
		$topToolType = $project['top_tool'] ?? 0;
		$topToolListRand = [];
		if ($questionList) {
			$limit = count($questionList) > 6 ? 6 : count($questionList);
			$topToolListRand = array_rand($questionList, $limit);
		}
		$newTopToolList = [];
		if ($topToolListRand) {
			foreach ($topToolListRand as $randVal) {
				if(!in_array($questionList[$randVal],$newTopToolList)){
					$newTopToolList[] = $questionList[$randVal];
				}
			}
		}
		return [
			'type' => $topToolType,
			'top_title' => $return['top_title'],
			'top_change' => $return['top_change'],
			'list' => $newTopToolList,
		];
	}


	/**
	 * 获取聊天ID
	 */
	function get_chatid(){
		$robot = input('robot');
		
        $project = db::name('ai_project')->where('id',$robot)->field('id,console_key,type')->find();
        $console_key = $project['console_key'];
        if(empty($console_key)){
            $console_key = "8e36c365-f419-488c-8002-9789d4dcbaee";
        }

        try {

			$aiEngine = AiService::getEngine($project['type']);
	        $return['chatId'] = $aiEngine->getChatId($console_key);
        } catch(\Exception $e) {
        	$this->error($e->getMessage());
        }
        
		$this->success('success',$return);
	}

    /**
	 * 获取卡片
	 */
	function get() {

		$robot = input('robot');
		$this->lang = input('lang');
		$msg = input('msg');

		// 处理语言翻译
		// $msg = $this->translateMessage($msg);

		$chatId = input('chatId');
		$project = Db::name('ai_project')->where('id', $robot)->find();
		$matchingScore = $project['matching_score']>0 ? $project['matching_score'] : 0.5;

		// 特殊消息处理
		if (strpos($msg, '你的知识库有哪些') !== false && $robot != 19) {
			$this->handleKnowledgeBaseList($project);
			return;
		}

		if (strpos($msg, '当前最热门的问题是什么') !== false) {
			$this->handleHotQuestion($robot);
			return;
		}

		// 获取答案
		$return = $this->fetchAnswer($project, $msg, $matchingScore);

		// 记录问答
		if (!empty($return['answer'])) {
			$this->logQuestion($robot, $chatId, $msg, $return['answer']);
			$return['references'] = $this->generateReferences($return['answer']);
		}

		/** 翻译 */
		if($this->lang && $this->lang!='cn'){
			if(empty($Translation)){
				$Translation = new \app\common\library\Translation();
			}
			$return['answer'] = $Translation->xfyun($return['answer'], 'cn', $this->lang);
			if(!empty($return['question']))
			{
				foreach($return['question'] as $qkey=>$qval)
				{
					$return['question'][$qkey] = $Translation->xfyun($qval, 'cn', $this->lang);
				}
			}
			if(!empty($return['references'])){
				foreach($return['references'] as $rkey=>$rval){
					$return['references'][$rkey]['title'] = $Translation->xfyun($rval['title'], 'cn', $this->lang);
					$return['references'][$rkey]['content'] = $Translation->xfyun($rval['content'], 'cn', $this->lang);
				}
			}
		}

		// 最终返回
		$this->success('success', $return);
	}

	private function translateMessage($msg) {
		if ($this->lang && $this->lang != 'cn') {
			$translation = new \app\common\library\Translation();
			return $translation->xfyun($msg, $this->lang, 'cn');
		}
		return $msg;
	}

	

	private function handleKnowledgeBaseList($project) {
		$consoleData = $this->getConsoleData($project['console_ids']);
		if (empty($consoleData)) {
			$return['answer'] = '知识库列表为空!';
		} else {
			$title = "**以下是我的知识库列表：** \n";
			$return['answer'] = $title . $this->arrayToMarkdownTable($consoleData);
			$return['content_type'] = 'markdown';
		}
		$return['answer'] = $this->translateMessage($return['answer']);
		$this->success('success', $return);
	}

	private function getConsoleData($consoleIds) {
		return collection(\app\admin\model\ai\ConsoleFile::where(['console_id' => ['in', explode(',', $consoleIds)]])
			->field('name "文档名称", word_count "数据总量"')
			->order('name', 'asc')
			->select())
			->toArray();
	}

	private function handleHotQuestion($robot) {
		$questionNumAll = Db::name('ai_msg')->where(['robot' => $robot])->count();
		$tagRow = \app\admin\model\ai\Tagextraction::where('robot', $robot)->order('num', 'desc')->find();
		if ($tagRow) {
			$question = $tagRow['name'];
			$questionNum = $tagRow['num'];
		} else {
			$res = $this->questionHot($robot);
			$question = $res['q'];
			$questionNum = $res['number'];
		}
		$answer = "当前已进行的问答次数：{$questionNumAll}次，其中最常被问到的问题是：“{$question}”相关的问题，这类似的问题已经被问到了{$questionNum}次";
		$return['answer'] = $this->translateMessage($answer);
		$this->success('success', $return);
	}

	private function fetchAnswer($project, $msg, $matchingScore) {
		$robot = $project['id'];
		$return = [];
		$aiEngine = AiService::getEngine('local');

        $answer = $aiEngine->selectVectorData($robot, 4, $msg, 0.888, $this->lang, $project['console_ids']);
		$return['answer'] = !empty($answer['a']) ? $answer['a'] : '';
		$return['question'] = !empty($answer['question']) ? explode(',', $answer['question']) : '';
		$return['answer'] = Convert::tag($return['answer']);

		if (strpos($return['answer'], 'video') !== false) {
			$return['list'] = [];
		} else {
			$sourceArr = $aiEngine->selectVectorData($robot, 1, $msg, $matchingScore, $this->lang, $project['console_ids']);

			if (!empty($sourceArr)) {
				foreach ($sourceArr as &$val) {
					$val['a'] = $this->formatCard($val['a']);
				}
			}
			$return['list'] = $sourceArr;
		}

		return $return;
	}

	private function processMultimodelList($project, $multimodelRow, $matchingScore, $msg) {
		$lang = $this->lang=='en' ? 'en' : 'zh';
		/** 未配置英文卡片，转换为中文匹配卡片 */
		$cardNumEn = \app\admin\model\ai\Card::where(['console_id' => ['in', explode(',', $project['console_ids'])], 'lang'=> 'en'])->count();
		if($this->lang=='en' && $cardNumEn<1){
			$msg = $this->translateMessage($msg);
			$multimodelRow = $this->getEmbeddingsNew([$msg]);
			$multimodelList = \app\admin\model\ai\Card::where(['console_id' => ['in', explode(',', $project['console_ids'])]])->select();
		}else{
			$multimodelList = \app\admin\model\ai\Card::where(['console_id' => ['in', explode(',', $project['console_ids'])], 'lang'=> $lang])->select();
		}

		// dump(\app\admin\model\ai\Card::getLastSql());exit;
		$sourceArr = [];
		$oldArr = [];

		foreach ($multimodelList as &$val) {
			$vectorizationArr = json_decode(base64_decode($val['vectorization']), true);
			$cosine = $this->cosineSimilarity($multimodelRow[0]['embedding'], $vectorizationArr['embedding']);
			$val['score'] = $cosine * 1000;
			// if($this->lang=='en' && $cardNumEn<1){
			// 	dump($val['score']);
			// 	dump($matchingScore);
			// 	dump('-----------------');
			// }
			if (!in_array(md5($val['a']), $oldArr) && $val['score'] > $matchingScore) {
				unset($val['vectorization']);
				$sourceArr[] = [
					'a' => $val['a'],
					'score' => $val['score']
				];
				$oldArr[] = md5($val['a']);
			}
		}

		return $sourceArr;
	}

	private function processUserCardList($robot, $multimodelRow, $matchingScore) {
		$multimodelListNew = \app\admin\model\ai\UserCard::with(['usercardext'])->where(['robot' => $robot, 'status' => 1])->select();

		foreach ($multimodelListNew as $row) {
			$row->visible(['id', 'cover', 'file', 'name']);
			$row->visible(['usercardext']);
			$row->getRelation('usercardext')->visible(['name', 'vectorization']);
		}

		$sourceArr = [];
		$oldArr = [];

		foreach ($multimodelListNew as &$val) {
			$vectorizationArr = json_decode(base64_decode($val['usercardext']['vectorization']), true);
			
			// if(empty($multimodelRow) || empty($vectorizationArr))
			// {
			// 	dump($multimodelRow);
			// 	dump($val);exit;
			// }
			if(!empty($multimodelRow) && !empty($vectorizationArr))
			{
				$cosine = $this->cosineSimilarity($multimodelRow[0]['embedding'], $vectorizationArr['embedding']);
				$val['score'] = $cosine * 1000;
				$val['a'] = "[![{$val['name']}]({$val['cover']})]({$val['file']})";
				if (!in_array(md5($val['file']), $oldArr) && $val['score'] > $matchingScore) {
					$sourceArr[] = [
						'a' => $val['a'],
						'score' => $val['score']
					];
					$oldArr[] = md5($val['file']);
				}
			}
		}

		return $sourceArr;
	}

	private function processVrhotList($robot, $multimodelRow, $matchingScore) {
		$vrhotList = \app\admin\model\ai\Vrhot::where(['robot' => $robot])->select();

		foreach ($vrhotList as $row) {
			$row->visible(['id', 'tag', 'image', 'url', 'vectorization']);
		}

		$sourceArr = [];
		$oldArr = [];

		foreach ($vrhotList as &$val) {
			$vectorizationArr = json_decode(base64_decode($val['vectorization']), true);
			$cosine = $this->cosineSimilarity($multimodelRow[0]['embedding'], $vectorizationArr['embedding']);
			$val['score'] = $cosine * 1000;
			$image = $val['image'] ? $val['image'] : 'https://aimaster.jw100.com.cn/assets/img/pdf_image.jpg';
			$val['a'] = "[![{$val['tag']}]({$image})]({$val['url']})";
			if (!in_array(md5($val['url']), $oldArr) && $val['score'] > $matchingScore) {
				$sourceArr[] = [
					'a' => $val['a'],
					'score' => $val['score'] + 100
				];
				$oldArr[] = md5($val['url']);
			}
		}

		return $sourceArr;
	}

	private function formatCard($card) {

		if(strpos($card,'.html')!==false || strpos($card,'.pdf')!==false || strpos($card,'deviceModel')!==false || strpos($card,'tour/')!==false || strpos($card,'(nw)')!==false)
		{
			$result = preg_replace_callback('/\[\!\[(.*?)\]\((.*?)\)\]\((.*?)\)(?:\((nw)\))?/', function($matches) {
				if(stripos($matches[2],'.mp4')!==false){
					return "<div class=\"card videoEle htmlEle\" onclick=\"openWindow('{$matches[1]}','{$matches[3]}')\"><div class=\"card-pic\"><video src=\"{$matches[2]}\" style=\"width: 100%;height: 100%;\"></div><div class=\"desc\">{$matches[1]}</div></div>";
				}else{
					if(strpos($matches[3],'.pdf')!==false){
						$file_path = 'https://vrmice.jw100.com.cn/AIynnz/channels/pdf_show.html?title='.urlencode($matches[1]).'&file='.urlencode($matches[3]);
					}else{
						$file_path = $matches[3];
					}

					//新窗口打开
					if (isset($matches[4]) && $matches[4] === 'nw') {
						return "<div class=\"card videoEle htmlEle\" onclick=\"openWindow('{$matches[1]}','{$file_path}',1)\"><div class=\"card-pic\"><img src=\"{$matches[2]}\"></div><div class=\"desc\">{$matches[1]}</div></div>";
					} else {
						return "<div class=\"card videoEle htmlEle\" onclick=\"openWindow('{$matches[1]}','{$file_path}')\"><div class=\"card-pic\"><img src=\"{$matches[2]}\"></div><div class=\"desc\">{$matches[1]}</div></div>";
					}
				}
			}, $card);
		}else if(strpos($card,'mp4')!==false)
		{
			$result = preg_replace_callback('/\[\!\[(.*?)\]\((.*?)\)\]\((.*?)\)/', function($matches) {
				$thum = !empty($matches[2]) ? $matches[2] : 'https://aimaster.jw100.com.cn/assets/img/pdf_image.jpg';
				return "<div class=\"card videoEle\" onclick=\"openVideo('{$matches[1]}','{$matches[3]}')\"><div class=\"card-pic\"><img src=\"{$thum}\"></div><div class=\"desc\">{$matches[1]}</div></div>";
			}, $card);
		}else{
			$result = preg_replace_callback('/\[\!\[(.*?)\]\((.*?)\)\]\((.*?)\)/', function($matches) {
				return "<div class=\"card videoEle imgEle\" onclick=\"showImage('{$matches[1]}','{$matches[2]}')\"><div class=\"card-pic\"><img src=\"{$matches[2]}\"></div><div class=\"desc\">{$matches[1]}</div></div>";
			}, $card);
		}
		return $result;

		
		return preg_replace_callback('/\[\!\[(.*?)\]\((.*?)\)\]\((.*?)\)/', function ($matches) {
			if (stripos($matches[2], '.mp4') !== false) {
				return "<div class=\"card videoEle htmlEle\" onclick=\"openWindow('{$matches[1]}','{$matches[3]}')\"><div class=\"card-pic\"><video src=\"{$matches[2]}\" style=\"width: 100%;height: 100%;\"></div><div class=\"desc\">{$matches[1]}</div></div>";
			} else {
				if (strpos($matches[3], '.pdf') !== false) {
					$filePath = 'https://vrmice.jw100.com.cn/AIynnz/channels/pdf_show.html?title=' . urlencode($matches[1]) . '&file=' . urlencode($matches[3]);
				} else {
					$filePath = $matches[3];
				}
				return "<div class=\"card videoEle htmlEle\" onclick=\"openWindow('{$matches[1]}','{$filePath}')\"><div class=\"card-pic\"><img src=\"{$matches[2]}\"></div><div class=\"desc\">{$matches[1]}</div></div>";
			}
		}, $card);
	}

	private function logQuestion($robot, $chatId, $msg, $answer) {
		$ip = request()->ip();
		$area = IpService::getArea($ip);
		$card = !empty($return['list']) ? 1 : 0;
		$answer = mb_substr($answer, 0, 5000);
		$msgId = Db::name('ai_msg')->insertGetId([
			'robot' => $robot,
			'chatId' => $chatId,
			'msg' => $msg,
			'time' => time(),
			'ip' => $ip,
			'content' => $answer,
			'city' => $area,
			'card' => $card,
			'qa' => 1,
			'lang' => $this->lang
		]);
		$result = [
			'msg' => $msg,
			'robot' => $robot,
			'msg_id' => $msgId
		];
		\fast\Http::sendAsyncRequest(request()->domain() . '/api/ai/card/tagExtractionOne', $result);
	}

	private function generateReferences($answer) {
		return [
			[
				'title' => '内部知识库.xlsx',
				'content' => $answer
			]
		];
	}

	/** 数组转markdown */
	function arrayToMarkdownTable($array) {
		$table = "|";
		// 获取所有键名
		$keys = array_keys(current($array));
		// 添加列标题
		foreach ($keys as $key) {
			$table .= " $key |";
		}
		$table .= "\n";
		// 添加分隔行
		$table .= "|";
		foreach ($keys as $key) {
			$table .= "-----|";
		}
		$table .= "\n";
		// 添加数据行
		foreach ($array as $row) {
			foreach ($keys as $key) {
				$table .= " $row[$key] |";
			}
			$table .= "\n";
		}
		return $table;
	}

	/**
	 * 获取最常问题
	 */
	function questionHot($robot){
		switch ($robot) {
			case 11:
				$tags = ['气相色谱','液相色谱','原子吸收'];
				break;
			case 16:
				$tags = ['细胞实验'];
				break;
			case 3:
				$tags = ['流体力学实验操作'];
				break;
			case 5:
				$tags = ['压片机的结构'];
				break;
			case 19:
				$tags = ['拜耳法'];
				break;
			case 2:
				$tags = ['人参的功效是什么'];
				break;
			default:
				$tags = ['气相色谱','液相色谱','原子吸收'];
				break;
		}
		$result = [];
		foreach($tags as $item){
			$questionNum = Db::name('ai_msg')->where(['robot'=>$robot,'msg'=>['like',"%{$item}%"]])->count();
			$questionNum += 10;
			$result[] = ['q'=>$item,'number'=>$questionNum];
		}
		usort($result, function($a, $b) {
			return $b['number'] - $a['number'];
		});
		return $result[0];
	}

	/** 调用向量库UNIT */
	function getEmbeddingsUnit($robot,$msg){
		$return = [];
		// $multimodelRow = $this->getEmbeddingsNew([$msg]);
		// $multimodelList = \app\admin\model\ai\Multimodel::where(['robot'=>['in',[$robot]]])->select();
		$answerHtml = '';
		// $oldArr = $sourceArr = [];
		// foreach($multimodelList as $key=>&$val){
		// 	$vectorizationArr = json_decode(base64_decode($val['vectorization']),true);
		// 	$cosine = $this->cosineSimilarity($multimodelRow[0]['embedding'],$vectorizationArr['embedding']);
		// 	$val['score'] = $cosine*1000;
		// 	if($val['score']>500)
		// 	{
		// 	    // error_log(date("Y-m-d H:i:s")."question:".$msg."|val:".$val['q']."|cosine:".$cosine,3,ROOT_PATH."/runtime/log/".date("Ym")."/embedding.log");
		// 	}
		// 	if(!in_array(md5($val['a']),$oldArr) && $val['score']>888)
		// 	{
		// 		unset($val['vectorization']);
		// 		$sourceArr[] = $val;
		// 		$oldArr[] = md5($val['a']);
		// 	}
		// }
		// usort($sourceArr, function($a, $b) {
		// 	return $b['score'] - $a['score'];
		// });
		$context = $question = '';
		/** 优化格式 */
		// if(!empty($sourceArr))
		// {
		// 	foreach($sourceArr as $key=>&$val)
		// 	{
		// 		$context = $val['a'];
		// 		$question = $val['question'];
		// 		break;
		// 	}
		// }
		$aiEngine = AiService::getEngine('local');

        $result = $aiEngine->selectVectorData($robot,4,$msg, 0.888, $this->lang);
		if($result){
			$context = $result['answer'];
			$question = $result['question'];
		}
		
		$context = Convert::tag($context);
		$return['answer'] = $context;
		$return['question'] = $question;
		return $return;
	}

	/** 标签转换 */
	function convert() {
		$msg = input('msg');
		$return = convert::tag($msg);
		$this->success('success',$return);
	}
	
	/** 
	 * 调用生成向量数组匹配对应关系
	 */
	function getEmbeddingsNew($arr){
		
		$input = json_encode(['input'=>$arr],JSON_UNESCAPED_UNICODE);

		$embeddings = new \app\common\library\Embeddings;
		$embeddingsStr = $embeddings->run($input);
		$embeddingsArr = json_decode($embeddingsStr,true);

		return $embeddingsArr['data'] ? $embeddingsArr['data'] : [];
	}

	/**
	 * 计算余弦相似度
	 */
	function cosineSimilarity($vector1, $vector2) {  
		$dotProduct = 0;  
		$magnitude1 = 0;  
		$magnitude2 = 0;  
		$count = count($vector1);  
	
		for ($i = 0; $i < $count; $i++) {  
			$dotProduct += $vector1[$i] * $vector2[$i];  
			$magnitude1 += pow($vector1[$i], 2);  
			$magnitude2 += pow($vector2[$i], 2);  
		}  
	
		$magnitude1 = sqrt($magnitude1);  
		$magnitude2 = sqrt($magnitude2);  
		return $dotProduct / ($magnitude1 * $magnitude2);  
	}

	/**
	 * 获取追问
	 */
	function get_question() {

		$robot = input('robot');
		$this->lang = input('lang');

		$msg = input('msg');
		$return = [];

        $aiEngine = AiService::getEngine('local');
        $return = $aiEngine->similar_question($msg);
		
		/** 翻译 */
		if($this->lang && $this->lang!='cn'){
			$Translation = new \app\common\library\Translation();
			if(!empty($return))
			{
				foreach($return as $qkey=>$qval)
				{
					$return[$qkey] = $Translation->xfyun($qval, 'cn', $this->lang);
				}
			}
		}

		$this->success('success',$return);
	}
	/** 调用FastGPT追问 */
	function getFastGptQuestion($robot,$msg){
		$apiUrl = 'https://share.fastgpt.in/api/core/ai/agent/createQuestionGuide';
		switch ($robot) {
			case '11':
			case '16':
				$apiKey = 'fastgpt-rk96CZ0W5mhXYc6vj23uLAVDsOXjx8OPDM7O';
				break;
			case '5':
				$apiKey = 'fastgpt-2GxtvBSekDS7w4MdCoiWAnByYQ';
				break;
				
			case '2':
				$apiKey = 'fastgpt-eTiyHB9xqWN5B3w33puTt1UxafrQLChg';
				break;
			case '3':
			case '7':
				$apiKey = 'fastgpt-KB4fVetStwyQhT56TwOOVyEIjdNfR0O';
				break;
			case '4':
				$apiKey = 'fastgpt-FDJWIY4mKsOh655KPAp4SIu6';
				$msg .= " 请用英文回复";
				break;
			case '6':
				$apiKey = 'fastgpt-a2zQc3FJSSrTJBbCRk0a81LUoks';
				break;
			case '9':
				$apiKey = 'fastgpt-ISKpfTE5TM0gQFCLbpAS6V2mSw';
				break;
			case '10':
				$apiKey = 'fastgpt-UIQG9nU9EdrwhXyqF6webLRojyzIh56vK';
				break;
			case '12':
				$apiKey = 'fastgpt-2Nz9vyrOI4LhG9v3lnJkwetrnDCRa';
				break;
			case '13':
				$apiKey = 'fastgpt-qZNWUNKGJcmVQLZhaeVLf5Cjb2W';
				$msg .= " 请用英文回复";
				break;
			case '14':
				$apiKey = 'fastgpt-DvuC9pMlVVDEiVmQbQbgdonwWbITlLWvUsnK';
				break;
			case '15':
				$apiKey = 'fastgpt-7F9bwT16bTyB1h2rPX9MEaGCkpi8gjc1';
				break;
			
			default:
				$apiKey = 'fastgpt-rk96CZ0W5mhXYc6vj23uLAVDsOXjx8OPDM7O';
				break;
		}
		$apiKey = 'fastgpt-3G3hQ6z3B0OG350MloBIbvgdnchTuAWYScoeNmdXFMp4eIBiD67i';
		$chatId = rand(11111,99999);
		$data = [
			"messages" => [
				[
					"content" => $msg,
					"role" => "assistant"
				]
			],
			'outLinkUid'=>'shareChat-1714300659349-DNgVzGbIAkc7kzWSSiHcts8n',
			'shareId'=>'xdsgb5ule9eh8x8rg1bw6aja'
		];
		
		$headers = [
			'Authorization: Bearer ' . $apiKey,
			'Content-Type: application/json'
		];
		
		$options = [
			'http' => [
				'header' => implode("\r\n", $headers),
				'method' => 'POST',
				'content' => json_encode($data),
				'timeout' => 30
			]
		];
		
		$context = stream_context_create($options);
		$result = @file_get_contents($apiUrl, false, $context);
		$list = [];
		if ($result !== false) {
			$data = json_decode($result, true);
			$list = isset($data['data'])?$data['data']:[];
		} else {
			trace(date("Y-m-d H:i:s")."|error:".print_r(error_get_last(),1));
		}
		return $list;
	}

	/** 标签提取 */
	function tagExtraction(){
		ignore_user_abort(true);
		set_time_limit(0);
		$pid = getmypid();
		error_log(date("Y-m-d H:i:s")."pid:".$pid."\r\n",3,ROOT_PATH."/runtime/log/".date("Ym")."/tagExtractionKeep.log");
		$robotList = [11,16,19,2,3,5];
		foreach($robotList as $key)
		{
			error_log(date("Y-m-d H:i:s")."robot:".$key."-------start--------\r\n",3,ROOT_PATH."/runtime/log/".date("Ym")."/tagExtractionKeep.log");
			$where = ['robot'=>$key,'time'=>['>',strtotime('-10minute')],'msg'=>['not in',['我可以问你什么问题？','你的知识库有哪些？']]];
			$list = Db::name('ai_msg')->where($where)->select();
			foreach($list as $val)
			{
				$robot = $val['robot'];
				if(strlen($val['msg'])>5)
				{
					$tagList = TagExtraction::run($val['msg'], $robot, $val['id']);
					error_log(date("Y-m-d H:i:s")."msg:".$val['msg']."\r\n",3,ROOT_PATH."/runtime/log/".date("Ym")."/tagExtractionKeep.log");
				}
			}
			error_log(date("Y-m-d H:i:s")."robot:".$key."-------end--------\r\n",3,ROOT_PATH."/runtime/log/".date("Ym")."/tagExtractionKeep.log");
		}
		$this->success('导入成功');
	}

	/** 标签提取 */
	function tagExtractionOne(){
		$msg = input('msg');
		$robot = input('robot');
		$msg_id = input('msg_id');
		TagExtraction::run($msg, $robot, $msg_id);
		$this->success('导入成功');
	}

	/**
	 * 用ChatID获取问答记录
	 */
	public function getChatIdMsgList(){
		$chat_id = input('chat_id');
		$num = Db::name('ai_msg')->where(['chatId'=>$chat_id])->count();
		$this->success('获取成功',$num);
	}

	/** 换一换 */
	function change_top_question(){
		$robot = input('robot');
		$this->lang = input('lang');
		/** 初始问题 */
		$question_list = \app\admin\model\ai\Question::where('robot',$robot)->column('question');
		if(empty($question_list)){
			$question_list = ['我可以问你什么问题？ ','你的知识库有哪些？'];
		}
		$top_tool_list = $question_list;
		
		$question_listarr = collection(\app\admin\model\ai\Tagextraction::where(['robot'=>$robot,'msg_id'=>['<>',''],'name'=>['not like','%问%']])->order('num','desc')->limit(100)->select())->toArray();
		$top_questiondata = $top_questionlist = [];
		foreach($question_listarr as $key=>$item){
			if(in_array($item['msg_id'],$top_questiondata)){
				continue;
			}
			$top_questiondata[] = $item['msg_id'];
			$msg = Db::name('ai_msg')->where(['robot'=>$robot,'id'=>$item['msg_id'], 'lang'=> $this->lang])->find();
			if(empty($msg)){
				continue;
			}
			if(count($top_questionlist)>=20)break;
			if(!in_array($msg['msg'], $top_tool_list))$top_tool_list[] = $msg['msg'];
		}

		if(count($top_tool_list)>6)
		{
			$top_tool_list_rand = count($top_tool_list)>6 ? array_rand($top_tool_list, 6) : $top_tool_list;
			$new_top_tool_list = [];
			foreach($top_tool_list_rand as $randval){
				$new_top_tool_list[] = $top_tool_list[$randval];
			}
		}else{
			$new_top_tool_list = $top_tool_list;
		}

		/** 翻译 */
		if($this->lang && $this->lang != 'cn'){
			$Translation = new \app\common\library\Translation();
			foreach($new_top_tool_list as $qkey=>$qitem){
				$new_top_tool_list[$qkey] = $Translation->xfyun($qitem, 'cn', $this->lang);
			}
		}

		$this->success('success',$new_top_tool_list);
	}



	/*
	* 添加卡片
	*/
	public function addCard()
    {
        $str = '';

        $postData = json_decode($str,true);
    	// $postData = json_decode(file_get_contents('php://input'),true);
    	if (empty($postData)) {
    		$this->error('参数错误');
    	}
    	try {
    		$insert = [];
	    	foreach ($postData as $v) {

	    		$a = "[![{$v['title']}]({$v['image']})]({$v['link']})(nw)";
	            $code = md5($a);

	            $newData = $this->getEmbeddingsNew([$v['title']]);
	            $vectorization = base64_encode(json_encode($newData[0]));

	            $insert = [
	                'robot' => 18,
	                'code' => $code,
	                'q' => $v['title'],
	                'a' => $a,
	                'vectorization' => $vectorization,
	                'createtime' => time()
	            ];

	            Db::name('ai_card')->insert($insert);
	    	}
	    	return json_encode($insert);

    	} catch (\Exception $e) {
    		$this->error($e->getMessage());
    	}

    }

	/** 清除缓存 */
	public function delete_cache(){
		$md5 = input('md5');
		$fileKey = md5($md5);
		if (\think\Cache::tag('lang_file')->has($fileKey)) {
			$result = \think\Cache::tag('lang_file')->pull($fileKey);
			$this->success('success',$result);
		}
	}

	/** 卡片信息归类到知识库 */
	public function card_to_kb(){
		$list = \app\admin\model\ai\Card::whereNull('console_id')->select();
		$count = 0;
		foreach($list as $item){
			$console_ids = \app\admin\model\ai\Project::where('id',$item['robot'])->value('console_ids');
			$console_arr = explode(',', $console_ids);
			\app\admin\model\ai\Card::where('id',$item['id'])->update(['console_id'=>$console_arr[0]]);
			dump(\app\admin\model\ai\Card::getLastSql());
			$count++;
		}
		$this->success('success',$count);
	}

	public function model_list(){
		$robot = input('robot');
		$this->lang = input('lang');
		$model_id = \app\admin\model\ai\Project::where('id', $robot)->value('model_id');
		$model = \app\admin\model\ai\Bigmodel::where('id', $model_id)->find();
		$result = [];
		$result[] = [
			'model_id' => $model['id'],
			'name' => $model['name'],
			'model' => $model['model'],
		];
		$model1 = \app\admin\model\ai\Bigmodel::where('id', 8)->find();
		$result[] = [
			'model_id' => $model1['id'],
			'name' => $model1['name'],
			'model' => $model1['model'],
		];
		$console_type = [
			1 => '基于知识库',
			2 => '基于全网',
			3 => '转人工'
		];
		if($this->lang && $this->lang != 'cn'){
			$translation = new \app\common\library\Translation();
			/** 静态输出缓存 */
			$fileKey = md5($robot . "_" . $this->lang);
			if (\think\Cache::tag('model_data')->has($fileKey)) {
				$return =  json_decode(\think\Cache::tag('initialize_data')->get($fileKey), true);
			}else{
				foreach($result as $key=>$item){
					$result[$key]['name'] = $translation->xfyun($item['name'], 'cn', $this->lang);
				}
				foreach($console_type as $key=>$item){
					$console_type[$key] = $translation->xfyun($item, 'cn', $this->lang);
				}
				$return = ['model_list'=>$result, 'console_type' => $console_type];
				\think\Cache::tag('initialize_data')->set($fileKey, json_encode($return), 86400 * 30);
			}
		}else{
			$return = ['model_list'=>$result, 'console_type' => $console_type];
		}
		$this->success('success', $return);
	}
}