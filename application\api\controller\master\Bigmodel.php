<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;

/**
 * API-模型管理
 */
class Bigmodel extends Api
{
    // protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     * Bigmodel模型对象
     * @var \app\admin\model\ai\Bigmodel
     */
    protected $model = null;
    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\Bigmodel();
    }
    

    /**
     * 列表
     */
    public function index()
    {
        $limit = 20;
        $sort = 'id';
        $order = 'ASC';
        $where = ['status'=>1];
        if($this->request->post('name')){
            $where['name'] = ['like',"%{$this->request->post('name')}%"];
        }
        $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
        
        $model_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('model_id') ?? 1;

        foreach ($list as $row) {
            $row->visible(['id','name','updatetime','image', 'status', 'permanent_status']);
            $row['updatetime'] = date("Y-m-d H:i",$row['updatetime']);
            $row['image'] = cdnurl($row['image'], true);
            $row['status'] = $model_id==$row['id']?1:0;
        }

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
        
    }
    /**
     * 修改
     */
    public function edit(){
        $id = $this->request->post('id');
        
        $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->find();
        if($project && $project['type']!='local'){
            $this->error("该项目不支持修改模型");
            return;
        }
        
        $result = false;
        Db::startTrans();
        try {
            $params['model_id'] = $id;
            $params['updatetime'] = time();
            $result = \app\admin\model\ai\Project::where('id',$project['id'])->update($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        
        $this->success("修改成功!");
    }
}
