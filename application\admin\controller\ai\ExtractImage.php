<?php

namespace app\admin\controller\ai;

use app\common\controller\Backend;

/**
 * 提取图片管理
 *
 * @icon fa fa-circle-o
 */
class ExtractImage extends Backend
{

    /**
     * ExtractImage模型对象
     * @var \app\admin\model\ai\ExtractImage
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\ExtractImage;

        $this->view->assign("projectList", \app\admin\model\ai\Project::select());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        $ids = $this->request->get('ids');
        $Attachment = model('Attachment');
        $row = $Attachment->get($ids)??[];
        if (!empty($row['imagetype']) && !in_array($row['imagetype'], ['docx'])) {
            $this->error(__('不支持此格式的提取，暂只支持DOCX'));
        }
        $file = $this->model->where('file_id',$ids)->find();
        if($ids && empty($file)){
            $this->extractImages($row, $ids);
        }
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $newWhere = [];
            if($ids)
            {
                $newWhere = ['file_id'=>$ids];
            }
            $list = $this->model
                    ->with(['attachment'])
                    ->where($where)
                    ->where($newWhere)
                    ->order($sort, $order)
                    ->paginate($limit);

                    

            foreach ($list as $row) {
                $row->visible(['id','file','createtime','name']);
                $row->visible(['attachment']);
				$row->getRelation('attachment')->visible(['filename']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }
    
    private function extractImages($row, $ids)
    {
        $python = '/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/read.py "%s" "%s"';
        $outputFolder = '/uploads/png/'.date("Ymd",$row['createtime'])."/".$row['id']."/"; 
        $return = exec(sprintf($python, ROOT_PATH . 'public' . $row['url'], ROOT_PATH . 'public' . $outputFolder));

        $files = array();
        if(!is_dir(ROOT_PATH . 'public' . $outputFolder))return;
        $dh = opendir(ROOT_PATH . 'public' . $outputFolder);
        while (false !== ($file = readdir($dh))) {
            if (!$file || $file[0] == '.') {
                continue;
            }
            if(strrchr($file, '.')=='.png')
            {
                $files[] = $outputFolder.$file;
            }
        }
        closedir($dh);
        if($files){
            $insert = [];
            foreach($files as $item){
                $name = substr($item, strripos($item, '/') + 1);
                $insert[] = [
                    'file_id'=>$ids,
                    'file'=>$item,
                    'name'=>$name,
                    'createtime'=>time(),
                ];
            }
            $this->model->insertAll($insert);
        }
        return $files;
    }

    /**
     * 同步卡片
     */
    public function import_card($ids){
        $robot = $this->request->post('robot');
        $list = $this->model->where(['id'=>['in',explode(',',$ids)]])->select();
        $Card = new \app\admin\model\ai\Card;
        $insert = [];
        foreach ($list as $item) {
            $row = [];
            $row['robot'] = $robot;
            $row['q'] = $item['name'];
            $url = cdnurl($item['file'], true);
            $row['a'] = "[![{$item['name']}]({$url})]({$url})";
            $row['code'] = md5($row['a']);
            $result = $Card->where(['robot'=>$robot,'code'=>$row['code']])->find();
            if($result || empty($row['q']))
            {
                continue;
            }else{
                if ($row) {
                    $insert[] = $row;
                }
            }
        }
        $segmentedArray = array_chunk($insert, 15);
        foreach($segmentedArray as $sval)
        {
            $newData = $data = [];
            foreach($sval as $key=>$item)
            {
                $data[] = $item['q'];
            }
            $newData = $this->getEmbeddingsNew($data);
            foreach($sval as $key1=>&$item1)
            {
                $vectorization = base64_encode(json_encode($newData[$key1]));
                $item1['vectorization'] = $vectorization;
            }
            $Card->saveAll($sval);
        }
        $this->success('导入成功');
    }

	/** 
	 * 调用生成向量数组匹配对应关系
	 */
	function getEmbeddingsNew($arr){
		$input = json_encode(['input'=>$arr]);
		
		$embeddings = new \app\common\library\Embeddings;
		$embeddingsStr = $embeddings->run($input);
		$embeddingsArr = json_decode($embeddingsStr,true);
		return $embeddingsArr['data'];
	}

}
