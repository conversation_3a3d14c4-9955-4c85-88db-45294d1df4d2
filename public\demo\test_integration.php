<?php
/**
 * 整合测试文件
 * 用于验证新架构的正确性
 */

// 模拟ThinkPHP环境（实际使用时不需要这些）
if (!function_exists('cdnurl')) {
    function cdnurl($url, $domain = false) {
        return $domain ? 'https://example.com/' . $url : $url;
    }
}

if (!class_exists('Db')) {
    class Db {
        public static function name($table) {
            return new self();
        }
        public function where($field, $value) {
            return $this;
        }
        public function find() {
            return ['id' => 1, 'name' => 'test'];
        }
    }
}

// 测试类
class IntegrationTest
{
    private $testResults = [];
    
    public function runAllTests()
    {
        echo "开始运行整合测试...\n\n";
        
        $this->testFactoryCreation();
        $this->testStrategyInterface();
        $this->testEngineCreation();
        $this->testStrategySwitching();
        $this->testProjectBasedCreation();
        
        $this->printResults();
    }
    
    /**
     * 测试工厂类创建
     */
    public function testFactoryCreation()
    {
        echo "测试1: 工厂类创建...\n";
        
        try {
            // 测试基础策略创建
            $basicEngine = \app\common\service\ai\engine\LocalEngineFactory::create('basic');
            $this->addResult('基础引擎创建', $basicEngine !== null);
            
            // 测试增强策略创建
            $enhanceEngine = \app\common\service\ai\engine\LocalEngineFactory::create('enhance');
            $this->addResult('增强引擎创建', $enhanceEngine !== null);
            
            // 测试专业策略创建
            $professionalEngine = \app\common\service\ai\engine\LocalEngineFactory::create('professional');
            $this->addResult('专业引擎创建', $professionalEngine !== null);
            
            // 测试默认策略创建
            $defaultEngine = \app\common\service\ai\engine\LocalEngineFactory::create('invalid');
            $this->addResult('默认引擎创建', $defaultEngine !== null);
            
        } catch (Exception $e) {
            $this->addResult('工厂类创建测试', false, $e->getMessage());
        }
        
        echo "测试1完成\n\n";
    }
    
    /**
     * 测试策略接口
     */
    public function testStrategyInterface()
    {
        echo "测试2: 策略接口...\n";
        
        try {
            // 检查策略类是否实现了接口
            $basicStrategy = new \app\common\service\ai\engine\strategy\BasicStrategy();
            $this->addResult('基础策略实现接口', $basicStrategy instanceof \app\common\service\ai\engine\strategy\StrategyInterface);
            
            $enhanceStrategy = new \app\common\service\ai\engine\strategy\EnhanceStrategy();
            $this->addResult('增强策略实现接口', $enhanceStrategy instanceof \app\common\service\ai\engine\strategy\StrategyInterface);
            
            $professionalStrategy = new \app\common\service\ai\engine\strategy\ProfessionalStrategy();
            $this->addResult('专业策略实现接口', $professionalStrategy instanceof \app\common\service\ai\engine\strategy\StrategyInterface);
            
            // 检查必要方法是否存在
            $this->addResult('基础策略有buildReferenceContent方法', method_exists($basicStrategy, 'buildReferenceContent'));
            $this->addResult('基础策略有buildPayload方法', method_exists($basicStrategy, 'buildPayload'));
            $this->addResult('基础策略有setEngine方法', method_exists($basicStrategy, 'setEngine'));
            
        } catch (Exception $e) {
            $this->addResult('策略接口测试', false, $e->getMessage());
        }
        
        echo "测试2完成\n\n";
    }
    
    /**
     * 测试引擎创建
     */
    public function testEngineCreation()
    {
        echo "测试3: 引擎创建...\n";
        
        try {
            // 测试直接创建引擎
            $engine = new \app\common\service\ai\engine\LocalEngine();
            $this->addResult('直接创建引擎', $engine !== null);
            
            // 测试引擎继承
            $this->addResult('引擎继承BaseLocal', $engine instanceof \app\common\service\ai\engine\BaseLocal);
            
            // 测试引擎方法
            $this->addResult('引擎有chat方法', method_exists($engine, 'chat'));
            $this->addResult('引擎有setStrategy方法', method_exists($engine, 'setStrategy'));
            $this->addResult('引擎有cleanupCombinedContent方法', method_exists($engine, 'cleanupCombinedContent'));
            
        } catch (Exception $e) {
            $this->addResult('引擎创建测试', false, $e->getMessage());
        }
        
        echo "测试3完成\n\n";
    }
    
    /**
     * 测试策略切换
     */
    public function testStrategySwitching()
    {
        echo "测试4: 策略切换...\n";
        
        try {
            $engine = new \app\common\service\ai\engine\LocalEngine();
            
            // 测试切换到基础策略
            $engine->setStrategy(\app\common\service\ai\engine\LocalEngine::STRATEGY_BASIC);
            $this->addResult('切换到基础策略', true);
            
            // 测试切换到增强策略
            $engine->setStrategy(\app\common\service\ai\engine\LocalEngine::STRATEGY_ENHANCE);
            $this->addResult('切换到增强策略', true);
            
            // 测试切换到专业策略
            $engine->setStrategy(\app\common\service\ai\engine\LocalEngine::STRATEGY_PROFESSIONAL);
            $this->addResult('切换到专业策略', true);
            
        } catch (Exception $e) {
            $this->addResult('策略切换测试', false, $e->getMessage());
        }
        
        echo "测试4完成\n\n";
    }
    
    /**
     * 测试基于项目的创建
     */
    public function testProjectBasedCreation()
    {
        echo "测试5: 基于项目配置的创建...\n";
        
        try {
            // 测试专业项目配置
            $professionalProject = ['query_augmentation' => 1];
            $engine1 = \app\common\service\ai\engine\LocalEngineFactory::createByProject($professionalProject);
            $this->addResult('专业项目配置创建', $engine1 !== null);
            
            // 测试增强项目配置
            $enhanceProject = ['enable_enhancement' => 1];
            $engine2 = \app\common\service\ai\engine\LocalEngineFactory::createByProject($enhanceProject);
            $this->addResult('增强项目配置创建', $engine2 !== null);
            
            // 测试基础项目配置
            $basicProject = [];
            $engine3 = \app\common\service\ai\engine\LocalEngineFactory::createByProject($basicProject);
            $this->addResult('基础项目配置创建', $engine3 !== null);
            
        } catch (Exception $e) {
            $this->addResult('基于项目配置创建测试', false, $e->getMessage());
        }
        
        echo "测试5完成\n\n";
    }
    
    /**
     * 添加测试结果
     */
    private function addResult($testName, $passed, $error = null)
    {
        $this->testResults[] = [
            'name' => $testName,
            'passed' => $passed,
            'error' => $error
        ];
        
        $status = $passed ? '✓ 通过' : '✗ 失败';
        echo "  {$testName}: {$status}";
        if ($error) {
            echo " (错误: {$error})";
        }
        echo "\n";
    }
    
    /**
     * 打印测试结果
     */
    private function printResults()
    {
        echo "=== 测试结果汇总 ===\n";
        
        $totalTests = count($this->testResults);
        $passedTests = array_filter($this->testResults, function($result) {
            return $result['passed'];
        });
        $passedCount = count($passedTests);
        $failedCount = $totalTests - $passedCount;
        
        echo "总测试数: {$totalTests}\n";
        echo "通过: {$passedCount}\n";
        echo "失败: {$failedCount}\n";
        echo "成功率: " . round(($passedCount / $totalTests) * 100, 2) . "%\n\n";
        
        if ($failedCount > 0) {
            echo "失败的测试:\n";
            foreach ($this->testResults as $result) {
                if (!$result['passed']) {
                    echo "  - {$result['name']}";
                    if ($result['error']) {
                        echo " (错误: {$result['error']})";
                    }
                    echo "\n";
                }
            }
        } else {
            echo "🎉 所有测试都通过了！整合成功！\n";
        }
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new IntegrationTest();
    $test->runAllTests();
} else {
    echo "<pre>";
    $test = new IntegrationTest();
    $test->runAllTests();
    echo "</pre>";
}
?>
