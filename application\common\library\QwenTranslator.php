<?php

namespace app\common\library;

class QwenTranslator {
    private $url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
    private $apiKey = "sk-613ccddd0b0948179c815a0e11cd8ebf";

    public function __construct() {
    }

    /**
     * 翻译中文为英文，支持术语保护
     */
    public function translate($text, $termMap = []) {
        $termArr = [];
        foreach ($termMap as $zh => $en) {
            $termArr[] = [
                "source" => $zh,
                "target" => $en
            ];
        }
        $data = [
            "model" => "qwen-mt-turbo",
            "messages" => [
                [
                    "role" => "user",
                    "content" => $text
                ]
            ],
            "translation_options"=>[
                "source_lang"=>"Chinese",
                "target_lang"=>"English",
                "terms"=> $termArr
            ]
        ];

        $jsonData = json_encode($data);

        $ch = curl_init($this->url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer {$this->apiKey}",
            "Content-Type: application/json"
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode == 200) {
            $result = json_decode($response, true);
            return $result['choices'][0]['message']['content'] ?? '翻译失败';
        } else {
            return "接口错误：HTTP $httpCode - $response";
        }
    }
}
