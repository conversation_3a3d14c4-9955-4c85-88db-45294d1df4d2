<?php

namespace app\common\library\aliyun;

use think\Exception;
use GuzzleHttp\Client;

class TitlePredictor
{
    protected $baseUrl = 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation';
    protected $apiKey;
    protected $model = 'qwen-plus'; // 或 qwen-turbo / qwen-max

    public function __construct($apiKey = null)
    {
        $this->apiKey = $apiKey ?: 'sk-613ccddd0b0948179c815a0e11cd8ebf'; // 从配置文件读取更佳
    }

    /**
     * 根据文章内容生成推荐标题
     *
     * @param string $doc 文章内容
     * @return array|false 返回标题列表或失败返回 false
     * @throws Exception
     */
    public function generateTitles($doc)
    {
        $client = new Client();

        $prompt = <<<EOT
请根据以下文章内容生成一个推荐标题，字数需要在10个字左右，不要编号或特殊格式：

{$doc}
EOT;

        try {
            $response = $client->post($this->baseUrl, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ],
                'json' => [
                    'model' => $this->model,
                    'input' => [
                        'prompt' => $prompt
                    ],
                    'parameters' => [
                        'result_format' => 'text'
                    ]
                ]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if (isset($result['output']['text'])) {
                $titles = explode("\n", trim($result['output']['text']));
                return array_map('trim', $titles);
            }

            return false;

        } catch (\Exception $e) {
            throw new Exception("调用阿里云标题生成接口失败：" . $e->getMessage());
        }
    }
}