<?php
namespace app\api\controller\ai;

use app\common\controller\Api;
use think\Db;
use PHPExcel;
use PHPExcel_IOFactory;
use Parsedown;
class Sync extends Api
{
	// 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];
    
	/**
	 * 获取高频问题列表
	 */
	public function getTopList(){
		$robot = input('robot');
		/** 高频问题排行 */
        $question_listarr = collection(\app\admin\model\ai\Tagextraction::where(['robot'=>$robot,'msg_id'=>['<>',''],'name'=>['not like','%问%']])->whereRaw('CHAR_LENGTH(`name`)>2')->order('num','desc')->limit(100)->select())->toArray();
        $top_questiondata = $top_questionlist = [];
        $Msg = new \app\admin\model\ai\Msg;
        foreach($question_listarr as $key=>$item){
            if(in_array($item['msg_id'],$top_questiondata)){
                continue;
            }
            $top_questiondata[] = $item['msg_id'];
            $msg = $Msg->where(['robot'=>$robot,'id'=>$item['msg_id']])->find();
            if(empty($msg)){
                continue;
            }
            $msg['content'] = str_ireplace("autoplay","",$msg['content']);
            if(count($top_questionlist)>=20)break;
            $top_questionlist[] = 
                [
                    'name'=>$msg['msg'],
                    'count'=>$item['num'],
                ];
        }
        $this->success('ok',$top_questionlist);
	}
}