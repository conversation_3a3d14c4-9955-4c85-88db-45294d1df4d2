<?php

namespace app\api\controller;

use app\common\controller\Api;

use think\Db;

/**
 * 首页接口
 */
class Index extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 首页
     *
     */
    public function index()
    {
        $user_agent = request()->header()['user-agent'];
        $robot = input('robot');
        if($robot==61)
        {
            $url = 'https://aimaster.jw100.com.cn/web/#/';
        }else{
            $url = 'https://aimaster.jw100.com.cn/font/#/';
        }
        if (isMobile($user_agent)) {
            $url = 'https://aimaster.jw100.com.cn/font/#/';
        }

        if ($robot) {
            $url .= '?robot=' . input('robot');
        }
		header( 'Location:' . $url );
    }



    public function testa()
    {

        $arr = [1,2,3,4,5,6,7,7];

        cache('test',json_encode($arr),60);

        dump(cache('test'));

        // $filepath = ROOT_PA0TH . '/public/uploads/20250422/765b4c0b624aa317ccef288fcc7e1d9e.pdf';
        // $filepath = 'https://aimaster.jw100.com.cn/uploads/20250429/9f3112ba6483f9813cb8688d41f02ccd.pdf';
        // $filepath = "https://aimaster.jw100.com.cn/uploads/20250424/83b98a689678f005eafd272b897c9955.docx";
        // $filepath = '/mnt/sdc/wwwroot/ai-master/master/public/uploads/20250422/dc1025f55a69eaa68722f1abee38f7d9.pdf';
        // $service = new \app\common\service\BuildGraphService;

        // $res = $service::aliyunAnalyzePdfBuildTask($filepath,7643);
        // // $res = $service::getAliyunAnalyzePdfResult($filepath,7643);
        // // $res = $service::getAliyunAnalyzePdfStatus($filepath,7643);

        // dump($res);die;
    }



    /*
    * 手动生成无标题结构的文档知识图谱
    */
    public function buildDocxParsing()
    {

        $console_id = 103;

        $files = Db::name('ai_console_file')
        ->where('console_id',$console_id)
        ->where('deletetime',null)
        ->field('id,name,file')
        ->select();
        foreach ($files as $v) {
            $catalogs = Db::name('ai_file_slice_catalog')
                ->where('file_id',$v['id'])
                ->where('level','<=', 3)
                // ->where('deletetime',null)
                ->field('id,name,pid,level')
                ->select();

            if (count($catalogs) > 1) {
                continue;
            }

            $filepath = ROOT_PATH . 'public' . $v['file'];
            // dump($filepath);
            // $r = \app\common\service\BuildGraphService::aliyunAnalyzePdfBuildTask($filepath,$v['id']);
            $r = \app\common\service\BuildGraphService::textinSplit($filepath,$v['id']);
            dump($r);
        }   

        die;

    }





}
