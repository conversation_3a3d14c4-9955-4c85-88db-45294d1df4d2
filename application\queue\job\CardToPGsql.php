<?php

namespace app\queue\job;

use app\common\service\ai\AiService;

use think\Queue\Job;
use think\Db;


class CardToPGsql
{


    /**
     *
     * 卡片信息同步到pgsql数据库中
     * 
     **/
    public function fire(Job $job, $data)
    {
        echo date('Y-m-d H:i:s') . "\n";
        echo '======开始任务======' . "\n";
        $aiEngine = AiService::getEngine('local');
        $type = $data['type'];
        /** 卡片 */
        if($type == 1){
            $card =new \app\admin\model\ai\Card;
            $cardList = $card::where('sync', 0)->select();
            if(count($cardList) > 0)
            {
                $ids = $insert = [];
                foreach($cardList as $item){
                    $vectorizationArr = json_decode(base64_decode($item['vectorization']), true);
                    $vectorization = $vectorizationArr['embedding'] ?? '';
                    if($vectorization)
                    {
                        $vectorization = json_encode($vectorization);
                        $q = str_replace(['"',"'"],"", $item['q']);
                        $insert[] = [
                            'robot'=>$item['robot'],
                            'type'=>$type,
                            'card_id'=>$item['id'],
                            'content'=>$q,
                            'embedding'=>$vectorization,
                            'status'=>1,
                            'console_id'=>$item['console_id'],
                        ];
                        $ids[] = $item['id'];
                        if(count($insert)>=100 || count($cardList) == count($insert))
                        {
                            $aiEngine->addVectorData($insert);
                            $card->where('id', 'in', $ids)->update(['sync'=>1]);
                            $ids = $insert = [];
                        }
                    }
                }
                if($insert)
                {
                    $aiEngine->addVectorData($insert);
                    $card->where('id', 'in', $ids)->update(['sync'=>1]);
                }
            }
        }

        /** 场景热点 */
        if($type == 2){
            $vrhot =new \app\admin\model\ai\Vrhot;
            $vrhotList = $vrhot::where('sync', 0)->select();
            if(count($vrhotList) > 0)
            {
                $ids = $insert = [];
                foreach($vrhotList as $item){
                    $vectorizationArr = json_decode(base64_decode($item['vectorization']), true);
                    $vectorization = $vectorizationArr['embedding'] ?? '';
                    if($vectorization)
                    {
                        $vectorization = json_encode($vectorization);
                        $q = str_replace(['"',"'"],"", $item['tag']);
                        $insert[] = [
                            'robot'=>$item['robot'],
                            'type'=>$type,
                            'card_id'=>$item['id'],
                            'content'=>$q,
                            'embedding'=>$vectorization,
                            'status'=>1,
                        ];
                        $ids[] = $item['id'];
                        if(count($insert)>=100)
                        {
                            $aiEngine->addVectorData($insert);
                            $vrhot->where('id', 'in', $ids)->update(['sync'=>1]);
                            $ids = $insert = [];
                        }
                    }
                }
                if($insert)
                {
                    $aiEngine->addVectorData($insert);
                    $vrhot->where('id', 'in', $ids)->update(['sync'=>1]);
                }
            }
        }

        /** 用户卡片 */
        if($type == 3){
            $user_card_ext =new \app\admin\model\ai\UserCardExt;
            $user_card_extList = $user_card_ext::where('sync', 0)->select();
            if(count($user_card_extList) > 0)
            {
                $ids = $insert = [];
                foreach($user_card_extList as $item){
                    $userCard = \app\admin\model\ai\UserCard::where('id', $item['card_id'])->find();
                    if(!$userCard)continue;

                    $vectorizationArr = json_decode(base64_decode($item['vectorization']), true);
                    $vectorization = $vectorizationArr['embedding'] ?? '';
                    if($vectorization)
                    {
                        $vectorization = json_encode($vectorization);
                        $q = str_replace(['"',"'"],"", $item['name']);
                        $insert[] = [
                            'robot'=>$userCard['robot'],
                            'type'=>$type,
                            'card_id'=>$item['card_id'],
                            'content'=>$q,
                            'embedding'=>$vectorization,
                            'status'=>1,
                            'console_id' => $userCard['console_id']
                        ];
                        $ids[] = $item['id'];
                        if(count($insert)>=100)
                        {
                            $aiEngine->addVectorData($insert);
                            $user_card_ext->where('id', 'in', $ids)->update(['sync'=>1]);
                            $ids = $insert = [];
                        }
                    }else{
                        $user_card_ext->where('id', $item['id'])->update(['sync'=>2]);
                    }
                }
                if($insert)
                {
                    $aiEngine->addVectorData($insert);
                    $user_card_ext->where('id', 'in', $ids)->update(['sync'=>1]);
                }
            }
        }

        /** 问答对 */
        if($type == 4){
            $multimodel =new \app\admin\model\ai\Multimodel;
            $multimodelList = $multimodel::where('sync', 0)->select();
            if(count($multimodelList) > 0)
            {
                $ids = $insert = [];
                foreach($multimodelList as $item){
                    $vectorizationArr = json_decode(base64_decode($item['vectorization']), true);
                    $vectorization = $vectorizationArr['embedding'] ?? '';
                    if($vectorization)
                    {
                        $vectorization = json_encode($vectorization);
                        $q = str_replace(['"',"'"],"", $item['q']);
                        $insert[] = [
                            'robot'=>$item['robot'],
                            'type'=>$type,
                            'card_id'=>$item['id'],
                            'content'=>$q,
                            'embedding'=>$vectorization,
                            'status'=>1,
                            'console_id' => 0
                        ];
                        $ids[] = $item['id'];
                        if(count($insert)>=100 || count($multimodelList) == count($insert))
                        {
                            $aiEngine->addVectorData($insert);
                            $multimodel->where('id', 'in', $ids)->update(['sync'=>1]);
                            $ids = $insert = [];
                        }
                    }else{
                        $multimodel->where('id', $item['id'])->update(['sync'=>2]);
                    }
                }
                if($insert)
                {
                    $aiEngine->addVectorData($insert);
                    $multimodel->where('id', 'in', $ids)->update(['sync'=>1]);
                }
            }
        }
        echo '======执行成功======' . "\n";
        echo date('Y-m-d H:i:s') . "\n";
        
        $job->delete();

    }


    public function failed($data){
        // ...任务达到最大重试次数后，失败了
    }
}