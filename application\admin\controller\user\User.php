<?php

namespace app\admin\controller\user;

use app\admin\model\UserGroup;
use app\common\controller\Backend;
use app\common\library\Auth;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use Exception;
use think\exception\PDOException;
use app\admin\model\ai\Project;
use think\Db;
use think\exception\ValidateException;

/**
 * 会员管理
 *
 * @icon fa fa-user
 */
class User extends Backend
{

    protected $relationSearch = true;
    protected $searchFields = 'id,username,nickname';

    /**
     * @var \app\admin\model\User
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\User;
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->with('group')
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
            foreach ($list as $k => $v) {
                $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                $v->hidden(['password', 'salt']);
                $v['project_name'] = \app\admin\model\ai\Project::where('FIND_IN_SET('.$v->id.',user_ids)')->value('name');
                if(empty($v['project_name'])){
                    $v['project_name'] = $v->project_id==9999?"所有项目":\app\admin\model\ai\Project::where('id', $v->project_id)->value('name');
                }
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('groupList', build_select('row[group_id]', \app\admin\model\UserGroup::column('id,name'), 1, ['class' => 'form-control selectpicker']));
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            // 使用模型的save方法确保触发beforeInsert等事件
            $result = $this->model->allowField(true)->save($params);
            $insertId = $this->model->id;

            if($params['group_id'] == 1){
                $user_ids = Project::where('id', $params['project_id'])->value('user_ids');
                $userid_arr = explode(',', $user_ids);
                $userid_arr[] = $insertId;
                $userid_arr = array_unique($userid_arr);
                $userid_str = implode(',', $userid_arr);
                Project::where('id', $params['project_id'])->update(['user_ids' => $userid_str]);
            }
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        // 使用模型的find方法确保返回的是模型实例
        $row = $this->model->find($ids);
        $project_id = $row['project_id'];
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('groupList', build_select('row[group_id]', \app\admin\model\UserGroup::column('id,name'), $row['group_id'], ['class' => 'form-control selectpicker']));
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            // 确保使用模型实例的save方法触发beforeUpdate回调
            $result = $row->allowField(true)->save($params);

            if($params['group_id'] == 1){
                if($project_id != $params['project_id'])
                {
                    $user_ids = Project::where('id', $project_id)->value('user_ids');
                    $userid_arr = explode(',', $user_ids);
                    unset($userid_arr[array_search($row['id'], $userid_arr)]);
                    $userid_str = implode(',', $userid_arr);
                    Project::where('id', $project_id)->update(['user_ids' => $userid_str]);
                }
                $user_ids = Project::where('id', $params['project_id'])->value('user_ids');
                $userid_arr = explode(',', $user_ids);
                $userid_arr[] = $row['id'];
                $userid_arr = array_unique($userid_arr);
                $userid_str = implode(',', $userid_arr);
                Project::where('id', $params['project_id'])->update(['user_ids' => $userid_str]);
            }
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        Auth::instance()->delete($row['id']);
        $this->success();
    }

    /**
     * 导入
     *
     * @return void
     * @throws PDOException
     * @throws BindParamException
     */
    public function import()
    {
        $file = $this->request->request('file');
        if (!$file) {
            $this->error(__('Parameter %s can not be empty', 'file'));
        }
        $filePath = ROOT_PATH . DS . 'public' . DS . $file;
        if (!is_file($filePath)) {
            $this->error(__('No results were found'));
        }
        //实例化reader
        $ext = pathinfo($filePath, PATHINFO_EXTENSION);
        if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
            $this->error(__('Unknown data format'));
        }
        if ($ext === 'csv') {
            $file = fopen($filePath, 'r');
            $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
            $fp = fopen($filePath, 'w');
            $n = 0;
            while ($line = fgets($file)) {
                $line = rtrim($line, "\n\r\0");
                $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                if ($encoding !== 'utf-8') {
                    $line = mb_convert_encoding($line, 'utf-8', $encoding);
                }
                if ($n == 0 || preg_match('/^".*"$/', $line)) {
                    fwrite($fp, $line . "\n");
                } else {
                    fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                }
                $n++;
            }
            fclose($file) || fclose($fp);

            $reader = new Csv();
        } elseif ($ext === 'xls') {
            $reader = new Xls();
        } else {
            $reader = new Xlsx();
        }
        //加载文件
        $insert = [];
        $num = $insert_num = $update_num = 0;
        $errorArr = [];
        try {
            if (!$PHPExcel = $reader->load($filePath)) {
                $this->error(__('Unknown data format'));
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表
            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行
            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            $fields = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[] = $val;
                }
            }
            if($fields[0]!='角色组' || $fields[5]!='密码'){
                throw new Exception('模版格式不正确!');
            }
            $auth = new Auth();
            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $values[] = is_null($val) ? '' : $val;
                }
                if(empty($values[0]))continue;
                $username = $values[2];
                $password = $values[5];
                $mobile = $values[4];
                $group_id = UserGroup::where('name', $values[0])->value('id');
                $robot = $values[1] == '所有项目' ? 9999:Project::where('name',$values[1])->value('id');
                if(empty($username) || empty($password) || empty($mobile) || empty($group_id) || empty($robot)){
                    $errorArr[] = $currentRow;
                }else{
                    $insert[] = [
                        'username' => $username,
                        'password' => $password,
                        'mobile' => $mobile,
                        'q' => $values[1],
                        'project_id' => $robot,
                        'group_id' => $group_id,
                    ];
                }
            }
        } catch (Exception $exception) {
            $this->error($exception->getMessage());
        }

        if($errorArr){
            $this->error("导入失败，第" . implode(',', $errorArr) . "行数据存在问题!");
        }

        if (!$insert) {
            $this->error(__('No rows were updated'));
        }

        try {
            foreach ($insert as $k => $v) {
                $username = $v['username'];
                $password = $v['password'];
                $mobile = $v['mobile'];
                $group_id = $v['group_id'];
                $robot = $v['project_id'];
                $ret = $auth->register($username, $password, '', $mobile, ['group_id'=>$group_id, 'project_id'=>$robot]);
                if ($ret) {
                    $insert_num++;
                } else {
                    $update_num++;
                }
            }
            
        } catch (PDOException $exception) {
            $msg = $exception->getMessage();
            if (preg_match("/.+Integrity constraint violation: 1062 Duplicate entry '(.+)' for key '(.+)'/is", $msg, $matches)) {
                $msg = "导入失败，包含【{$matches[1]}】的记录已存在";
            };
            $this->error($msg);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success("导入成功,总共{$num}条,新增{$insert_num}条,失败:{$update_num}条!");
    }

}
