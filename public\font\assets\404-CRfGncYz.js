import{_,c as i,r as d,o as l,a as n,b as s,t as r,u as p,d as h,w as u,e as m,f as v,p as b,g as f}from"./index-h8WYcc8a.js";const g="/font/assets/404-N4aRkdWY.png",a="/font/assets/404_cloud-CPexjtDj.png",e=t=>(b("data-v-5945313b"),t=t(),f(),t),x={class:"wscn-http404-container"},k={class:"wscn-http404"},w=m('<div class="pic-404" data-v-5945313b><img class="pic-404__parent" src="'+g+'" alt="404" data-v-5945313b><img class="pic-404__child left" src="'+a+'" alt="404" data-v-5945313b><img class="pic-404__child mid" src="'+a+'" alt="404" data-v-5945313b><img class="pic-404__child right" src="'+a+'" alt="404" data-v-5945313b></div>',1),N={class:"bullshit"},S=e(()=>s("div",{class:"bullshit__oops"}," 404错误! ",-1)),I={class:"bullshit__headline"},V=e(()=>s("div",{class:"bullshit__info"}," 对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。 ",-1)),B={__name:"404",setup(t){let c=i(()=>"找不到网页！");return(C,j)=>{const o=d("router-link");return l(),n("div",x,[s("div",k,[w,s("div",N,[S,s("div",I,r(p(c)),1),V,h(o,{to:"/index",class:"bullshit__return-home"},{default:u(()=>[v(" 返回首页 ")]),_:1})])])])}}},R=_(B,[["__scopeId","data-v-5945313b"]]);export{R as default};
