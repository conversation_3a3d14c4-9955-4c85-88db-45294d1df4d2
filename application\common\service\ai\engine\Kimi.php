<?php

namespace app\common\service\ai\engine;

use app\common\service\ai\BaseEngine;
use app\common\service\ai\BaseClient;
use app\common\service\IpService;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class Kimi extends BaseEngine
{

	private $ApiKey = 'sk-L2EM28lEW0RXRXsynhcfpUx3P56yRkN5KfYCVO2eWENxFKRd';

	private $Host = 'https://api.moonshot.cn';

	/*
	* 聊天
	*/
	public function chat($modelId,$contextId,$msg,$robot,$card,$lang = 'cn')
	{
		$url = $this->Host . '/v1/chat/completions';
		$stream = true; //流式输出
		$message = [
			[
				'role' => 'system',
				'content' => $this->returnGraphBuildPrompt()
			],
			[
				'role' => 'user',
				'content' => $msg
			]
		];
		$params = [
			'model' => 'moonshot-v1-32k',
			'messages' => $message,
			'stream' => $stream
		];

		$params = json_encode($params);
		$headers = [
			'Content-Type:application/json',
			'Authorization:Bearer ' . $this->ApiKey
		];

		$answerStr = '';
		$ip = request()->ip();
		$area = IpService::getArea($ip);
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($curl, $data) {


			// 判断返回的数据是否包含 [DONE]
		    if (strpos($data, '[DONE]') !== false) {
		        // 删除 [DONE] 数据
		        $data = str_replace('[DONE]', '', $data);
		        // 发送关闭 SSE 连接的响应头
		        // header('Connection: close');
		        // 刷新输出缓冲区并关闭输出缓冲
		        ob_end_flush();
		        // 结束当前脚本
		        exit;
		    }
			$str = trim(str_replace('data:','',$data));
			// echo $str;
			$strArr = explode("\n",$str);
			foreach ($strArr as $v) {
				if (empty($v)) {
					continue;
				} 
				$v = json_decode($v,true);
				$text = $v['choices'][0]['delta']['content'];
                $text = str_replace(["    ","~"], ["","—"], $text);
				// $r = json_encode(['text' => $text,'references' => ''],JSON_UNESCAPED_UNICODE);
				// $this->sendMessage($contextId,$r);
				echo $text;
		        ob_flush();
		        flush();
			}
			// $arr = json_decode($str,true);
			// $text = $arr['choices'][0]['delta']['content'];

	        

		    return strlen($data);
		});
		$response = curl_exec($ch);
		if (curl_errno($ch)) {
		    echo 'Curl error: ' . curl_error($ch);
		}
		curl_close($ch);


	}



	private function returnGraphBuildPrompt()
	{
		return <<<EOF
		# 角色
你是一个优秀的文本处理助手和实体抽取工具，你会完全理解文本结构抽取目录和将文本中每一个有意义的实体提取出来
# 任务和要求
1. 用户会上传一段文件的内容,需要将文件内容的目录整理出来，包括一级标题和二级标题，同时从阿拉伯数字1开始重新编号，一级标题和二级标题没有层级，平铺展示，按顺序编号
2. 在标题后使用`[]`标识属于一级标题或者二级标题，数字1代表一级标题，数字2代表二级标题，例如 实验一 液体、固体样品红外吸收光谱的测定[1] 就属于一级标题，安全说明[2]就属于二级标题
2. 通读全文，理解全文的意思，从全文中抽取出关系实体，用mermaid的语法编写实体关系图
3. 抽取的实体要说明依据哪个目录,例如 样品提取[2]就说明`样品提取`这个实体是根据第`2`个目录的论点抽取出的
4. 完整输出,不要省略,不要多余内容
# 输出示例
```目录
1.实验一 液体、固体样品红外吸收光谱的测定[1]
2.安全说明[2]
3.方法依据[2]
4.实验步骤[2]
5.实验二 火焰原子吸收法测定自来水中的钠[1]
6.实验三 石墨炉原子吸收法测定自来水中的铅[1]
```
```实体内容
graph TD
    A[人工智能安全治理框架][1] --> B[治理原则][2]
    A --> C[治理框架构成][3]
    A --> D[安全风险分类][4]
    A --> E[技术应对措施][4]
    A --> F[综合治理措施][5]
    A --> G[安全开发应用指引][5]

    B --> B1[包容审慎、确保安全][5]
    B --> B2[风险导向、敏捷治理][6]
    B --> B3[技管结合、协同应对][6]
    B --> B4[开放合作、共治共享][6]
```
# 相关限制
1. 目录整理必须要细化到二级标题
2. 目录后面跟的数字代表一级和二级标题，不能包含三级标题，也就是说，整理出的目录非一级标题就是二级标题
3. 抽取的关系实体尽可能详细


EOF;



	}



	public function chatWithPrompt($msg,$prompt)
	{
		$url = $this->Host . '/v1/chat/completions';
		$stream = false; //流式输出
		$message = [
			[
				'role' => 'system',
				'content' => $prompt
			],
			[
				'role' => 'user',
				'content' => $msg
			]
		];
		$params = [
			'model' => 'moonshot-v1-32k',
			'messages' => $message,
			'stream' => $stream
		];

		$params = json_encode($params);
		$headers = [
			'Content-Type:application/json',
			'Authorization:Bearer ' . $this->ApiKey
		];

		$answerStr = '';
		$ip = request()->ip();
		$area = IpService::getArea($ip);
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

		$response = curl_exec($ch);
		// if (curl_errno($ch)) {
		//     echo 'Curl error: ' . curl_error($ch);
		// }
		curl_close($ch);
		return $response;

	}

	public function chatWithPromptPython($msg)
	{

		$path = '/mnt/sdc/wwwroot/ai-master/python/kimi/build_console_graph.py';
        $c = $this->pythonPath . ' ' . $msg;
        $res = exec(sprintf($c));

        dump($res);
	}



}