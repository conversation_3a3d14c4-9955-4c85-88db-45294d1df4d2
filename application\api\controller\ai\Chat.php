<?php

namespace app\api\controller\ai;

use app\common\controller\Api;
use app\common\service\ai\AiService;

/**
 * 对话接口
 */
class Chat extends Api
{
    // protected $noNeedLogin = ['login', 'mobilelogin', 'register', 'resetpwd', 'changeemail', 'changemobile', 'third'];
    protected $noNeedRight = '*';

    protected $model = null;

    private $lang = 'cn';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\ChatList;
    }

    /**
     * 对话列表
     */
    public function list(){
        $robot = $this->request->post('robot');
        $limit = $this->request->post('limit') ?? 20;
        $page = $this->request->post('page') ?? 1;
        if($page > 100){
            return json(['code' => -1, 'msg' => 'page不能大于100']);
        }
        $this->request->get([config('paginate.var_page') => $page]);
        $sort = 'id';
        $order = 'DESC';
        $where = ['uid'=>$this->auth->id,'robot' => $robot];
        $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
        foreach ($list as &$row) {
            $row->visible(['id','title','createtime','chatId']);
            $row->createtime = date('Y-m-d H:i', $row->createtime);
        }

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }

    /**
     * 对话详情
     */
    public function detail(){
        $limit = $this->request->post('limit') ?? 100;
        $page = $this->request->post('page') ?? 1;
        $robot = $this->request->post('robot');
        $chatId = $this->request->post('chatId');
        $lang = $this->request->post('lang')?$this->request->post('lang'):$this->lang;
        $this->request->get([config('paginate.var_page') => $page]);
        $sort = 'id';
        $order = 'ASC';
        $where = ['robot'=>$robot, 'chatId'=>$chatId];
        $list = \app\admin\model\ai\Msg::where($where)
                ->order($sort, $order)
                ->paginate($limit);
        
        $project = \app\admin\model\ai\Project::where(['id' => $robot])->find();
		$aiEngine = AiService::getEngine('local');
        $group_id = $this->auth->group_id;
        foreach ($list as $row) {
            $row->visible(['id','time','chatId','msg','content','card_list','referrer','click_type','images']);
            $row->time = date('Y-m-d H:i', $row->time);
            
            $references = json_decode(base64_decode($row['referrer']),true);
            $row->referrer = $references;

            if (strpos($row->content, 'video') !== false) {
                $row->card_list = [];
            } else {
                // $aiEngine->vectorContent = '';
                // $sourceArr = $aiEngine->selectVectorData($robot, 1, $row->msg, $project['matching_score'], $lang, $project['console_ids']);
    
                // if (!empty($sourceArr)) {
                //     foreach ($sourceArr as &$val) {
                //         $val['a'] = $this->formatCard($val['a']);
                //     }
                // }
                // $row->card_list = $sourceArr;
            }
            if($group_id == 3)
            {
                $content = preg_replace('/\^\[\d+\]\^/', '', $row->content);
                $row->content = $content;
            }
        }

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
        
    }

    private function formatCard($card) {

		if(strpos($card,'.html')!==false || strpos($card,'.pdf')!==false || strpos($card,'deviceModel')!==false || strpos($card,'tour/')!==false || strpos($card,'(nw)')!==false)
		{
			$result = preg_replace_callback('/\[\!\[(.*?)\]\((.*?)\)\]\((.*?)\)(?:\((nw)\))?/', function($matches) {
				if(stripos($matches[2],'.mp4')!==false){
					return "<div class=\"card videoEle htmlEle\" onclick=\"openWindow('{$matches[1]}','{$matches[3]}')\"><div class=\"card-pic\"><video src=\"{$matches[2]}\" style=\"width: 100%;height: 100%;\"></div><div class=\"desc\">{$matches[1]}</div></div>";
				}else{
					if(strpos($matches[3],'.pdf')!==false){
						$file_path = 'https://vrmice.jw100.com.cn/AIynnz/channels/pdf_show.html?title='.urlencode($matches[1]).'&file='.urlencode($matches[3]);
					}else{
						$file_path = $matches[3];
					}

					//新窗口打开
					if (isset($matches[4]) && $matches[4] === 'nw') {
						return "<div class=\"card videoEle htmlEle\" onclick=\"openWindow('{$matches[1]}','{$file_path}',1)\"><div class=\"card-pic\"><img src=\"{$matches[2]}\"></div><div class=\"desc\">{$matches[1]}</div></div>";
					} else {
						return "<div class=\"card videoEle htmlEle\" onclick=\"openWindow('{$matches[1]}','{$file_path}')\"><div class=\"card-pic\"><img src=\"{$matches[2]}\"></div><div class=\"desc\">{$matches[1]}</div></div>";
					}
				}
			}, $card);
		}else if(strpos($card,'mp4')!==false)
		{
			$result = preg_replace_callback('/\[\!\[(.*?)\]\((.*?)\)\]\((.*?)\)/', function($matches) {
				$thum = !empty($matches[2]) ? $matches[2] : 'https://aimaster.jw100.com.cn/assets/img/pdf_image.jpg';
				return "<div class=\"card videoEle\" onclick=\"openVideo('{$matches[1]}','{$matches[3]}')\"><div class=\"card-pic\"><img src=\"{$thum}\"></div><div class=\"desc\">{$matches[1]}</div></div>";
			}, $card);
		}else{
			$result = preg_replace_callback('/\[\!\[(.*?)\]\((.*?)\)\]\((.*?)\)/', function($matches) {
				return "<div class=\"card imgEle\" onclick=\"showImage('{$matches[1]}','{$matches[2]}')\"><div class=\"card-pic\"><img src=\"{$matches[2]}\"></div><div class=\"desc\">{$matches[1]}</div></div>";
			}, $card);
		}
		return $result;
	}

    /**
     * 重命名
     */
    public function rename(){
        $id = $this->request->post('id');
        $title = $this->request->post('title');
        $return = $this->model->where(['id'=>$id])->update(['title'=>$title]);
        if($return)
        {
            $this->success("修改成功");
        }else{
            $this->error("修改失败");
        }
    }

    /**
     * 删除
     */
    public function delete(){
        $id = $this->request->post('id');
        $return = $this->model->where(['id'=>$id])->update(['deletetime'=>time()]);
        if($return)
        {
            $this->success("删除成功");
        }else{
            $this->error("删除失败");
        }
    }
}
