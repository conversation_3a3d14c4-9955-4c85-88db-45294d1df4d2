<style>
    .slider{
        width: 287px !important;
    }
    .slider-track{
        background-image: linear-gradient(to bottom, #9b9b9b, #9b9b9b);
    }
    .slider-selection{
        background-image: linear-gradient(to bottom, #4397fd, #4397fd);
    }
</style>
<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('所属单位')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-unit_id" data-rule="required" data-source="ai/unit/index" class="form-control selectpage" name="row[unit_id]" type="text" value="{$row.unit_id|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('数据平台')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[type]', [ 'baidu'=>__('百度'),'doubao'=>__('豆包'),'local'=>__('本地')],$row['type'])}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('选择模型')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-model_id" data-rule="required" data-source="ai/bigmodel/index" class="form-control selectpage" name="row[model_id]" type="text" value="{$row.model_id|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('应用ID')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-console_key" class="form-control" name="row[console_key]" type="text" value="{$row.console_key|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Console.name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-console_ids" data-multiple="true" data-max-select-limit="50" data-source="ai/console/index" class="form-control selectpage" name="row[console_ids]" type="text" value="{$row.console_ids|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('用户')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_ids" data-source="user/user/index" data-field="nickname" data-multiple="true" data-max-select-limit="20" class="form-control selectpage" name="row[user_ids]" type="text" value="{$row.user_ids|htmlentities}" data-params='{"custom[group_id]":"1"}'>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Size')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-size" class="form-control" name="row[size]" type="text" value="{$row.size|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('欢迎词')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-welcome" class="form-control" name="row[welcome]">{$row.welcome|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Memo')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-memo" class="form-control" name="row[memo]" type="text">{$row.memo|htmlentities}</textarea>
        </div>
    </div>
    <!-- <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-url" class="form-control" name="row[url]" type="text" value="{$row.url|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Project_url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-project_url" class="form-control" name="row[project_url]" type="text" value="{$row.project_url|htmlentities}">
        </div>
    </div> -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Assistant_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-assistant_name" class="form-control" name="row[assistant_name]" type="text" value="{$row.assistant_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Assistant_name')}-英文:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-assistant_name_en" class="form-control" name="row[assistant_name_en]" type="text" value="{$row.assistant_name_en|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Assistant')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-assistant" data-rule="required" data-source="ai/assistant/index" class="form-control selectpage" name="row[assistant]" type="text" value="{$row.assistant|htmlentities}">
        </div>
    </div>
    <!-- <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Api_key')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-api_key" class="form-control" name="row[api_key]" type="text" value="{$row.api_key|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Open_unit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-open_unit" class="form-control" name="row[open_unit]" type="number" value="{$row.open_unit|htmlentities}">
        </div>
    </div> -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Vioce')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-voice" data-rule="required" data-source="ai/voice/index" class="form-control selectpage" name="row[voice]" type="text" value="{$row.voice|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('卡片匹配分')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-matching_score" type="text" class="form-control slider" data-slider-min="0.01" data-slider-max="0.99" data-slider-step="0.01" data-slider-value="{$row.matching_score>0?$row.matching_score:'0.5'}" value="{$row.matching_score>0?$row.matching_score:'0.5'}" data-slider-orientation="horizontal" data-slider-selection="before" data-slider-tooltip="show"  name="row[matching_score]"  data-slider-handle="triangle" />
            <div class="form-tip">在检索过程中，用来计算输入Query和卡片标签的相似度，高于或等于匹配分的片段将会被召回</div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('多语言开关')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[lang_type]', ['0'=>__('关'), '1'=>__('开')], $row['lang_type'])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('头部工具栏')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[top_tool]', ['0'=>__('关'), '1'=>__('开')], $row['top_tool'])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('开场动画开关')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[openvideo_type]', ['0'=>__('关'), '1'=>__('开')], $row['openvideo_type'])}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('开场视频')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-open_video" class="form-control" size="50" name="row[open_video]" type="text" value="{$row.open_video|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-open_video" class="btn btn-danger faupload" data-input-id="c-open_video" data-mimetype="video/mp4" data-multiple="false" data-preview-id="p-open_video"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-open_video" class="btn btn-primary fachoose" data-input-id="c-open_video" data-mimetype="image/*|category1" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-open_video"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-open_video"></ul>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('PC开场视频')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-pc_open_video" class="form-control" size="50" name="row[pc_open_video]" type="text" value="{$row.pc_open_video|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-pc_open_video" class="btn btn-danger faupload" data-input-id="c-pc_open_video" data-mimetype="video/mp4" data-multiple="false" data-preview-id="p-pc_open_video"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-pc_open_video" class="btn btn-primary fachoose" data-input-id="c-pc_open_video" data-mimetype="image/*|category1" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-pc_open_video"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-pc_open_video"></ul>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('开场动画欢迎词')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-openvideo_welcome" class="form-control" name="row[openvideo_welcome]">{$row.openvideo_welcome|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('系统提示词')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea style="height:300px;" id="c-system_prompt" class="form-control" name="row[system_prompt]">{$row.system_prompt|htmlentities}</textarea>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('切片匹配分')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-vector_score" type="text" class="form-control slider" data-slider-min="0.01" data-slider-max="0.99" data-slider-step="0.01" data-slider-value="{$row.vector_score>0?$row.vector_score:'0.3'}" value="{$row.vector_score>0?$row.vector_score:'0.3'}" data-slider-orientation="horizontal" data-slider-selection="before" data-slider-tooltip="show"  name="row[vector_score]"  data-slider-handle="triangle" />
            <div class="form-tip">在检索过程中，用来计算输入Query和切片内容的相似度，高于或等于匹配分的片段将会被召回</div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('召回数量')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-vector_num" type="text" class="form-control slider" data-slider-min="1" data-slider-max="20" data-slider-step="1" data-slider-value="{$row.vector_num>0?$row.vector_num:'6'}" value="{$row.vector_num>0?$row.vector_num:'6'}" data-slider-orientation="horizontal" data-slider-selection="before" data-slider-tooltip="show"  name="row[vector_num]"  data-slider-handle="triangle" />
            <div class="form-tip">从知识库中召回与输入Query匹配的片段个数，数量越大召回的片段越多</div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('追问开关')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[chasequestion_type]', ['0'=>__('关'), '1'=>__('开')], $row['chasequestion_type'])}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('字体大小')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[fontsize_type]', ['0'=>__('关'), '1'=>__('开')], $row['fontsize_type'])}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('查询增强')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[query_augmentation]', ['0'=>__('关'), '1'=>__('开')], $row['query_augmentation'])}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('知识增强')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[enhance_status]', ['0'=>__('关'), '1'=>__('开')], $row['enhance_status'])}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('知识增强权限')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[enhance_type]', ['0'=>__('关'), '1'=>__('开')], $row['enhance_type'])}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('知识图谱pdf解析加强')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[buildgraph_type]', ['0'=>__('关'), '1'=>__('开')], $row['buildgraph_type'])}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('登录开关')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[login_type]', ['0'=>__('关'), '1'=>__('开')], $row['login_type'])}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('注册开关')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[register_type]', ['0'=>__('关'), '1'=>__('开')], $row['register_type'])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('知识库管理帮助文档')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-knowledgebase_help_url" class="form-control" name="row[knowledgebase_help_url]" type="text" value="{$row.knowledgebase_help_url|htmlentities}">
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
