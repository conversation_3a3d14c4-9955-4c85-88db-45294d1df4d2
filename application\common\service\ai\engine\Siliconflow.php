<?php

namespace app\common\service\ai\engine;

use app\common\service\ai\BaseEngine;
use app\common\service\ai\BaseClient;
use app\common\service\IpService;


use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class Siliconflow extends BaseEngine
{

	private $ApiKey = 'sk-kmtwfzypptfruckbiudpdhanspiokcyfjhulzmpozcdehykx';

	/*
	* 聊天
	*/
	public function chat($modelId,$contextId,$msg,$robot,$card,$lang = 'cn')
	{
		$url = 'https://api.siliconflow.cn/v1/chat/completions';
		$stream = true; //流式输出
		$message = [
			[
				'role' => 'user',
				'content' => $msg
			]
		];
		$params = [
			'model' => 'deepseek-ai/DeepSeek-R1',
			'messages' => $message,
			'stream' => $stream
		];
		$params = json_encode($params);
		$headers = [
			'Content-Type:application/json',
			'Authorization:Bearer ' . $this->ApiKey
		];

		$answerStr = '';
		$ip = request()->ip();
		$area = IpService::getArea($ip);
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($curl, $data) {


			// 判断返回的数据是否包含 [DONE]
		    if (strpos($data, '[DONE]') !== false) {
		        // 删除 [DONE] 数据
		        $data = str_replace('[DONE]', '', $data);
		        // 发送关闭 SSE 连接的响应头
		        // header('Connection: close');
		        // 刷新输出缓冲区并关闭输出缓冲
		        ob_end_flush();
		        // 结束当前脚本
		        exit;
		    }
			$str = trim(str_replace('data:','',$data));
			// $strArr = explode("\n",$str);
			// foreach ($strArr as $v) {
			// 	if (empty($v)) {
			// 		continue;
			// 	} 
			// 	$v = json_decode($v,true);
			// 	$text = $v['choices'][0]['delta']['content'];
            //     $text = str_replace(["    ","~"], ["","—"], $text);
				// $r = json_encode(['text' => $text,'references' => ''],JSON_UNESCAPED_UNICODE);
				// $this->sendMessage($contextId,$r);
			// }
			$arr = json_decode($str,true);
			$text = $arr['choices'][0]['delta']['content'];

	        echo $text;
	        ob_flush();
	        flush();

		    return strlen($data);
		});
		$response = curl_exec($ch);
		if (curl_errno($ch)) {
		    echo 'Curl error: ' . curl_error($ch);
		}
		curl_close($ch);


	}



}