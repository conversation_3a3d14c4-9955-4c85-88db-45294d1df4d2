<?php

namespace app\common\util;

class KnowledgeGraphUtil
{
	


	/*
	* 解析mermaid语法到json
	*/
	public static function parseMermaid($text) 
    {
        $nodes = [];
        $edges = [];
        preg_match_all('/(\w+)\[([^\]]+)\]\((.*?)\)/',$text,$nodeMatches);

        foreach ($nodeMatches[0] as $k=>$v) {
            $nodes[] = [
                'name' => $nodeMatches[2][$k],
                'id' => $nodeMatches[1][$k],
                'catalog' => $nodeMatches[3][$k],
            ];
        }

        preg_match_all('/(\w+)[\[.*\]]*\s*-->\|(.*?)\|(\w+)\[.*\]/',$text,$edgeMatches);

        foreach ($edgeMatches[0] as $k=>$v) {
            $edges[] = [
                'source' => $edgeMatches[1][$k],
                'value' => $edgeMatches[2][$k],
                'target' => $edgeMatches[3][$k]
            ];
        }

        return ['nodes' => $nodes,'links' => $edges];
        
    }




}