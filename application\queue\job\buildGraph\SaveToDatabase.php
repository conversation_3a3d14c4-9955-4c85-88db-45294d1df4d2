<?php

namespace app\queue\job\buildGraph;

use app\common\util\KnowledgeGraphUtil;

use think\Queue\Job;
use think\Db;


class SaveToDatabase
{


    /**
     *
     * 解析图谱数据并保存到数据库
     * 
     **/
    public function fire(Job $job, $data)
    {

        $fileContent = $data['content'];
        $message = $data['message'];
        $console_file_id = $data['console_file_id'];

        //重试超过3次
        if ($job->attempts() > 3) {
            //通过这个方法可以检查这个任务已经重试了几次了
            $job->delete();
        }

        $catalogReg = '/```目录([^```]*)```/';
        $graphReg = '/```实体([^```]*)```/';
        preg_match($catalogReg,$message,$match);
        $catalogArr = explode("\n",$match[1]);
        $catalog = [];

        foreach ($catalogArr as $v) {
            if (empty($v)) {
                continue;
            }
            preg_match('/[^\.]*/',$v,$numberMatch);
            preg_match('/\[(\w*)\]/',$v,$levelMatch);
            $catalog[$numberMatch[0]] = [
                'id' => $numberMatch[0],
                'level' => $levelMatch[1],
                'content' => trim(str_replace([$numberMatch[0] . '.','[' . $levelMatch[1] . ']'],['',''],$v))
            ];

        }

        preg_match($graphReg,$message,$match);
        $graphData = $match[1];
        $graph = KnowledgeGraphUtil::parseMermaid($match[1]);

        $maxId = count($graph['nodes']) + 1;
        $maxChar = 'Z';
        $rootId = 'root';
        $insertNodes = [
            [
                'name' => $filename,
                'id' => $rootId,
                'category' => 0
            ]
        ];  

        $insertLinks = [];
        $lastId = $insertNodes[0]['id'];
        foreach ($catalog as &$v) {
            $tmp = [
                'name' => $v['content'],
                'id' => $maxChar . $maxId,
            ];
            if ($v['level'] == 1) {
                $lastId = $tmp['id'];

                $insertLinks[] = [
                    'source' => $rootId,
                    'value' => '包含',
                    'target' => $tmp['id'],
                ];

                $tmp['category'] = 1;
            } else {
                $insertLinks[] = [
                    'source' => $lastId,
                    'value' => '包含',
                    'target' => $tmp['id']
                ];
                $tmp['category'] = 2;
            }

            $insertNodes[] = $tmp;
            $v['id'] = $tmp['id'];
            $maxId ++;
        }
        unset($v);

        foreach ($graph['nodes'] as &$v) {
            $v['category'] = $catalog[$v['catalog']]['level'] + 1;
            $insertLinks[] = [
                'source' => $catalog[$v['catalog']]['id'],
                'value' => '包含',
                'target' => $v['id']
            ];
        }
        unset($v);

        $graph['nodes'] = array_merge($graph['nodes'],$insertNodes);
        $graph['links'] = array_merge($graph['links'],$insertLinks);

        foreach ($graph['nodes'] as &$v) {
            $nodeCount = 0;
            foreach($graph['links'] as $key => $val){
                if($v['id'] == $val['source']){
                    $nodeCount++;
                }
            }
            $v['nodeCount'] = $nodeCount ? $nodeCount : '';
        }   
        unset($v);

        //第四步将原文按照目录切片并入库
        $slices = DocParsingService::sliceDocBycatalog($fileContent,array_column($catalog,'content'));

        try {
            Db::startTrans();

            Db::name('ai_file_slice')->where('file_id',$console_file_id)->delete();
            foreach ($catalog as $k => $v) {
                $insert = [
                    'file_id' => $console_file_id,
                    'catalog' => $v['content'],
                    'sort' => $k,
                    'createtime' => time()
                ];
                Db::name('ai_file_slice')->insert($insert);

            }
            //保存图谱数据
            $insert = [
                'console_id' => $console_id,
                'file_id' => $console_file_id,
                'content' => $graphData,
                'json_data' => json_encode($graph,JSON_UNESCAPED_UNICODE),
                'createtime' => time()
            ];

            Db::name('ai_knowledge_graph')
            ->where('console_id',$console_id)
            ->where('file_id',$console_file_id)
            ->delete();

            Db::name('ai_knowledge_graph')->insert($insert);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            dump($e->getMessage());
        }

        echo '======数据入库成功======'."\n";
        $job->delete();

    }


    public function failed($data){
        // ...任务达到最大重试次数后，失败了
    }
}