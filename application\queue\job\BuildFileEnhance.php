<?php

namespace app\queue\job;

use app\common\service\KnowledgeEnhance;
use think\Queue\Job;

class BuildFileEnhance
{
    /**
     * 构建知识增强
     * 
     * @param Job $job 队列任务对象
     * @param array $data 任务数据
     */
    public function fire(Job $job, $data)
    {
        $file_id = $data['file_id'];
        $user_id = $data['user_id'];

        // 重试超过3次
        if ($job->attempts() > 3) {
            // 通过这个方法可以检查这个任务已经重试了几次了
            $job->delete();
            return;
        }

        echo ("======开始执行======"). "\n";
        echo (date('Y-m-d H:i:s')). "\n";
        echo ("执行编号：{$file_id}"). "\n";

        try {
            // 使用新的知识增强服务
            $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$user_id.',user_ids)')->find();
            if($project['enhance_status'] == 1)
            {
                $enhanceService = new KnowledgeEnhance();
                $result = $enhanceService->enhanceFile($file_id);
            
                echo ("======执行结果======"). "\n";
                echo ($result ? "成功" : "失败"). "\n";
                echo (date('Y-m-d H:i:s')). "\n";
                echo ("======执行完成======"). "\n";
            }else{
                echo ("======执行结果======"). "\n";
                echo ("该项目未开启知识增强"). "\n";
                echo (date('Y-m-d H:i:s')). "\n";
                echo ("======执行完成======"). "\n";
            }
            
            $job->delete();
        } catch (\Exception $e) {
            echo ("执行异常: " . $e->getMessage()). "\n";
            
            // 如果是第一次或第二次失败，则重新放回队列稍后重试
            if ($job->attempts() <= 2) {
                $job->release(60); // 60秒后重试
            } else {
                $job->delete(); // 第三次失败后删除任务
            }
        }
    }

    /**
     * 任务达到最大重试次数后，失败处理
     * 
     * @param array $data 任务数据
     */
    public function failed($data)
    {
        // 记录失败日志
        $file_id = $data['file_id'];
        error_log(date("Y-m-d H:i:s")."|知识增强任务最终失败，文件ID: {$file_id}\r\n", 3,
                 ROOT_PATH."/runtime/log/".date("Ym")."/buildKnowledgeEnhance.log");
    }
}
