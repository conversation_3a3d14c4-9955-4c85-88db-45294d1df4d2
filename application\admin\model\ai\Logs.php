<?php

namespace app\admin\model\ai;

use think\Model;
use traits\model\SoftDelete;

class Logs extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'ai_logs';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'source_text'
    ];
    

    
    public function getSourceList()
    {
        return ['1' => __('Source 1'), '2' => __('Source 2')];
    }


    public function getSourceTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['source']) ? $data['source'] : '');
        $list = $this->getSourceList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function project()
    {
        return $this->belongsTo('Project', 'robot', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
