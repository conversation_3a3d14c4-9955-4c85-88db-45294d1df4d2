<?php
namespace app\api\controller\ai;

use app\common\controller\Api;
use think\Db;
use PHPExcel;
use PHPExcel_IOFactory;
use Parsedown;
use app\common\service\IpService;
class Speed extends Api
{
	// 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];
    
	
	// 实现调用AppBuilder接口并进行流式输出
    public function callStreamApi()
    {
        header("Access-Control-Allow-Origin: *");
		header('Content-Type: text/event-stream');
      	header('Cache-Control: no-cache');
      	header('Connection: keep-alive');
      	header('X-Accel-Buffering: no');
        $descriptorspec = [
            0 => ["pipe", "r"],  // 标准输入
            1 => ["pipe", "w"],  // 标准输出
            2 => ["pipe", "w"]   // 标准错误输出
        ];
		$postData = file_get_contents("php://input");
        $postDataArr = json_decode($postData);
        $robot = $postDataArr->robot;
		$chatId = $postDataArr->chatId;
        $question = isset($postDataArr->prompt)?$postDataArr->prompt:"";
        
		$chatRow = Db::name('ai_msg')->where(['robot'=>$robot,'chatId'=>$chatId])->find();
		$messages = [];
		if($chatRow){
			$messages[] = [
				"content" => $chatRow['msg'],
				"role" => "user"
			];
			$messages[] = [
				"content" => $chatRow['content'],
				"role" => "assistant"
			];
		}
		$messages[] = [
			"content" => $postDataArr->prompt,
			"role" => "user"
		];
		$data = [
			"prompt" => $messages
		];
		$client = new \GuzzleHttp\Client();
		// 发送HTTP请求到OpenAI的API
		$response = $client->request('POST', 'http://*************:12345/eb_stream', [
			'headers' => [
				'Content-Type' => 'application/json',
				// 'Authorization' => 'Bearer ' . $apiKey,
			],
			'json' => $data,
			'stream' => true, // 启用流式输出
		]);
            
        // 通过流式输出将回答发送到浏览器
		$body = $response->getBody();

		$answerStr = '';
		while (!$body->eof()) {
			$chunk = $body->read(1024); // 每次读取 1KB 数据并输出
			$chunkArr = json_decode($chunk,true);
            if(isset($chunkArr['result'])){
                if(is_string($chunkArr['result']))
                {
                    $text = preg_replace("/\^(.*)\^/", "", $chunkArr['result']);
                    $data = json_encode(["text"=>$text,"references"=>'']);
                    $this->sendMessage($chatId,$data);
                }
                if(is_string($chunkArr['result']) && !empty($chunkArr['result']))
                {
                    $answerStr .= $chunkArr['result'];
                }
            }
		}
		if($answerStr)
		{
			$ip = request()->ip();
			$area = IpService::getArea($ip);
			Db::name('ai_msg')->insert(['robot'=>$postDataArr->robot, 'chatId'=> $chatId, 'msg'=>$postDataArr->prompt, 'time'=>time(), 'ip'=> $ip, 'content'=>$answerStr,'city'=>$area]);
		}
        exit;
    }
    
    function sendMessage($id, $data) {
        echo "id: $id" . PHP_EOL;
        echo "data: $data" . PHP_EOL;
        echo PHP_EOL;
        ob_flush();
        flush();
    }
}