<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use think\Cache;


/**
 * API-知识库统计
 */
class ConsoleStatistics extends Api
{
    protected $noNeedRight = ['*'];


    /**
     * ConsoleFile模型对象
     * @var \app\admin\model\ai\ConsoleFile
     */
    protected $model = null;
    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\ConsoleFile;
    }
    

    /**
     * 列表
     */
    public function index()
    {
        $start_date = $this->request->post('start_date') ? $this->request->post('start_date') : date("Y-m-d", strtotime('-3 month'));
        $end_date = $this->request->post('end_date') ? $this->request->post('end_date') : date("Y-m-d", strtotime('+1 day'));
        $master_upload = 0;
        $limit = 10;
        $sort = 'id';
        $order = 'DESC';
        $where = [];
        $console_id = $this->request->post('console_id');
        /** 文档数据 */
        if($console_id>0){
            $where['console_id'] = $console_id;
        }else{
            $console_ids = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('console_ids');
            $where['console_id'] = ['in', explode(',', $console_ids)];
        }
        if($console_id>0)
        {
            $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$console_id.',console_ids)')->value('id');
        }else{
            $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        }
        /** 问答次数 */
        $count = \app\admin\model\ai\Msg::where('robot',$project_id)->count();

        /** 文件总数 */
        $file_count = $this->model->where($where)->count();

        /** 总字数 */
        $word_count = $this->model->where($where)->sum('word_count');

        /** 总内存 */
        $memory_count = $this->model->where($where)->sum('file_size');
        $memory_count = $memory_count ?? 0;

        $group_list = collection($this->model->where($where)->field('type,count(1) num,sum(word_count) word_count,sum(file_size) file_size')->group('type')->select())->toArray();
        $file_list = $group_arr = [];
        foreach($group_list as $item){
            $group_arr[$item['type']??0] = $item;
        }
        for($i=0; $i<=5; $i++)
        {
            $num = $group_arr[$i]['num']??0;
            $word_count1 = $group_arr[$i]['word_count']??0;
            $file_size = !empty($group_arr[$i]['file_size'])?format_bytes($group_arr[$i]['file_size']):'0B';
            switch ($i) {
                case 1:
                    $type = 'docx';
                    break;
                case 2:
                    $type = 'pdf';
                    break;
                case 3:
                    $type = 'txt';
                    break;
                case 4:
                    $type = 'excel';
                    break;
                case 5:
                    $type = '链接';
                    break;
                default:
                    $type = '未知';
                    break;
            }
            if($type!='未知')
            {
                $file_list[] = [
                    'name' => $type,
                    'num' => $num,
                    'page_count' => ceil($word_count1/500),
                    'word_count' => $word_count1,
                    'file_size' => $file_size
                ];
            }
        }

        /** 多模态数据 */
        $group_list1 = collection(\app\admin\model\ai\UserCard::where($where)->field('type,count(1) num,sum(file_size) file_size,sum(duration) duration')->group('type')->select())->toArray();
        $card_list = $group_arr1 = [];
        foreach($group_list1 as $item){
            $group_arr1[$item['type']??0] = $item;
        }
        for($i=1; $i<4; $i++)
        {
            $num = $duration_count_frist = $file_size_frist = 0;
            if($i==1){
                $master_upload_image_num = \app\admin\model\ai\Card::where($where)->where(['robot'=>$project_id,'a'=>['not like',"%mp4%"]])->count();
                $file_size_frist = $master_upload_image_num*55600;
                $memory_count += $file_size_frist;
                $master_upload += $master_upload_image_num;
                $num += $master_upload_image_num;
            }else if($i==2){
                $master_upload_video_num = \app\admin\model\ai\Card::where($where)->where(['robot'=>$project_id,'a'=>['like',"%mp4%"]])->count();
                $file_size_frist = $master_upload_video_num*55600000;
                $duration_count_frist = $master_upload_video_num*123;
                $memory_count += $file_size_frist;
                $master_upload += $master_upload_video_num;
                $num += $master_upload_video_num;
            }

            $num += $group_arr1[$i]['num']??0;
            $duration_count = $group_arr1[$i]['duration']??0;
            $file_size_frist += $group_arr1[$i]['file_size']??0;
            $memory_count += $group_arr1[$i]['file_size']??0;
            $file_size = !empty($file_size_frist)?format_bytes($file_size_frist):'0B';
            switch ($i) {
                case 1:
                    $type = 'image';
                    break;
                case 2:
                    $type = 'video';
                    break;
                default:
                    $type = '未知';
                    break;
            }
            $file_count += $num;
            if($type!='未知')
            {
                $card_list[] = [
                    'name' => $type,
                    'num' => $num,
                    'duration_count' => ($duration_count_frist+$duration_count),
                    'file_size' => $file_size
                ];
            }
        }

        /** 上传者占比 */
        $group_list2 = collection($this->model->where($where)->field('user_id,count(1) num')->group('user_id')->select())->toArray();
        // dump($group_list2);exit;
        $user_list = [];
        foreach($group_list2 as $gitem){
            if($gitem['user_id']){
                $username = \app\admin\model\User::where('id',$gitem['user_id'])->value('nickname');
            }else{
                $username = '后台';
            }
            $avg = number_format($gitem['num']/$file_count,2);
            $user_list[$username] = ($avg*100);
        }
        $user_list['后台'] = $master_upload;
        /** 知识热度 */
        /** 知识热度统计用户的知识来源 */
        // $source_list = \app\admin\model\ai\Tagextraction::where('robot',$project_id)->order('num desc')->limit(10)->column('name');
        // // $source_list = ['#1 热度第一','#2 热度第二','#3 热度第三'];
        // foreach($source_list as $skey=>$sitem){
        //     $source_list[$skey] = str_replace("\r","",$sitem);
        // }
        
        $source_arr = collection(\app\admin\model\ai\Msg::where(['robot'=>$project_id,'referrer'=>['<>','']])->where(['time'=>['between',[strtotime($start_date),strtotime($end_date)+86400]]])->select())->toArray();
        $source_list = [];
        foreach($source_arr as $item){
            $references = json_decode(base64_decode($item['referrer']),true);
            foreach($references as $rkey=>&$rval)
            {
                // if(strpos($rval['title'],'xlsx')===false)continue;
                $key = md5($rval['title']);
                $content_key = md5($rval['title'].$rval['content']);
                
                if(isset($source_list[$key]))
                {
                    $source_list[$key]['num'] += 1;
                    $source_list[$key]['document_id'] = !empty($rval['document_id'])  && empty($source_list[$key]['document_id']) ? $rval['document_id'] : $source_list[$key]['document_id'];
                    $source_list[$key]['file_id'] = !empty($rval['file_id']) && empty($source_list[$key]['file_id']) ? $rval['file_id'] : $source_list[$key]['file_id'];
                }else{
                    $source_list[$key] = [
                        'title' => $rval['title'],
                        'console_name' => '',
                        'num'=>1,
                        'document_id' => $rval['document_id'] ?? '',
                        'file_id' => $rval['file_id'] ?? '',
                    ];
                }
                    
                if(isset($source_list[$key]['list'][$content_key]['num'])){
                    $num1 = $source_list[$key]['list'][$content_key]['num'] + 1;
                }else{
                    $num1 = 1;
                }
                $source_list[$key]['list'][$content_key] = [
                    'num'=> $num1,
                    'content'=>$rval['content'],
                    'console_name' => '',
                    'document_id' => $rval['document_id'] ?? '',
                    'file_id' => $rval['file_id'] ?? '',
                ];
            }
        }
        $source_list = array_slice($source_list, 0, 20);

        usort($source_list, function($a, $b) {
            return $b['num'] - $a['num'];
        });
        $number = 1;
        foreach($source_list as $skey=>&$sval){
            if(!empty($sval['document_id']) || !empty($sval['file_id']))
            {
                $file_row = [];
                if(!empty($sval['document_id']))
                {
                    $file_row = \app\admin\model\ai\ConsoleFile::where($where)->where('file_number',$sval['document_id'])->find();
                }
                if(empty($file_row) && !empty($sval['file_id'])){
                    $file_row = \app\admin\model\ai\ConsoleFile::where($where)->where('id',$sval['file_id'])->find();
                }
                if(empty($file_row)){
                    unset($source_list[$skey]);
                    continue;
                }
                $sval['title'] = $file_row['name'] ?? $sval['title'];
                $console_name = \app\admin\model\ai\Console::where('id', $file_row['console_id'])->value('name');
                $sval['console_name'] = $console_name;
            }
            $sval['no'] = $number;
            $number++;
            usort($sval['list'], function($a, $b) {
                return $b['num'] - $a['num'];
            });
        }
        usort($source_list, function($a, $b) {
            return $b['num'] - $a['num'];
        });
        /** 问答准确度评分 */
        $qa_accuracy = 80;

        $result = [
            "count" => $count, "file_count" => $file_count, "word_count" => $word_count, "memory_count" => $memory_count? format_bytes($memory_count) : '0B',
            "file_list" => $file_list,
            "card_list" => $card_list,
            'user_list' => $user_list,
            'source_list' => $source_list,
            'qa_accuracy'=>$qa_accuracy
        ];
        return json($result);
        
    }


    /*
    * 导出知识库文件列表
    */
    public function exportConsole()
    {

        $console_id = input('console_id');

        $where = [];
        if ($console_id > 0) {
            $where['console_id'] = $console_id;
        } else {
            $console_ids = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('console_ids');
            $where['console_id'] = ['in', explode(',', $console_ids)];
        }

        $list = Db::name('ai_console_file')
        ->where($where)
        ->where('deletetime',null)
        ->field('name,file_size,createtime,type,console_id')
        ->select();

        $whereImage = [];
        $whereImage['a'] = ['not like',"%mp4%"];

        $Card = new \app\admin\model\ai\Card;
        $image = $Card
                ->where($where)
                ->where($whereImage)
                ->field('q,createtime,console_id')
                ->select();
        $image = collection($image)->toArray();

        $whereImage = [];
        $whereImage['a'] = ['like',"%mp4%"];
        $video = $Card
                ->where($where)
                ->where($whereImage)
                ->field('q,createtime,console_id')
                ->select();
        $video = collection($video)->toArray();

        // $urls = \app\admin\model\ai\UserUrl::where($where)->where('user_id',$this->auth->id)->field('title,createtime')->select(); 
        // $urls = collection($urls)->toArray();

        $arr = [
          ['文件名称','格式','大小(mb)','上传时间','知识库名称'],  //这一行是表头
        ];
        $tmp = [];
        foreach ($list as $v) {
            $console_name = \app\admin\model\ai\Console::where('id', $v['console_id'])->value('name');
            if($v['type']==5)
            {
                $file_type = '链接';
            }else{
                $file_type = substr($v['name'],strrpos($v['name'],'.')+1);
            }
            $file_size = round($v['file_size'] / 1024 / 1024,3);
            $tmp = [$v['name'],$file_type,$file_size,date('Y-m-d H:i:s',$v['createtime']), $console_name];
            array_push($arr,$tmp);
        }

        foreach ($image as $v) {
            $console_name = \app\admin\model\ai\Console::where('id', $v['console_id'])->value('name');
            $file_type = '图片';
            $file_size = round(55600 / 1024 / 1024,3);
            $tmp = [$v['q'],$file_type,$file_size,date('Y-m-d H:i:s',$v['createtime']), $console_name];
            array_push($arr,$tmp);
        }

        foreach ($video as $v) {
            $console_name = \app\admin\model\ai\Console::where('id', $v['console_id'])->value('name');
            $file_type = '视频';
            $file_size = round(55600000 / 1024 / 1024,3);
            $tmp = [$v['q'],$file_type,$file_size,date('Y-m-d H:i:s',$v['createtime']), $console_name];
            array_push($arr,$tmp);
        }

        # 用户上传的视频和图片
        $userCard = new \app\admin\model\ai\UserCard;
        $whereImage1 = [];
        $whereImage1['type'] = 1;
        $image1 = $userCard
                ->where($where)
                ->where($whereImage1)
                ->field('name,createtime,console_id,file_size')
                ->select();
        $image1 = collection($image1)->toArray();

        foreach ($image1 as $v) {
            $console_name = \app\admin\model\ai\Console::where('id', $v['console_id'])->value('name');
            $file_type = '图片';
            $file_size = round($v['file_size'] / 1024 / 1024,3);
            $tmp = [$v['name'].'.jpg',$file_type,$file_size,date('Y-m-d H:i:s',$v['createtime']), $console_name];
            array_push($arr,$tmp);
        }

        $whereVideo1 = [];
        $whereVideo1['type'] = 2;
        $video1 = $userCard
                ->where($where)
                ->where($whereVideo1)
                ->field('name,createtime,console_id,file_size')
                ->select();
        $video1 = collection($video1)->toArray();

        foreach ($video1 as $v) {
            $console_name = \app\admin\model\ai\Console::where('id', $v['console_id'])->value('name');
            $file_type = '视频';
            $file_size = round($v['file_size'] / 1024 / 1024,3);
            $tmp = [$v['name'].'.mp4',$file_type,$file_size,date('Y-m-d H:i:s',$v['createtime']), $console_name];
            array_push($arr,$tmp);
        }

        // foreach ($urls as $v) {
        //     $file_type = '链接';
        //     $file_size = round(55600000 / 1024 / 1024,3);
        //     $tmp = [$v['title'],$file_type,$file_size,date('Y-m-d H:i:s',$v['createtime'])];
        //     array_push($arr,$tmp);
        // }
        $project_name = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('name');
        if($console_id)
        {
            $console_name = \app\admin\model\ai\Console::where('id', $console_id)->value('name');
            $filename = $project_name . "_" . $console_name . '_AI知识库列表';
        }else{
            $filename = $project_name . '_所有知识库_AI知识库列表';
        }
        // $filename = '文件列表-' . substr(md5(time().uniqid()),0,6);
        try {
            $cache = Cache::init();
            // 缓存数据
            $cache->set($filename, json_encode($arr), 60);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('导出成功',['url' => request()->domain() . '/api/master/console_file_log/downloadFile?filename='.$filename]);
    }


    public function downloadFile()
    {
        $name = input('filename');
        $cache = Cache::init();
        $data = 0 === strpos($name, '?') ? $cache->has(substr($name, 1)) : $cache->get($name);
        if(empty($data)) {
            $this->error('文件已过期');
        }
        $arr = json_decode($data,true);
        $title = input('filename').'.xlsx';
        ini_set ('memory_limit', '1024M');
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        foreach ($arr as $key1=>$sub_data) { //列
            foreach ($sub_data as $key2=>$item) { //行
                $sheet->setCellValueExplicitByColumnAndRow($key2+1, $key1+1,$item,'s');
            }
        }

        unset($arr);
        $writer = new Xlsx($spreadsheet);
        unset($spreadsheet);
//      $writer->save($title);
        header("Pragma: public");
        header("Expires: 0");
        header('Access-Control-Allow-Origin:*');
        header('Access-Control-Allow-Headers:content-type');
        header('Access-Control-Allow-Credentials:true');
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");;
        header("Content-Disposition:attachment;filename=$title");
        header("Content-Transfer-Encoding:binary");
        $writer->save('php://output');
    }




    /** 导出 */
    public function export(){
        $console_id = $this->request->post('console_id');
    }

}
