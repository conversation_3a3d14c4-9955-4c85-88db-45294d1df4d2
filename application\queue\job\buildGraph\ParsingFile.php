<?php

namespace app\queue\job\buildGraph;

use app\common\service\ai\AiService;
use app\common\service\DocParsingService;
use app\common\util\KnowledgeGraphUtil;


use think\Queue\Job;
use think\Queue;


class ParsingFile
{


    /**
     *
     * 向大模型请求获取图谱数据
     * 
     **/
    public function fire(Job $job, $data)
    {

        set_time_limit(0);
        //重试超过3次
        if ($job->attempts() > 3) {
            //通过这个方法可以检查这个任务已经重试了几次了
            $job->delete();
        }
        $path = $data['file_path'];
        $console_file_id = $data['console_file_id'];

        //第一步解析文件
        $content = DocParsingService::parsing($path);
        echo '======文件解析完成======' . "\n";

        Queue::push('app\queue\job\buildGraph\RequestLLM', [
            'content' => $content,
            'console_file_id' => $console_file_id,
        ]);
        $job->delete();
    }


    public function failed($data){
        // ...任务达到最大重试次数后，失败了
    }
}