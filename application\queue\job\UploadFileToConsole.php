<?php

namespace app\queue\job;

use app\common\service\ai\AiService;
use think\Queue\Job;
use think\Db;
use think\Log;
use think\Queue;

class UploadFileToConsole
{
    /**
     * 自动上传文件到知识库中（豆包+百度）
     *
     * @param Job $job
     * @param array $data
     */
    public function fire(Job $job, $data)
    {
        // 先关闭旧连接，防止使用无效连接
        Db::connect()->close();

        // 重新连接数据库
        Db::connect()->connect();

        $user_id = $data['user_id'] ?? 1;
        $url = $data['url'];
        $console_number = $data['console_number'];
        $filename = $data['filename'];
        $engine_type = $data['engine_type'];
        $console_file_id = $data['console_file_id'];

        // 重试超过3次
        if ($job->attempts() > 2) {
            echo ("任务达到最大重试次数，任务ID: {$console_file_id}"). "\n";
            $job->delete();
            return;
        }

        echo  ("======开始执行======"). "\n";
        echo (date('Y-m-d H:i:s')). "\n";
        echo ("执行编号：{$console_file_id}"). "\n";

        try {
            Db::query('SELECT 1');
            $engine = AiService::getEngine($engine_type);
            $res = $engine->addConsoleFile($console_number, $console_file_id, $url, $filename);
            if($res)
            {
                $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$user_id.',user_ids)')->find();
                if($project['enhance_status'] == 1)
                {
                    // 推入构建文档增强
                    Queue::push('app\queue\job\BuildFileEnhance', [
                        'file_id' => $console_file_id,
                        'user_id' => $user_id
                    ], 'ai_file_emhance');
                }
            }
            echo ("执行结果：{$res}"). "\n";
            echo (date('Y-m-d H:i:s')). "\n";
            echo ("======执行成功======"). "\n";
            \app\admin\model\ai\ConsoleFileLog::insert(['user_id'=> $user_id, 'action'=>1, 'filename'=> $filename, 'createtime' => time(), 'updatetime' => time()]);
            $job->delete();
        } catch (\PDOException $e) {
            if (strpos($e->getMessage(), 'MySQL server has gone away') !== false) {
                echo ("MySQL server has gone away, 尝试重新连接并重试, 任务ID: {$console_file_id}"). "\n";
                Db::close();
                Db::connect();
                sleep(5); // 延迟5秒
                $job->release(5); // 释放任务到队列，5秒后重试
            } else {
                echo ("任务执行失败, 任务ID: {$console_file_id}, 错误信息: " . $e->getMessage()). "\n";
                throw $e; // 其他异常直接抛出
            }
        }
    }

    /**
     * 任务失败处理
     *
     * @param array $data
     */
    public function failed($data)
    {
        Log::error("任务失败, 数据: " . json_encode($data));
        // 可以在这里添加其他失败处理逻辑
    }
}