<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use think\Queue;

use app\common\service\ai\AiService;



/**
 * API-知识训练
 */
class ConsoleFile extends Api
{
    protected $noNeedLogin = ['getSync','addSync', 'syncPGSql'];
    protected $noNeedRight = ['*'];


    /**
     * ConsoleFile模型对象
     * @var \app\admin\model\ai\ConsoleFile
     */
    protected $model = null;
    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\ConsoleFile;
    }
    

    /**
     * 列表
     */
    public function index()
    {
        $limit = $this->request->post('limit') ?? 50;
        $page = $this->request->post('page') ?? 1;
        $this->request->get([config('paginate.var_page') => $page]);
        $sort = $this->request->post('sort') ?? 'id';
        if($sort=='file_type') $sort = 'type';
        $order = $this->request->post('order') ?? 'DESC';
        $where = $whereNew = [];
        if($this->request->post('console_id')>0){
            $console_id = $this->request->post('console_id');
            $consoleRow = \app\admin\model\ai\Console::where('id', $console_id)->find();
            $console_arr[] = $console_id;
        }else{
            $console_ids = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('console_ids');
            $console_arr = explode(',', $console_ids);
            $consoleRow = \app\admin\model\ai\Console::where('FIND_IN_SET('.$this->auth->id.',user_id)')->find();
            // $console_id = $consoleRow['id'];
        }
        $where['console_id'] = ['in', $console_arr];
        $where['console_file.type'] = ['<>', 5];
        if($this->request->post('name')){
            $where['console_file.name'] = ['like',"%{$this->request->post('name')}%"];
        }
        if($this->request->post('file_type')){
            $whereNew['console_file.name'] = ['like',"%{$this->request->post('file_type')}%"];
        }
        $list = $this->model
                ->with(['console','user'])
                ->where($where)
                ->where($whereNew)
                ->order($sort, $order)
                ->paginate($limit);
        $refresh = 0;
        foreach ($list as $row) {
            $row->visible(['id','name','createtime','display_status','word_count','file_type','file','type','file_size','user_id','file_path','know_id','retry','catalog_status','enhance_status','enhance_status_text']);
            $row->visible(['console']);
            $row->getRelation('console')->visible(['name']);
            $row->visible(['user']);
            $row->getRelation('user')->visible(['nickname']);
            /** 状态为-处理中 去百度同步 */
            // if($row['display_status']=='1' && $refresh===0){
            //     $this->model->sync(['id'=>$console_id]);
            //     $refresh = 1;
            // }
            $file_type = substr($row['file'],strrpos($row['file'],'.')+1);
            $row['file_type'] = $file_type;
            $row['file_size'] = format_bytes($row['file_size']);
            $row['display_status'] = __('Display_status '.$row['display_status']);
            $row['enhance_status_text'] = __('Enhance_status '.$row['enhance_status']);
            if($row['file'])
            {
                $file_path = $row['file'];
                $row['file_path'] = cdnurl($row['file'], true);
            }else{
                $file_path = '/uploads/'.date("Ymd",$row['createtime']).'/'.$row['name'];
                if(file_exists(ROOT_PATH . 'public' . $file_path))
                {
                    $row['file_path'] = cdnurl($file_path, true);
                }else{
                    $file_path = '/uploads/console/'.$row['name'];
                    if(file_exists(ROOT_PATH . 'public' . $file_path))
                    {
                        $row['file_path'] = cdnurl($file_path, true);
                    }else{
                        $row['file_path'] = '';
                    }
                }
            }
            $row['createtime'] = date("Y-m-d H:i",$row['createtime']);

            /** 临时更新数据 */
            if(empty($row['type']) && file_exists(ROOT_PATH . 'public' . $file_path)){
                $suffix = strtolower(pathinfo($row['file_path'], PATHINFO_EXTENSION));
                switch ($suffix) {
                    case 'docx':
                    case 'doc':
                        $type = 1;
                        break;
                    case 'pdf':
                        $type = 2;
                        break;
                    case 'txt':
                        $type = 3;
                    case 'xlsx':
                    case 'xls':
                        $type = 4;
                        break;
                    default:
                        $type = 0;
                        break;
                }
                $params = [];
                $params['type'] = $type;
                $params['file_size'] = filesize(ROOT_PATH . 'public' . $file_path);
                if(empty($row['file'])){
                    $params['file'] = $file_path;
                }
                if(empty($row['user_id'])){
                    $userArr = explode(",",$consoleRow['user_id']);
                    $params['user_id'] = $userArr[0] ?? '';
                }
                $this->model->where('id',$row['id'])->update($params);
            }
            /** 临时更新数据END */
            $row['know_id'] = $row['catalog_status'] == 1 ? 1 : '';
            $row['retry'] = '';
        }
        $robot = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $file_size = $this->model
                ->with(['console','user'])
                ->where($where)
                ->where($whereNew)->sum('file_size');
        $result = ["total" => $list->total(), "rows" => $list->items(), "robot" => $robot, "refresh" => $refresh, "console_name" => ($consoleRow['name'] ?? ""), "file_size"=> format_bytes($file_size)];
        return json($result);
        
    }
    
    /**
     * 检测文件是否存在知识库
     */
    public function checkfile(){
        $file = input('file');
        $file_name = input('name');
        $file1 = ROOT_PATH . "public" . $file;
        if(is_file($file1) && file_exists($file1)){
            $file_md5 = md5_file($file1);
            $console_ids = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('console_ids');
            $console_arr = explode(',', $console_ids);
            $id = \app\admin\model\ai\ConsoleFile::where(['console_id'=>['in', $console_arr]])->whereRaw("name='{$file_name}' OR file_md5='{$file_md5}'")->value('id');
            if(empty($id)){
                $this->success('未检测到知识库存在该文件信息！');
            }else{
                $this->error('检测到知识库存在该文件信息！');
            }
        }else{
            $this->error('文件不存在！');
        }
    }

    /**
     * 开始训练
     */
    public function create(){
        $data = $this->request->post();
        $fileData = $data['file'];
        $console_id = $data['console_id'];

        $console = Db::name('ai_console')->where('id',$console_id)->find();
        if (empty($console)) {
            $this->error('知识库不存在');
        }

        if(count($data)>10){
            $this->error('数量超过10个文件，请删除多余文件！');
        }
            Db::startTrans();
            try {
                $console_ids = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('console_ids');
                foreach($fileData as $key => $item)
                {
                    if(empty($item)) continue;
                    $params['console_id'] = $console_id;
                    $params['file_number'] = '';
                    $params['name'] = $item['filename'];
                    $params['display_status'] = 0;
                    $params['file'] = $item['url'];

                    # 检查文件是否已存在
                    $file_md5 = md5_file(ROOT_PATH . "public" . $params['file']);
                    $console_arr = explode(',', $console_ids);
                    $id = \app\admin\model\ai\ConsoleFile::where(['console_id'=>['in', $console_arr]])->whereRaw("name='{$params['name']}' OR file_md5='{$file_md5}'")->value('id');
                    if($id){
                        error_log(date("Y-m-d H:i:s") . "|filename:" . print_r($params['name'], 1) . "|filepath:" . print_r($params['file'], 1) . "\r\n", 3, ROOT_PATH . "/runtime/log/" . date("Ym") . "/checkfile.log");
                        continue;
                    }
                    $params['user_id'] = $this->auth->id;
                    $suffix = strtolower(pathinfo($item['filename'], PATHINFO_EXTENSION));
                    switch ($suffix) {
                        case 'docx':
                        case 'doc':
                            $type = 1;
                            break;
                        case 'pdf':
                            $type = 2;
                            break;
                        case 'txt':
                            $type = 3;
                            break;
                        case 'xlsx':
                        case 'xls':
                            $type = 4;
                            break;
                        default:
                            $type = 0;
                            break;
                    }
                    $params['type'] = $type;
                    $params['file_size'] = filesize(ROOT_PATH."public" . $item['url']);
                    $params['createtime'] = time();
                    $params['updatetime'] = time();
                    $params['file_md5'] = md5_file(ROOT_PATH . "public" . $item['url']);
                    $console_file_id = $this->model->allowField(true)->insertGetId($params);
                    
                    // 推入训练队列中
                    Queue::push('app\queue\job\UploadFileToConsole', [
                        'url' => $item['fullurl'],
                        'console_number' => $console['console_number'],
                        'console_file_id' => $console_file_id,
                        'filename' => $params['name'],
                        'engine_type' => $console['type'] ?? 'baidu',
                        'user_id'=> $this->auth->id
                    ], config('task.file'));

                    // 推入知识图谱生成队列
                    Queue::push('app\queue\job\BuildKnowledgeGraph', [
                        'file_path' => ROOT_PATH . "public" . $item['url'],
                        'console_file_id' => $console_file_id
                    ], 'ai_file_buildgraph');

                    $aiEngine = AiService::getEngine($console['type']);
                    $aiEngine->vectorFileName($console_id,$console_file_id,$params['name']);

                    #  记录日志信息
                    \app\admin\model\ai\ConsoleFileLog::insert(['user_id'=> $this->auth->id, 'action'=>1, 'filename'=> $params['name'], 'createtime' => time(), 'updatetime' => time()]);
                }

                // 修改知识库整体知识图谱状态为未完成
                Db::name('ai_console')->where('id',$console_id)->update(['build_graph_status' => 0]);

                Db::commit();
            } catch (ValidateException|PDOException|Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
        
        $this->success("导入成功,训练中");
    }

    /**
     * 重命名
     */
    public function rename(){
        $id = $this->request->post('id');
        $name = $this->request->post('name');
        $row = $this->model->get($id);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $result = false;
        Db::startTrans();
        try {
            $old_name = $row['name'];
            $result = $row->allowField(true)->save(['name'=>$name]);

            \app\admin\model\ai\ConsoleFileLog::insert(['user_id'=> $this->auth->id, 'action'=>2, 'filename'=> $old_name, 'createtime' => time(), 'updatetime' => time()]);
            $console = Db::name('ai_console')->where('id',$row['console_id'])->find();
            $aiEngine = AiService::getEngine($console['type']);
            $aiEngine->vectorFileName($row['console_id'],$id,$name);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 删除
     */
    public function del(){
        
        $ids = $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            $ConsoleModel = new \app\admin\model\ai\Console;
            foreach ($list as $item) {

                $console = $ConsoleModel->where('id',$item['console_id'])->field('id,console_number,type')->find();
                $console_number = $console['console_number'];

                $aiEngine = AiService::getEngine($console['type']);
                $aiEngine->deleteConsoleFile($console_number,$item);

                $count += $item->delete();
                \app\admin\model\ai\ConsoleFileLog::insert(['user_id'=> $this->auth->id, 'action'=>3, 'filename'=> $item['name'], 'createtime' => time(), 'updatetime' => time()]);
            }


            //修改知识库整体知识图谱状态为未完成
            Db::name('ai_console')->where('id',$ids)->update(['build_graph_status' => 0]);
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success('删除成功');
        }
        $this->error(__('No rows were deleted'));
    }

    /**
     * 问题反馈
     */
    public function feedback(){
        $params = $this->request->post();
        $result = false;
        Db::startTrans();
        try {
            $params['user_id'] = $this->auth->id;
            $params['createtime'] = time();
            $params['updatetime'] = time();
            $Feedback = new \app\admin\model\ai\Feedback;
            $result =  $Feedback::insert($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        
        $this->success();
    }

    /** 
     * 获取文件状态
     */
    public function getSync(){

        $console_id = $this->model
        ->where('display_status', 1)
        ->column('console_id');

        $sync = $this->model->sync(['id'=>['in', $console_id]]);
        $this->success("获取成功");

    }

    /**
     * 同步文件到APPBuilder
     */
    public function addSync(){
        $consoleFile = $this->model->where('display_status', 0)->select();
        $Console = new \app\admin\model\ai\Console;
        foreach($consoleFile as $item){
            if(empty($item['file']))continue;
            $console_number = $Console->where('id',$item['console_id'])->value('console_number');
            
            $python = '/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/console_dataset.py "%s" 2 "%s"';
            $fileDataStr = ROOT_PATH."public".$item['file'];
            $return = exec(sprintf($python,$console_number,$fileDataStr));
            $consleFile = json_decode($return,true);
            // $return = "dataset_id='e0a7fe17-00a6-4d9c-9839-f07774d1e3cc' document_ids=['f64ec08d-36e6-4865-832b-a1c503a1512e', '13d06970-1d32-4464-86c0-82da2709bc32']";
            if(!empty($consleFile['document_ids'][0])){
                $this->model->where("id", $item['id'])->update(['file_number'=>$consleFile['document_ids'][0],'display_status'=>1]);
            }else{
                $this->model->where("id", $item['id'])->update(['display_status'=>3]);
            }
        }
        $this->success("同步成功");
    }

    /** 数据同步至PGSql */
    public function syncPGSql(){
        $aiEngine = AiService::getEngine('local');
        /** 卡片 */
        $type = 1;
        $card =new \app\admin\model\ai\Card;
        $cardList = $card::where('sync', 0)->select();
        if(count($cardList) > 0)
        {
            $ids = $insert = [];
            foreach($cardList as $item){
                $vectorizationArr = json_decode(base64_decode($item['vectorization']), true);
                $vectorization = $vectorizationArr['embedding'] ?? '';
                if($vectorization)
                {
                    $vectorization = json_encode($vectorization);
                    $q = str_replace(['"',"'"],"", $item['q']);
                    $insert[] = [
                        'robot'=>$item['robot'],
                        'type'=>$type,
                        'card_id'=>$item['id'],
                        'content'=>$q,
                        'embedding'=>$vectorization,
                        'status'=>1,
                    ];
                    $ids[] = $item['id'];
                    if(count($insert)>=100 || count($cardList) == count($insert))
                    {
                        $aiEngine->addVectorData($insert);
                        $card->where('id', 'in', $ids)->update(['sync'=>1]);
                        $ids = $insert = [];
                    }
                }
            }
        }

        /** 场景热点 */
        $type = 2;
        $vrhot =new \app\admin\model\ai\Vrhot;
        $vrhotList = $vrhot::where('sync', 0)->select();
        if(count($vrhotList) > 0)
        {
            $ids = $insert = [];
            foreach($vrhotList as $item){
                $vectorizationArr = json_decode(base64_decode($item['vectorization']), true);
                $vectorization = $vectorizationArr['embedding'] ?? '';
                if($vectorization)
                {
                    $vectorization = json_encode($vectorization);
                    $q = str_replace(['"',"'"],"", $item['tag']);
                    $insert[] = [
                        'robot'=>$item['robot'],
                        'type'=>$type,
                        'card_id'=>$item['id'],
                        'content'=>$q,
                        'embedding'=>$vectorization,
                        'status'=>1,
                    ];
                    $ids[] = $item['id'];
                    if(count($insert)>=100 || count($vrhotList) == count($insert))
                    {
                        $aiEngine->addVectorData($insert);
                        $vrhot->where('id', 'in', $ids)->update(['sync'=>1]);
                        $ids = $insert = [];
                    }
                }
            }
        }

        /** 用户卡片 */
        $type = 3;
        $user_card_ext =new \app\admin\model\ai\UserCardExt;
        $user_card_extList = $user_card_ext::where('sync', 0)->select();
        if(count($user_card_extList) > 0)
        {
            $ids = $insert = [];
            foreach($user_card_extList as $item){
                $robot = \app\admin\model\ai\UserCard::where('id', $item['card_id'])->value('robot');
                if(!$robot)continue;

                $vectorizationArr = json_decode(base64_decode($item['vectorization']), true);
                $vectorization = $vectorizationArr['embedding'] ?? '';
                if($vectorization)
                {
                    $vectorization = json_encode($vectorization);
                    $q = str_replace(['"',"'"],"", $item['name']);
                    $insert[] = [
                        'robot'=>$robot,
                        'type'=>$type,
                        'card_id'=>$item['card_id'],
                        'content'=>$q,
                        'embedding'=>$vectorization,
                        'status'=>1,
                    ];
                    $ids[] = $item['id'];
                    if(count($insert)>=100 || count($user_card_extList) == count($insert))
                    {
                        $aiEngine->addVectorData($insert);
                        $user_card_ext->where('id', 'in', $ids)->update(['sync'=>1]);
                        $ids = $insert = [];
                    }
                }else{
                    $user_card_ext->where('id', $item['id'])->update(['sync'=>2]);
                }
            }
        }

        /** 问答对 */
        $type = 4;
        $multimodel =new \app\admin\model\ai\Multimodel;
        $multimodelList = $multimodel::where('sync', 0)->select();
        if(count($multimodelList) > 0)
        {
            $ids = $insert = [];
            foreach($multimodelList as $item){
                $vectorizationArr = json_decode(base64_decode($item['vectorization']), true);
                $vectorization = $vectorizationArr['embedding'] ?? '';
                if($vectorization)
                {
                    $vectorization = json_encode($vectorization);
                    $q = str_replace(['"',"'"],"", $item['q']);
                    $insert[] = [
                        'robot'=>$item['robot'],
                        'type'=>$type,
                        'card_id'=>$item['id'],
                        'content'=>$q,
                        'embedding'=>$vectorization,
                        'status'=>1,
                    ];
                    $ids[] = $item['id'];
                    if(count($insert)>=100 || count($multimodelList) == count($insert))
                    {
                        $aiEngine->addVectorData($insert);
                        $multimodel->where('id', 'in', $ids)->update(['sync'=>1]);
                        $ids = $insert = [];
                    }
                }
            }
        }
        $this->success("同步成功");
    }

    /**
     * 检查重复文档列表
     */
    public function checkRepeat()
    {
        $robot = input("robot");
        $delete = input("delete")==1?true:false;
        $console_ids = \app\admin\model\ai\Project::where('id', $robot)->value('console_ids');
        $console_arr = explode(',', $console_ids);
        $list = $this->model->field("name,file_md5,count(1) count1")->where(['console_id'=>['in', $console_arr]])->group("file_md5")->order("count1","DESC")->select();
        if($list){
            foreach($list as $k=>$v){
                if($v['count1']>1){
                    echo "文件md5：{$v['file_md5']}重复了 {$v['count1']} 次<br/>";

                    // 获取该 file_md5 的所有记录
                    $duplicateList = $this->model->where('file_md5',  $v['file_md5'])->select();

                    if($delete && count($duplicateList) > 1){
                        $ConsoleModel = new \app\admin\model\ai\Console;

                        // 跳过第一个，保留它，删除其余的
                        array_shift($duplicateList); // 移除第一个元素（保留）

                        foreach ($duplicateList as $item) {
                            $console = $ConsoleModel->where('id',$item['console_id'])->field('id,console_number,type')->find();
                            if ($console) {
                                $console_number = $console['console_number'];
                                $aiEngine = AiService::getEngine($console['type']);
                                $aiEngine->deleteConsoleFile($console_number,$item);
                            }

                            // 删除数据库记录
                            $item->delete();
                            
                            // 记录日志
                            \app\admin\model\ai\ConsoleFileLog::insert([
                                'user_id' => $this->auth->id,
                                'action' => 3,
                                'filename' => $item['name'],
                                'createtime' => time(),
                                'updatetime' => time()
                            ]);
                        }
                    }
                }
            }
        }

        $list = $this->model->field("name,count(1) count1")->where(['console_id'=>['in', $console_arr]])->group("name")->order("count1","DESC")->select();
        if($list){
            foreach($list as $k=>$v){
                if($v['count1']>1){
                    echo "文件名：{$v['name']}重复了 {$v['count1']} 次<br/>";

                    // 获取该 file_md5 的所有记录
                    $duplicateList = $this->model->where('name',  $v['name'])->select();

                    if($delete && count($duplicateList) > 1){
                        $ConsoleModel = new \app\admin\model\ai\Console;

                        // 跳过第一个，保留它，删除其余的
                        array_shift($duplicateList); // 移除第一个元素（保留）

                        foreach ($duplicateList as $item) {
                            $console = $ConsoleModel->where('id',$item['console_id'])->field('id,console_number,type')->find();
                            if ($console) {
                                $console_number = $console['console_number'];
                                $aiEngine = AiService::getEngine($console['type']);
                                $aiEngine->deleteConsoleFile($console_number,$item);
                            }

                            // 删除数据库记录
                            $item->delete();
                            
                            // 记录日志
                            \app\admin\model\ai\ConsoleFileLog::insert([
                                'user_id' => $this->auth->id,
                                'action' => 3,
                                'filename' => $item['name'],
                                'createtime' => time(),
                                'updatetime' => time()
                            ]);
                        }
                    }
                }
            }
        }

        exit;
    }

    /**
     * 移动知识库
     */
    public function move(){
        $ids = input("ids") ? input('ids') : input('id');
        $console_id = input("console_id");
        
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        
        // 支持单个ID或ID数组
        $idsArray = is_array($ids) ? $ids : explode(',', $ids);
        
        $rows = $this->model->where('id', 'in', $idsArray)->select();
        if (empty($rows)) {
            $this->error(__('No Results were found'));
        }
        
        $successCount = 0;
        $failCount = 0;
        
        Db::startTrans();
        try {
            $targetConsole = Db::name('ai_console')->where('id', $console_id)->find();
            if (!$targetConsole) {
                throw new Exception('目标知识库不存在');
            }
            
            foreach ($rows as $row) {
                try {
                    $oldConsole = Db::name('ai_console')->where('id', $row['console_id'])->find();
                    if (!$oldConsole) {
                        $failCount++;
                        continue;
                    }
                    
                    $result = $row->allowField(true)->save(['console_id' => $console_id]);
                    if ($result !== false) {
                        \app\admin\model\ai\ConsoleFileLog::insert([
                            'user_id' => $this->auth->id, 
                            'action' => 2, 
                            'filename' => $row['name'] . "，从".$oldConsole['name'] . "移动到".$targetConsole['name'], 
                            'createtime' => time(), 
                            'updatetime' => time()
                        ]);
                        $successCount++;
                    } else {
                        $failCount++;
                    }
                } catch (Exception $e) {
                    $failCount++;
                    continue;
                }
            }
            
            Db::commit();
            
            if ($successCount > 0) {
                $this->success("成功移动{$successCount}个文件".($failCount > 0 ? "，失败{$failCount}个" : ""));
            } else {
                $this->error("移动失败");
            }
            
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }
}
