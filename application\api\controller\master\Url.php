<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use think\Queue;
use app\admin\model\ai\UrlData;
use app\common\service\ai\AiService;

/**
 * API-用户链接管理
 */
class Url extends Api
{
    // protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     * UserUrl模型对象
     * @var \app\admin\model\ai\UserUrl
     */
    protected $model = null;
    protected $embeddings = null;

    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\UserUrl;
    }
    

    /**
     * 列表
     */
    public function index()
    {
        $limit = 10;
        $sort = 'id';
        $order = 'DESC';
        $where = [];
        $console_id = $this->request->post('console_id');
        
        if($this->request->post('console_id')>0){
            $console_id = $this->request->post('console_id');
            $console_arr[] = $console_id;
        }else{
            $console_ids = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('console_ids');
            $console_arr = explode(',', $console_ids);
        }
        $where['console_id'] = ['in', $console_arr];
        if($this->request->post('url')){
            $where['user_url.url'] = ['like',"%{$this->request->post('url')}%"];
        }
        $list = $this->model
                ->with(['project','user'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
        foreach ($list as &$row) {
            $row->visible(['id','url','status','createtime','title']);
            $row->visible(['project']);
            $row->getRelation('project')->visible(['name']);
            $row->visible(['user']);
            $row->getRelation('user')->visible(['nickname']);
            $row['createtime'] = date("Y-m-d H:i",$row['createtime']);
            if(empty($row['title'])){
                $content = @file_get_contents($row['url']);
                preg_match('/\<title\>(.*)</isU',$content,$match);
                if(!empty($match[1])){
                    $title = $match[1];
                    if (!mb_check_encoding($title, 'UTF-8')) {
                        $title = mb_convert_encoding($title, 'UTF-8');
                    }
                    $this->model->where('id',$row['id'])->update(['title'=>$title]);
                    $row['title'] = $title;
                }
            }
        }

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
        
    }

    /**
     * 创建
     */
    public function create(){
        $console_id = $this->request->post('console_id');
        $url = $this->request->post('url');
        if(empty($url)){
            $this->error("请填写URL!");
        }
        $parse_type = $this->request->post('parse_type');
        $internal = $parse_type==2 ?? false;
        
        $result = false;
        Db::startTrans();
        try {
            $content = \fast\Http::get($url);
            // dump($content);
            preg_match('/\<title\>(.*)</isU',$content,$match);
            // dump($match[1]);exit;
            // $consoleList = \app\admin\model\ai\Console::where('FIND_IN_SET('.$this->auth->id.',user_id)')->column('id');
            $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->find();
            
            $row = $this->model->where('robot',$project['id'])->where('url',$url)->find();
            if($row){
                throw new \Exception("该链接已存在！");
            }

            $params['robot'] = $project['id'];
            $params['console_id'] = $console_id;
            $params['title'] = isset($match[1]) ? trim($match[1]) : '';
            $params['url'] = $url;
            $params['createtime'] = time();
            $params['status'] = 0;
            $params['user_id'] = $this->auth->id;
            $params['parse_type'] = $parse_type;
            $result = $this->model->allowField(true)->insertGetId($params);
            # 本地训练
            if($project['type'] == 'local')
            {
                //推入训练队列中
                Queue::push('app\queue\job\UrlToConsole', [
                    'url_id' => $result,
                    'url' => $url,
                    'internal' => $internal,
                    "user_id" => $this->auth->id,
                    "console_id" => $console_id
                ],'url_to_console');
            }

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        
        $this->success("创建成功!");
    }

    /**
     * 更新
     */
    public function update($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                #更新总链接
                $url = $item['url'];
                $content = \fast\Http::get($url);
                preg_match('/\<title\>(.*)</isU',$content,$match);
                $title = isset($match[1]) ? trim($match[1]) : '';
                $result = false;
                # 状态为未更新并且超过一小时未更新 则更新
                if( !in_array($item['status'], [0,3]) && $item['updatetime'] < time()- 3600)
                {
                    $result = $this->model->where($pk, $item[$pk])->update([
                        'updatetime' => time(),
                        'status' => 3,
                        'title' => $title
                    ]);
                }
                if($result){
                    $count++;
                    $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->find();
                    # 本地训练
                    if($project['type'] == 'local')
                    {
                        $internal = $item['parse_type']==2 ?? false;
                        //推入训练队列中
                        Queue::push('app\queue\job\UrlToConsole', [
                            'url_id' => $item['id'],
                            'url' => $url,
                            'internal' => $internal,
                            "user_id" => $this->auth->id,
                            "console_id" => $item['console_id']
                        ],'url_to_console');
                    }
                }
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success("更新中！");
        }
        $this->error(__('更新失败，请勿频繁操作（一小时后再试）！'));
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            $ConsoleModel = new \app\admin\model\ai\Console;
            foreach ($list as $item) {
                $console = $ConsoleModel->where('id',$item['console_id'])->field('id,type')->find();
                $userData = UrlData::where('url_id',$item['id'])->select();
                if($userData){
                    foreach ($userData as $value) {
                        
                        #  删除文件
                        $aiEngine = AiService::getEngine($console['type']);
                        $aiEngine->deleteConsoleFile($value['file_number'],$item);

                        \app\admin\model\ai\ConsoleFile::where('file_number',$value['file_number'])->update(['deletetime'=>time()]);
                    }
                    # 删除子链接
                    UrlData::where('url_id',$item['id'])->update(['deletetime'=>time()]);
                }
                #删除总链接
                $count += $item->delete();
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success("删除成功!");
        }
        $this->error(__('No rows were deleted'));
    }
}
