<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use think\Validate;

/**
 * API-AI问答底部菜单栏
 */
class Toolbarlist extends Api
{
    // protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     * UserUrl模型对象
     * @var \app\admin\model\ai\Toolbarlist
     */
    protected $model = null;

    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\Toolbarlist;

    }
    

    /**
     * 列表
     */
    public function index()
    {
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $where = ['robot' => $project_id];
        $limit = $this->request->post('limit') ?? 10;
        $page = $this->request->post('page') ?? 1;
        $this->request->get([config('paginate.var_page') => $page]);

        $list = $this->model
                ->with(['project'])
                ->where($where)
                ->order('sort', 'desc')
                ->paginate($limit);

        foreach ($list as $row) {
            $row->visible(['id','icon_image','show_name','question_name','createtime', 'updatetime', 'sort', 'nickname']);
            $row->visible(['project']);
            $row->getRelation('project')->visible(['name']);
            $row->nickname = $this->auth->nickname;
            $row->createtime = date('Y-m-d H:i:s', $row->createtime);
            $row->updatetime = date('Y-m-d H:i:s', $row->updatetime);
        }

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }

    /**
     * 创建
     */
    public function create(){
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $icon_image = $this->request->post('icon_image');
        $show_name = $this->request->post('show_name');
        $question_name = $this->request->post('question_name');
        $max_sort = $this->model->where('robot', $project_id)->max('sort');
        $sort = $max_sort+1;
        $result = false;
        Db::startTrans();
        try {
            $params['robot'] = $project_id;
            $params['icon_image'] = $icon_image;
            $params['show_name'] = $show_name;
            $params['question_name'] = $question_name;
            $params['sort'] = $sort;
            $params['createtime'] = time();
            $params['updatetime'] = time();
            $params['type'] = 1;
            $result = $this->model->allowField(true)->insertGetId($params);
            Db::commit();

        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        
        $this->success("创建成功!");
    }
    /**
     * 修改
     */
    public function edit(){
        $id = $this->request->post('id');
        $icon_image = $this->request->post('icon_image');
        $show_name = $this->request->post('show_name');
        $question_name = $this->request->post('question_name');
        $sort = $this->request->post('sort');

        $row = $this->model->get($id);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $result = false;
        Db::startTrans();
        try {
            $params['icon_image'] = $icon_image;
            $params['show_name'] = $show_name;
            $params['question_name'] = $question_name;
            $params['sort'] = $sort;
            $params['updatetime'] = time();

            $result = $row->allowField(true)->save($params);

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success("修改成功!");
    }

    /**
     * 通用排序
     */
    public function weigh()
    {
        //排序的数组
        $ids = $this->request->post("ids");
        //拖动的记录ID
        $changeid = $this->request->post("changeid");
        //操作字段
        $field = "sort";
        //操作的数据表
        $table = "ai_toolbar_list";
        if (!Validate::is($table, "alphaDash")) {
            $this->error();
        }
        //主键
        $pk = "id";
        //排序的方式
        $orderway = "desc";
        $orderway = $orderway == 'asc' ? 'ASC' : 'DESC';
        $sour = $weighdata = [];
        $ids = explode(',', $ids);
        $prikey = $pk && preg_match("/^[a-z0-9\-_]+$/i", $pk) ? $pk : (Db::name($table)->getPk() ?: 'id');
        $pid = $this->request->post("pid", "");
        //限制更新的字段
        $field = in_array($field, ['sort']) ? $field : 'sort';

        // 如果设定了pid的值,此时只匹配满足条件的ID,其它忽略
        if ($pid !== '') {
            $hasids = [];
            $list = Db::name($table)->where($prikey, 'in', $ids)->where('pid', 'in', $pid)->field("{$prikey},pid")->select();
            foreach ($list as $k => $v) {
                $hasids[] = $v[$prikey];
            }
            $ids = array_values(array_intersect($ids, $hasids));
        }

        $list = Db::name($table)->field("$prikey,$field")->where($prikey, 'in', $ids)->order($field, $orderway)->select();
        foreach ($list as $k => $v) {
            $sour[] = $v[$prikey];
            $weighdata[$v[$prikey]] = $v[$field];
        }
        $position = array_search($changeid, $ids);
        $desc_id = isset($sour[$position]) ? $sour[$position] : end($sour);    //移动到目标的ID值,取出所处改变前位置的值
        $sour_id = $changeid;
        $weighids = array();
        $temp = array_values(array_diff_assoc($ids, $sour));
        foreach ($temp as $m => $n) {
            if ($n == $sour_id) {
                $offset = $desc_id;
            } else {
                if ($sour_id == $temp[0]) {
                    $offset = isset($temp[$m + 1]) ? $temp[$m + 1] : $sour_id;
                } else {
                    $offset = isset($temp[$m - 1]) ? $temp[$m - 1] : $sour_id;
                }
            }
            if (!isset($weighdata[$offset])) {
                continue;
            }
            $weighids[$n] = $weighdata[$offset];
            Db::name($table)->where($prikey, $n)->update([$field => $weighdata[$offset]]);
        }
        $this->success();
    }

    /**
     * 删除（支持批量）
     */
    public function delete()
    {
        $ids = $this->request->post('ids');
        $idsArr = explode(',', $ids);
        if (empty($ids)) {
            $this->error(__('请选择要删除的数据'));
        }

        $result = false;
        Db::startTrans();
        try {
            foreach ($idsArr as $id) {
                $row = $this->model->get($id);
                if (!$row) {
                    throw new Exception(__("未找到ID为： {$id} 的记录", ['id' => $id]));
                }
                $params['deletetime'] = time();
                $result = $row->allowField(true)->save($params);
                if ($result === false) {
                    throw new Exception(__("删除ID为： {$id} 的记录失败", ['id' => $id]));
                }
            }

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }

        $this->success("删除成功!");
    }

}
