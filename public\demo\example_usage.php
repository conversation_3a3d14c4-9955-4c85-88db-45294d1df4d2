<?php
/**
 * 新整合架构使用示例
 * 展示如何使用新的LocalEngine和策略模式
 */

require_once 'application/common/service/ai/engine/LocalEngineFactory.php';
require_once 'application/common/service/ai/engine/LocalEngine.php';

use app\common\service\ai\engine\LocalEngineFactory;
use app\common\service\ai\engine\LocalEngine;

// ========== 示例1：使用工厂类创建不同策略的引擎 ==========

echo "=== 示例1：使用工厂类 ===\n";

// 创建基础引擎
$basicEngine = LocalEngineFactory::create('basic');
echo "创建基础引擎成功\n";

// 创建增强引擎
$enhanceEngine = LocalEngineFactory::create('enhance');
echo "创建增强引擎成功\n";

// 创建专业引擎
$professionalEngine = LocalEngineFactory::create('professional');
echo "创建专业引擎成功\n";

// ========== 示例2：根据项目配置自动选择引擎 ==========

echo "\n=== 示例2：根据项目配置自动选择 ===\n";

// 模拟项目配置
$project1 = ['query_augmentation' => 1]; // 应该选择专业引擎
$project2 = ['enable_enhancement' => 1]; // 应该选择增强引擎
$project3 = []; // 应该选择基础引擎

$engine1 = LocalEngineFactory::createByProject($project1);
echo "项目1使用专业引擎\n";

$engine2 = LocalEngineFactory::createByProject($project2);
echo "项目2使用增强引擎\n";

$engine3 = LocalEngineFactory::createByProject($project3);
echo "项目3使用基础引擎\n";

// ========== 示例3：手动创建和切换策略 ==========

echo "\n=== 示例3：手动创建和切换策略 ===\n";

$engine = new LocalEngine();

// 设置为基础策略
$engine->setStrategy(LocalEngine::STRATEGY_BASIC);
echo "切换到基础策略\n";

// 设置为增强策略
$engine->setStrategy(LocalEngine::STRATEGY_ENHANCE);
echo "切换到增强策略\n";

// 设置为专业策略
$engine->setStrategy(LocalEngine::STRATEGY_PROFESSIONAL);
echo "切换到专业策略\n";

// ========== 示例4：在控制器中的使用 ==========

echo "\n=== 示例4：控制器中的使用示例 ===\n";

/**
 * 模拟控制器方法
 */
function chatController()
{
    // 获取参数（模拟）
    $enhanceType = 'professional'; // 可以是 'basic', 'enhance', 'professional'
    $console_type = 1;
    $contextId = 'test_context';
    $msg = '你好，请介绍一下你自己';
    $robot = 1;
    $card = '';
    $lang = 'cn';
    $model = null;
    $debug = false;
    $ext = [];
    
    // 使用新架构
    $ai = LocalEngineFactory::create($enhanceType);
    
    echo "准备调用 {$enhanceType} 策略的聊天功能\n";
    
    // 实际调用（这里只是示例，不真正执行）
    // $ai->chat($console_type, $contextId, $msg, $robot, $card, $lang, $model, $debug, $ext);
    
    echo "聊天功能调用完成\n";
}

chatController();

// ========== 示例5：获取可用的引擎类型 ==========

echo "\n=== 示例5：获取可用的引擎类型 ===\n";

$availableTypes = LocalEngineFactory::getAvailableTypes();
foreach ($availableTypes as $type => $description) {
    echo "类型: {$type} - 描述: {$description}\n";
}

// ========== 示例6：错误处理 ==========

echo "\n=== 示例6：错误处理 ===\n";

try {
    // 尝试创建不存在的策略类型
    $invalidEngine = LocalEngineFactory::create('invalid_type');
} catch (Exception $e) {
    echo "捕获异常: " . $e->getMessage() . "\n";
}

// 创建默认引擎（当类型无效时）
$defaultEngine = LocalEngineFactory::create('invalid_type');
echo "创建默认引擎成功（无效类型时自动使用基础策略）\n";

// ========== 示例7：批量处理不同策略 ==========

echo "\n=== 示例7：批量处理不同策略 ===\n";

$strategies = ['basic', 'enhance', 'professional'];
$engines = [];

foreach ($strategies as $strategy) {
    $engines[$strategy] = LocalEngineFactory::create($strategy);
    echo "创建 {$strategy} 策略引擎\n";
}

// 模拟根据不同条件使用不同策略
function selectEngineByCondition($condition, $engines)
{
    switch ($condition) {
        case 'simple_query':
            return $engines['basic'];
        case 'complex_query':
            return $engines['enhance'];
        case 'professional_query':
            return $engines['professional'];
        default:
            return $engines['basic'];
    }
}

$conditions = ['simple_query', 'complex_query', 'professional_query'];
foreach ($conditions as $condition) {
    $selectedEngine = selectEngineByCondition($condition, $engines);
    echo "条件 '{$condition}' 选择了相应的引擎\n";
}

echo "\n=== 所有示例执行完成 ===\n";

// ========== 迁移对比示例 ==========

echo "\n=== 迁移对比示例 ===\n";

echo "// 原来的代码:\n";
echo "/*\n";
echo "if (\$enhanceType == 'professional') {\n";
echo "    \$ai = new \\app\\common\\service\\ai\\engine\\Localprofessional();\n";
echo "} elseif (\$enhanceType == 'enhance') {\n";
echo "    \$ai = new \\app\\common\\service\\ai\\engine\\Localenhance();\n";
echo "} else {\n";
echo "    \$ai = new \\app\\common\\service\\ai\\engine\\Local();\n";
echo "}\n";
echo "*/\n\n";

echo "// 新的代码:\n";
echo "\$ai = LocalEngineFactory::create(\$enhanceType);\n";
echo "\n";
echo "代码行数从 7 行减少到 1 行，简化了 85%！\n";
?>
