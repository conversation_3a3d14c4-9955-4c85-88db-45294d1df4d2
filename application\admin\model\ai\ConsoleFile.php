<?php

namespace app\admin\model\ai;

use think\Model;
use traits\model\SoftDelete;

use app\common\service\ai\AiService;



class ConsoleFile extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'ai_console_file';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [

    ];
    
    public function sync($where){
        $list = \app\admin\model\ai\Console::where($where)->select();
        // $python = '/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/console_dataset.py "%s" 1';
        // $count = $success = 0;
        // $insert = [];
        foreach($list as $item){

            if ($item['type'] == 'doubao') {
                $AiService = AiService::getEngine('doubao');
            } else if ($item['type'] == 'baidu') {
                $AiService = AiService::getEngine('baidu');
            }

            $AiService->freshConsoleFileStatus($item);
            // $return = exec(sprintf($python,$item['console_number']));
            // if(empty($return))continue;
            // $result = $this->mdTOArr($return);
            // foreach($result as $rval)
            // {
            //     if($rval['meta']['file_id']=='null' || empty($rval['meta']['file_id']))continue;
            //     $display_status = 0;
            //     if($rval['word_count']>0)
            //     {
            //         $display_status = 2;
            //     }
            //     if($display_status===0)continue;
            //     $row = $this->where(['console_id'=>$item['id'],'file_number'=>$rval['id']])->find();
            //     if($row && $row['display_status']===2)continue;
            //     if($row){
            //         $this->where(['console_id'=>$item['id'],'file_number'=>$rval['id']])->update(['display_status'=>$display_status,'word_count'=>$rval['word_count']]);
            //     }else{
            //         $insert[] = [
            //             'console_id'=>$item['id'],
            //             'file_number'=>$rval['id'],
            //             'name'=>$rval['name'],
            //             'createtime'=>$rval['created_at'],
            //             'display_status'=>$display_status,
            //             'word_count'=>$rval['word_count']
            //         ];
            //         $success++;
            //     }
            //     $count++;
            // }
        }
        // if($insert)
        // {
        //     $this->insertAll($insert);
        // }
        // return ['count'=>$count,'success'=>$success];


    }

    // MD格式转换处理
    public function mdTOArr($dataString){
        $return = json_decode($dataString, true);
        return isset($return['data']) ? $return['data'] : [];
        // 提取 data 字段中的内容
        $dataStart = strpos($dataString, "[") + 1;
        $dataEnd = strpos($dataString, "]", $dataStart);
        $dataContent = substr($dataString, $dataStart, $dataEnd - $dataStart);
        // 将 data 字段的内容解析为数组
        $documentInfos = explode("DocumentInfo(", $dataContent);
        $return = [];
        // 遍历每个 DocumentInfo 实例
        foreach ($documentInfos as $documentInfo) {
            // 解析每个实例的字段
            preg_match("/id='(.*?)', name='(.*?)', created_at=(\d+), indexing_status='(.*?)', error=(.*?), enabled=(.*?), disabled_at=(.*?), disabled_by=(.*?), display_status='(.*?)', word_count=(\d+)\)/", $documentInfo, $matches);
            if(isset($matches[1])){
                // 输出字段值
                $id = $matches[1];
                $name = $matches[2];
                $created_at = $matches[3];
                $indexing_status = $matches[4];
                $error = $matches[5];
                $enabled = $matches[6];
                $disabled_at = $matches[7];
                $disabled_by = $matches[8];
                $display_status = $matches[9];
                $word_count = $matches[10];

                $return[] = [
                    'id'=>$id,
                    'name'=>$name,
                    'created_at'=>$created_at,
                    'indexing_status'=>$indexing_status,
                    'error'=>$error,
                    'enabled'=>$enabled,
                    'disabled_at'=>$disabled_at,
                    'disabled_by'=>$disabled_by,
                    'display_status'=>$display_status,
                    'word_count'=>$word_count,
                ];
            }
        }
        return $return;
    }






    public function console()
    {
        return $this->belongsTo('Console', 'console_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function user()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
