<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use think\Cache;

/**
 * API用户信息
 */
class User extends Api
{
    // protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     * User模型对象
     * @var \app\admin\model\User
     */
    protected $model = null;
    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\User;
    }

    /**
     * 用户列表
     */
    public function index(){
        $limit = $this->request->post('limit') ?? 10;
        $page = $this->request->post('page') ?? 1;
        $this->request->get([config('paginate.var_page') => $page]);
        $sort = $this->request->post('sort') ?? 'id';
        $new_sort = '';
        if($sort == 'ai_num'){
            $new_sort = 'ai_num';
            $sort = 'id';
        }
        $order = $this->request->post('order') ?? 'DESC';
        $status = $this->request->post('status') ?? 'Normal';
        $where = ['user.status'=>$status, 'user.group_id'=>['in', [2,3]]];
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $where['project_id'] = $project_id;

        if($this->request->post('keywords')){
            $where['username|mobile'] = ['like',"%{$this->request->post('keywords')}%"];
        }

        if($this->request->post('username')){
            $where['username'] = ['like',"%{$this->request->post('username')}%"];
        }

        if($this->request->post('nickname')){
            $where['nickname'] = ['like',"%{$this->request->post('nickname')}%"];
        }

        if($this->request->post('mobile')){
            $where['mobile'] = ['like',"%{$this->request->post('mobile')}%"];
        }

        $list = $this->model
                ->with(['group'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
        foreach ($list as $row) {
            $row->visible(['id','username','nickname','mobile','logintime','loginip','joinip','jointime','createtime','status','login_num','ai_num','download_num']);
            $row->visible(['group']);
            $row->getRelation('group')->visible(['id','name']);

            /** 登录次数 */
            $row->login_num = Db::name('user_token')->where('user_id', $row['id'])->count();

            /** AI问答次数 */
            $row->ai_num = Db::name('ai_chatlist')->where('uid', $row['id'])->count();

            /** 文档下载次数 */
            $row->download_num = Db::name('ai_filedownload_log')->where('uid', $row['id'])->count();
        }

        // 按ai_num排序逻辑
        if ($new_sort == 'ai_num') {
            $order = strtolower($order) === 'asc' ? SORT_ASC : SORT_DESC;
            $items = $list->items();
            usort($items, function ($a, $b) use ($order) {
                if ($order === SORT_ASC) {
                    return $a->ai_num <=> $b->ai_num;
                } else {
                    return $b->ai_num <=> $a->ai_num;
                }
            });
            $result = array("total" => $list->total(), "rows" => $items);
        } else {
            $result = array("total" => $list->total(), "rows" => $list->items());
        }

        return json($result);

    }

    /**
     * 导出
     */
    public function export(){
        $limit = $this->request->post('limit') ?? 10000;
        $page = $this->request->post('page') ?? 1;
        $this->request->get([config('paginate.var_page') => $page]);
        $sort = 'id';
        $order = 'DESC';

        $where = ['user.status'=>'normal'];
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $where['project_id'] = $project_id;

        $list = $this->model
                ->with(['group'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

        $arr = [
          ['账号','姓名','手机号', '登录时间', '注册时间', '登录次数', 'AI问答次数', '文档下载次数'],
        ];
        $tmp = [];
        foreach ($list as $row) {
            $row->visible(['id','username','nickname','mobile','logintime','loginip','joinip','jointime','createtime','status','login_num','ai_num','download_num']);
            $row->visible(['group']);
            $row->getRelation('group')->visible(['name']);

            /** 登录次数 */
            $row->login_num = Db::name('user_token')->where('user_id', $row['id'])->count();

            /** AI问答次数 */
            $row->ai_num = Db::name('ai_chatlist')->where('uid', $row['id'])->count();

            /** 文档下载次数 */
            $row->download_num = Db::name('ai_filedownload_log')->where('uid', $row['id'])->count();
            
            $tmp = [$row['username'], $row['nickname'], $row['mobile'], date("Y-m-d H:i:s",$row['logintime']), date("Y-m-d H:i:s",$row['jointime']), $row['login_num'], $row['ai_num'], $row['download_num']];
            array_push($arr,$tmp);
        }

        $filename = '用户日志-' . substr(md5(time().uniqid()),0,6);
        try {
            $cache = Cache::init();
            // 缓存数据
            $cache->set($filename, json_encode($arr), 60);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('导出成功',['url' => request()->domain() . '/api/master/console_file_log/downloadFile?filename='.$filename]);
    }

    /**
     * 修改分组和黑名单
     */
    public function editUser(){
        $uid = $this->request->post('uid');
        $group_id = $this->request->post('group_id');
        $status = $this->request->post('status')=='normal'?'normal':'hidden';
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');

        $update = ['group_id'=>$group_id,'status'=>$status];
        $result = $this->model->where(['id'=>$uid,'project_id'=>$project_id])->update($update);
        if($result){
            $this->success("修改成功!");
        }else{
            $this->error("修改失败!");
        }
    }

    /**
     * 分组列表
     */
    public function groupList(){
        $list = Db::name('user_group')->where(['id'=>['in',[2,3]]])->field('id,name,memo')->select();
        $result = array("total" => count($list), "rows" => $list);
        return json($result);
    }


    /**
     * 修改
     */
    public function edit(){
        $nickname = $this->request->post('nickname');
        $avatar = $this->request->post('avatar');
        
        
        $result = false;
        Db::startTrans();
        try {
            $params['nickname'] = $nickname;
            $params['avatar'] = $avatar;
            $params['updatetime'] = time();
            $result = $this->model->where('id',$this->auth->id)->update($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        
        $this->success("修改成功!");
    }






    /*
    * 修改密码
    */
    public function changePass()
    {

    }

    /**
     * 移除黑名单
     */
    public function removeBlacklist(){
        $uid = $this->request->post('uid');
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');

        $update = ['status'=>'normal'];
        $result = $this->model->where(['id'=>$uid,'project_id'=>$project_id])->update($update);
        if($result){
            $this->success("移除黑名单成功!");
        }else{
            $this->error("移除黑名单失败!");
        }
    }












}
