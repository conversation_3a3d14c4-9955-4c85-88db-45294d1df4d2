<?php

namespace app\admin\model\ai;

use think\Model;
use traits\model\SoftDelete;

class ConsoleFileLog extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'ai_console_file_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'action_text',
        'type_text'
    ];
    

    
    public function getActionList()
    {
        return ['1' => __('Action 1'), '2' => __('Action 2'), '3' => __('Action 3'), '4' => __('Action 4')];
    }

    public function getTypeList()
    {
        return ['1' => __('Type 1'), '2' => __('Type 2'), '3' => __('Type 3'), '4' => __('Type 4')];
    }


    public function getActionTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['action']) ? $data['action'] : '');
        $list = $this->getActionList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type']) ? $data['type'] : '');
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function user()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
