<?php

namespace app\admin\model\ai;

use think\Model;


class UserCardExt extends Model
{

    

    

    // 表名
    protected $name = 'ai_user_card_ext';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [

    ];
    

    







    public function card()
    {
        return $this->belongsTo('app\admin\model\ai\user\Card', 'card_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
