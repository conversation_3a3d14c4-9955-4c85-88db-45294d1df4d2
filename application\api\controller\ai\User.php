<?php

namespace app\api\controller\ai;

use app\common\controller\Api;

/**
 * 会员接口
 */
class User extends Api
{
    // protected $noNeedLogin = ['login', 'mobilelogin', 'register', 'resetpwd', 'changeemail', 'changemobile', 'third'];
    protected $noNeedRight = '*';

    /**
     * 重置密码
     *
     * @ApiMethod (POST)
     * @param string $mobile      手机号
     * @param string $newpassword 新密码
     * @param string $captcha     验证码
     */
    public function resetpwd()
    {
        $newpassword = $this->request->post("newpassword");
        if(empty($newpassword))
        {
            $this->error("新密码不能为空");
        }
        //模拟一次登录
        $ret = $this->auth->changepwd($newpassword, '', true);
        if ($ret) {
            $this->success(__('Reset password successful'));
        } else {
            $this->error($this->auth->getError());
        }
    }
}
