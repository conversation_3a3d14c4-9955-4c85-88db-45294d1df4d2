<?php

namespace app\api\controller\ai;

use app\common\controller\Api;
use app\admin\model\AdminLog;
use think\Hook;
use app\common\model\User;
use think\Validate;
use app\common\library\Sms;
use think\Db;


/**
 * API-登录
 */
class Login extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    private $groupArr = [1, 2, 3];

    
    /**
     * 是否登录
     */
    public function isLogin()
    {
        $isLogin = $this->auth->isLogin();
        if($isLogin){
            $robot = input('robot');
            $user = Db::name('user')->where(['id'=>$this->auth->id,'project_id'=>$robot,'group_id'=>['in', $this->groupArr]])->find();
            if($user){
                $isLogin = true;
            }else{
                $isLogin = false;
            }
        }
        $this->success('获取成功', ['isLogin' => $isLogin?1:0]);
    }

    /**
     * 登录和注册开关
     */
    public function switch()
    {
        $robot = input('robot');
        $result = Db::name('ai_project')->field('login_type,register_type')->where('id', $robot)->find();
        $this->success('获取成功', $result);
    }


    /**
     * 前端用户登录
     */
    public function index()
    {
        $url = $this->request->get('url', '', 'url_clean');
        $url = $url ?: 'index/index';
        // if ($this->auth->isLogin()) {
        //     $this->success(__("You've logged in, do not login again"), $url);
        // }
        if ($this->request->isPost()) {
            $username = $this->request->post('username');
            $password = $this->request->post('password', '', null);
            $robot = $this->request->post('robot');
            
            AdminLog::setTitle(__('Login'));
            $result = $this->auth->login($username, $password, $this->groupArr, $robot);
            if ($result === true) {
                if($this->auth->project_id != 9999 && $this->auth->project_id != $robot){
                    $this->error('登录失败，账号项目与当前项目不匹配！');
                }
                Hook::listen("admin_login_after", $this->request);
                $group_name = Db::name('user_group')->where(['id' => $this->auth->group_id])->value('name');
                $return = [
                    'id' => $this->auth->id, 
                    'username' => $username, 
                    'nickname' => $this->auth->nickname, 
                    'avatar' => $this->auth->avatar, 
                    'token' => $this->auth->getToken(),
                    'group_id' => $this->auth->group_id,
                    'group_name' => $group_name
                ];
                $this->success(__('Login successful'), $return);
            } else {
                $msg = $this->auth->getError();
                $msg = $msg ? $msg : __('Username or password is incorrect');
                $this->error($msg);
            }
        }
        $this->error(__('Account can not be empty'));

    }



    /**
     * 仅用账号登陆（交付页免登录跳转）
     */
    public function loginByUsername()
    {
        //保持会话有效时长，单位:小时
        $keeyloginhours = 24;
        if ($this->request->isPost()) {
            $username = $this->request->post('username');

            $user = User::get(['username' => $username]);
            if (!$user) {
                $this->error('用户不存在');
            }

            if ($user->status != 'normal') {
                $this->error('用户状态不允许登录');
            }

            //直接登录会员
            $this->auth->direct($user->id);
            Hook::listen("admin_login_after", $this->request);
            $this->success(__('Login successful'), ['id' => $this->auth->id, 'username' => $username, 'nickname' => $this->auth->nickname, 'avatar' => $this->auth->avatar, 'token' => $this->auth->getToken()]);
          
        }
        $this->error(__('Account can not be empty'));

    }
    
    /**
     * 注册会员
     *
     * @ApiMethod (POST)
     * @param string $username 用户名
     * @param string $password 密码
     * @param string $email    邮箱
     * @param string $mobile   手机号
     * @param string $code     验证码
     */
    public function register()
    {
        $robot = $this->request->post('robot');
        $username = $this->request->post('username');
        $password = $this->request->post('password');
        // $email = $this->request->post('email');
        $mobile = $this->request->post('mobile');
        $code = $this->request->post('code');
        if (!$username || !$password) {
            $this->error(__('Invalid parameters'));
        }
        // if ($email && !Validate::is($email, "email")) {
        //     $this->error(__('Email is incorrect'));
        // }
        if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        $ret = Sms::check($mobile, $code, 'register');
        if (!$ret) {
            $this->error(__('Captcha is incorrect'));
        }
        $ret = $this->auth->register($username, $password, '', $mobile, ['group_id'=>3, 'project_id'=>$robot]);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Sign up successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 手机验证码登录
     *
     * @ApiMethod (POST)
     * @param string $mobile  手机号
     * @param string $captcha 验证码
     */
    public function mobilelogin()
    {
        $robot = $this->request->post('robot');
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (!Sms::check($mobile, $captcha, 'mobilelogin')) {
            $this->error(__('Captcha is incorrect'));
        }
        $user = \app\common\model\User::where(['project_id'=>$robot, 'group_id'=> ['in', $this->groupArr]])->getByMobile($mobile);
        if ($user) {
            if ($user->status != 'normal') {
                $this->error(__('Account is locked'));
            }
            //如果已经有账号则直接登录
            $ret = $this->auth->direct($user->id);
        } else {
            // $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
            $this->error(__('手机号不存在，新用户请注册!'));
        }
        if ($ret) {
            if($this->auth->project_id != 9999 && $this->auth->project_id != $robot){
                $this->error('登录失败，账号项目与当前项目不匹配！');
            }
            // Sms::flush($mobile, 'mobilelogin');
            // $data = ['userinfo' => $this->auth->getUserinfo()];
            $return = [
                'id' => $this->auth->id, 
                'username' => $this->auth->username, 
                'nickname' => $this->auth->nickname, 
                'avatar' => $this->auth->avatar, 
                'token' => $this->auth->getToken(),
                'group_id' => $this->auth->group_id,
            ];
            $this->success(__('Logged in successful'), $return);
        } else {
            $this->error($this->auth->getError());
        }
    }

}
