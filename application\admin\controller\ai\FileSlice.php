<?php

namespace app\admin\controller\ai;

use app\common\controller\Backend;
use app\common\service\TagExtraction;
use think\Db;

/**
 * 文档切片管理
 *
 * @icon fa fa-circle-o
 */
class FileSlice extends Backend
{

    /**
     * FileSlice模型对象
     * @var \app\admin\model\ai\FileSlice
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\FileSlice;
        $this->view->assign("enabledList", $this->model->getEnabledList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $whereNew = [];
            if($this->request->get('ids'))
            {
                $fileRow = \app\admin\model\ai\ConsoleFile::where('id', $this->request->get('ids'))->find();
                $whereNew['documentId'] = $fileRow['file_number'];
                $console_id = \app\admin\model\ai\Console::where('id', $fileRow['console_id'])->value('console_number');
                // $this->model->sync($console_id, $fileRow['file_number']);
            }

            $list = $this->model
                    ->where($whereNew)
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);
            $robot = 11;
            foreach ($list as $row) {
                $row->visible(['id','slice_id','type','knowledgeBaseId','documentId','content','enabled','wordCount','tokenCount','status','statusMessage','createTime','updateTime']);
                
                $tagId = Db::name('ai_file_slice_tag')->where(['robot'=>$robot,'pid'=>$row['id']])->value('id');
                if(!$tagId)
                {
                    $result = [
                        'msg' => $row['content'],
                        'robot' => $robot,
                        'pid' => $row['id']
                    ];
                    $this->tagExtractionOne($result);
                }
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

	/** 标签提取 */
	function tagExtractionOne($row){
		$msg = $row['msg'];
		$robot = $row['robot'];
		$pid = $row['pid'];
		TagExtraction::run_slice($msg, $robot, $pid);
        return true;
	}

}
