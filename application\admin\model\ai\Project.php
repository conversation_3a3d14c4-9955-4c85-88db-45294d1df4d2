<?php

namespace app\admin\model\ai;

use think\Model;


class Project extends Model
{

    

    

    // 表名
    protected $name = 'ai_project';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [

    ];
    

    
    public function unit()
    {
        return $this->belongsTo('Unit', 'unit_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

}
