<?php

namespace app\admin\model\flow;

use think\Model;
use traits\model\SoftDelete;

class CaseDetail extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'flow_casedetail';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'first_text'
    ];
    

    
    public function getFirstList()
    {
        return ['0' => __('First 0'), '1' => __('First 1')];
    }


    public function getFirstTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['first']) ? $data['first'] : '');
        $list = $this->getFirstList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function caselist()
    {
        return $this->belongsTo('CaseList', 'case_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
