<?php

namespace app\common\service\ai\engine;

use app\common\service\ai\BaseEngine;
use app\common\service\ai\BaseClient;
use app\common\service\IpService;
use Exception;
use think\Db;
use \app\admin\model\ai\Msg;
use app\admin\model\ai\Project;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\Queue;
use app\admin\model\ai\ConsoleFile;
use app\admin\model\ai\UrlData;
use app\common\service\BaiduSearchService;
use app\common\library\aliyun\VisionModel;

class Local extends BaseEngine
{
	# 硅基流动
	// private $url = "https://api.siliconflow.cn/v1/chat/completions";
	// private $token = "sk-xupsiedhwmitdoguckdruegjudnwaovmrznommcwiycqemjo";
	
	# 火山
	// private $url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions";
	// private $token = "87d2f9b6-a4e1-4004-8f79-79ba3676e1d6";

	# 阿里
	private $url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
	private $token = "sk-613ccddd0b0948179c815a0e11cd8ebf";
	private $model = 'deepseek-r1';
	private $references = [];
	private $save_references = [];
	public $vectorContent;
	private $lastMsgId;
	private $pgTable = 'public.vector_data_upgrades';

	private $paramsType = false;
	private $paramsTypeArr = ["farui-plus","chatglm3-6b"]; # 参数类型模型
	public $debug = false;
	private $msg = '';
	public $group_id = 2;

	function generateUUIDv4() {
		$data = random_bytes(16);
		// 设置 UUID 版本 (4) 和 variant bits
		$data[6] = chr(ord($data[6]) & 0x0f | 0x40);
		$data[8] = chr(ord($data[8]) & 0x3f | 0x80);
	
		return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
	}

	/** 链接PGsql数据库 */
	private function pg_connect()
	{
		$request = Db::connect(
			[
				'type' => 'pgsql',
				'hostname' => '127.0.0.1',
				'database' => config('postgres.database'),
				'username' => 'postgres',
				'password' => 'DRrTmhCKrLWs2b34',
				'hostport' => '5432'
			]
		);
		return $request;
	}

	/*
	* 创建上下文缓存获取对话id
	*/
	public function getChatId()
	{
		$uuid = $this->generateUUIDv4();
		return $uuid;
	}


	/**
	 * 聊天方法
	 * @param int $console_type 1=知识库,2=全网
	 * @param int $contextId 对话ID
	 * @param string $msg 用户输入的消息
	 * @param int $robot 机器人ID
	 * @param mixed $card 是否有卡片信息
	 * @param string $lang 语言，默认'cn'
	 * @param mixed $model 模型信息
	 * @param bool $debug 调试模式
	 * @param array $ext 扩展参数，默认为空
	 * @return void
	 */
	public function chat($console_type, $contextId, $msg, $robot, $card, $lang = 'cn', $model = null, $debug = false, $ext = [])
	{
		$this->msg = $msg;
		$this->debug = $debug;
		$ip = request()->ip();
		$area = IpService::getArea($ip);

		// 获取项目与模型信息
		$project = $this->getProjectInfo($robot);
		$bigModel = $this->getModelInfo($project, $model);
		$this->setBigModelConfig($bigModel);

		$messages = [];

		// 处理系统提示词
		if ($project['system_prompt']) {
			$messages[] = [
				"role" => "system",
				"content" => $project['system_prompt']
			];
		}

		// 获取上下文消息
		$old_messages = $this->buildContextMessages($contextId);
		$messages = array_merge($messages, $old_messages);

		// 存在图片信息时调用视觉理解接口
		if(!empty($ext['image'])){
			$vision = new VisionModel();
			$result = $vision->analyze($ext['image'], $msg);
			if(!empty($result['content']))
			{
				$answerStr = $result['content'];
				// 获取引用内容
				if($console_type == 2){
					$vectorStr = $this->buildReferenceHtml($answerStr);
				}else{
					$vectorStr = $this->buildReferenceContent($project, $answerStr);
				}
				if ($this->references) {
					$this->save_references = $this->references;
				}
				$this->saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr, $ext['image']);
				// 发送消息到前端
				$result = json_encode([
					'text' => $answerStr,
					'reasoning_content' => '',
					'references' => $this->references,
					'msg_id' => $this->lastMsgId
				], JSON_UNESCAPED_UNICODE);
				$this->sendMessage($contextId, $result);
			}

			exit;
		}

		// 获取引用内容
		if($console_type == 2){
			$vectorStr = $this->buildReferenceHtml($msg);
		}else{
			$vectorStr = $this->buildReferenceContent($project, $msg);
		}
		$userPrompt = $this->buildSystemPrompt($msg ?? '', $lang, $vectorStr);
		// 用户输入
		$messages[] = [
			"role" => "user",
			"content" => $userPrompt
		];
		
		if($this->debug){
			dump($messages);exit;
		}

		// 构造请求参数
		$payload = $this->buildPayload($messages);
		$headers = $this->buildHeaders();

		$params = json_encode($payload);

		// 发起CURL请求
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $this->url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

		$answerStr = '';
		// $this->references = [];
		$this->saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr);

		curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) use (
			$lang, $contextId, $robot, $msg, $ip, $area, $card, $debug, &$answerStr
		) {
			// 检测 [DONE] 标识
			if (strpos($data, '[DONE]') !== false) {
				$data = str_replace('[DONE]', '', $data);
				ob_end_flush();
				if ($answerStr) {
					$this->saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr);
					// 异步请求卡片标签处理
					\fast\Http::sendAsyncRequest(request()->domain() . '/api/ai/card/tagExtractionOne', [
						'msg' => $msg,
						'robot' => $robot,
						'msg_id' => $this->lastMsgId ?? 0
					]);
				}
				exit;
			}

			$lines = explode("\n", trim(str_replace('data:', '', $data)));
			foreach ($lines as $line) {
				if (empty($line)) continue;
				$v = json_decode($line, true);
				if (empty($v)) continue;

				// 判断结束标识（针对部分模型返回）
				if ($this->paramsType && !empty($v['output']['choices'][0]['finish_reason'])
					&& $v['output']['choices'][0]['finish_reason'] === 'stop'
				) {
					ob_end_flush();
					if ($answerStr) {
						$this->saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr);
						\fast\Http::sendAsyncRequest(request()->domain() . '/api/ai/card/tagExtractionOne', [
							'msg' => $msg,
							'robot' => $robot,
							'msg_id' => $this->lastMsgId ?? 0
						]);
					}
					exit;
				}

				$reasoningContent = '';
				$text = $this->paramsType
					? ($v['output']['choices'][0]['message']['content'] ?? '')
					: ($v['choices'][0]['delta']['content'] ?? '');
				$reasoningContent = $this->paramsType ? '' : (empty($text) ? ($v['choices'][0]['delta']['reasoning_content'] ?? '') : '');

				// 翻译处理（若非中文）
				if ($lang && $lang != 'cn' && trim($text) !== '\n') {
					$this->translateReferences($lang);
				}

				$text = str_replace(["    ", "~"], ["", "—"], $text);
				if ($this->references) {
					$this->save_references = $this->references;
				}

				// 记录累计返回的内容
				$answerStr .= $text;
				// 发送消息到前端
				$result = json_encode([
					'text' => $text,
					'reasoning_content' => $reasoningContent,
					'references' => $this->references,
					'msg_id' => $this->lastMsgId
				], JSON_UNESCAPED_UNICODE);
				$this->references = [];
				$this->sendMessage($contextId, $result);
			}
			return strlen($data);
		});

		$response = curl_exec($ch);
		if (curl_errno($ch)) {
			echo 'Curl error: ' . curl_error($ch);
		}
		curl_close($ch);
	}

	/** 以下为辅助方法 **/

	/**
	 * 获取项目信息
	 */
	private function getProjectInfo($robot)
	{
		$project = Db::name('ai_project')->where('id', $robot)->find();
		if(!empty($project['console_ids']) && $this->group_id == 3)
		{
			$consoleArr = Db::name('ai_console')->where(['id'=>['in', explode(',', $project['console_ids'])], 'invoking_status'=>1])->column('id');
			$project['console_ids'] = implode(',', $consoleArr);
		}
		return $project;
	}

	/**
	 * 获取模型信息
	 */
	private function getModelInfo($project, $model)
	{
		$where = $model ? ['model' => $model] : ['id' => $project['model_id']];
		return Db::name('ai_bigmodel')->where($where)->find();
	}

	/**
	 * 设置大模型配置信息
	 */
	private function setBigModelConfig($bigModel)
	{
		$this->url = $bigModel['api_url'];
		$this->token = $bigModel['api_key'];
		$this->model = $bigModel['model'];
		$this->paramsType = in_array($this->model, $this->paramsTypeArr);
	}

	/**
	 * 构造对话上下文（取最近三条消息）
	 */
	private function buildContextMessages($contextId)
	{
		$messages = [];
		$msgList = Msg::where('chatId', $contextId)->order('id', 'desc')->limit(3)->select();
		foreach ($msgList as $item) {
			$messages[] = [
				"role" => "user",
				"content" => $item['msg']
			];
			$content = mb_substr($item['content'], 0, 50);
			$messages[] = [
				"role" => "assistant",
				"content" => $content
			];
		}
		return $messages;
	}

	/**
	 * 构造引用内容
	 */
	private function buildReferenceContent($project, $msg)
	{
		$vectorStr = '';
		$vectorId = 1;
		$vectorList = $this->queryVector($project, $msg);
		if($this->debug){
			dump($vectorList);exit;
		}
		$vectorScore = $project['vector_score']>0 ? $project['vector_score'] : 0.3;
		$list = [];
		foreach ($vectorList as &$item) {
			if ((isset($item['cosine_similarity']) && $item['cosine_similarity'] > $vectorScore) || (isset($item['relevance_score']) && $item['relevance_score'] > $vectorScore)) {
				$consoleFile = ConsoleFile::where('id', $item['file_id'])->find();
				if($consoleFile)
				{
					$file_number = $consoleFile['file_number'] ?? 0;
					if(!empty($consoleFile['type']) && $consoleFile['type'] == 5){
						$urlData = UrlData::where('file_number', $file_number)->find();
						$fileName = !empty($urlData['title']) ? $urlData['title'] : $consoleFile['name'];
						$filePath = !empty($urlData['url']) ? $urlData['url'] : cdnurl($consoleFile['file'], true);
					}else{
						$fileName = !empty($consoleFile['name']) ? $consoleFile['name'] : '';
						$filePath = !empty($consoleFile['file'])? cdnurl($consoleFile['file'], true) : '';
					}
					$item['title'] = $fileName;
					$item['file'] = $filePath;
					$item['file_id'] = $item['file_id'];
					$item['file_number'] = $file_number;
					$list[] = $item;
				}
			}
		}
		if($list)
		{
			$list = $this->reorderReferences($list);
			$vectorNum = $project['vector_num']>0 ? $project['vector_num'] : 6;
			$list = array_slice($list, 0, $vectorNum);

			$mergedReferences = [];

			foreach ($list as $reference) {
				$fileId = $reference['file_id'];

				if (isset($mergedReferences[$fileId])) {
					// 如果已存在相同的 file_id，则合并 content
					$mergedReferences[$fileId]['content'] .= "\n" . $reference['content'];
				} else {
					// 否则直接存储
					$mergedReferences[$fileId] = $reference;
				}
			}

			// 转换为索引数组（去除键名）
			$list = array_values($mergedReferences);

			foreach ($list as $item) {
				$vectorStr .= "[{$vectorId}] " . $item['title'] . "：" . $item['content'] . "\n";
				$slice = $item['content'];
				$this->references[] = [
					'title'   => $item['title'],
					'content' => $slice,
					'file'    => $item['file'],
					'cosine_similarity' => $item['cosine_similarity'] ?? 0,
					'relevance_score' => $item['relevance_score'] ?? 0,
					'document_id' => $item['file_number'],
					'file_id' => $item['file_id']
				];
				$vectorId++;
			}
		}
		return $vectorStr;
	}

	/**
	 * 构造引用网页
	 */
	private function buildReferenceHtml($query)
	{
		$baiduSearch = new BaiduSearchService();
        try {
            $result = $baiduSearch->search($query, [
                'site' => [],
                'time_range' => 'year'
            ]);
			$vectorStr = '';
			$vectorId = 1;
			foreach($result['references'] as $item)
			{
				$vectorStr .= "[{$vectorId}] " . $item['title'] . "：" . $item['content'] . "\n";
				$this->references[] = [
					'title'   => $item['title'],
					'content' => $item['content'],
					'file'    => $item['url'],
					'cosine_similarity' => 0,
					'relevance_score' => 0
				];
				$vectorId++;
			}
			return $vectorStr;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
	}

	/**
	 * 构造系统提示词
	 */
	private function buildSystemPrompt($basePrompt, $lang, $vectorStr)
	{
		if ($lang && $lang != 'cn') {
			$basePrompt .= " 请只用英文回答。不要重复问题。提供一个直接的答案。";
		}

		if ($vectorStr) {
			// 构建基础规则
			$rules = [
				"只使用参考资料中的信息来回答。",
				"不要直接引用或重复原文内容。",
				"不要在答案中包含任何推理或指导文本。",
				"忽略参考内容中的图片、视频或链接。"
			];
			
			// 只有当 group_id 不为 3 时才添加引用格式说明
			if ($this->group_id != 3) {
				$rules[] = "根据下面的编号参考，回答用户的问题。";
				$rules[] = "在你的答案中，用带数字的方括号指出来源，例如，^[1]^,^[2]^。";
			}
			
			$rulesStr = implode("\n", array_map(function($rule) {
				return "					" . $rule;
			}, $rules));

			return <<<PROMPT
				任务和规则:
					{$rulesStr}
				以下是检索到的相关文档内容:
					{$vectorStr}
				提问问题：
				{$basePrompt}
			PROMPT;
		} elseif ($basePrompt) {
			return $basePrompt;
		}
		return '';
	}


	/**
	 * 构造请求payload
	 */
	private function buildPayload($messages)
	{
		$payload = [
			"model"              => $this->model,
			"messages"           => $messages,
			"stream"             => true,
			"max_tokens"         => 2096,
			"stop"               => ["null"],
			"temperature"        => 0.3,
			"top_p"              => 0.3,
			"top_k"              => 50,
			"frequency_penalty"  => 1,
			"n"                  => 1,
			"response_format"    => ["type" => "text"],
			"usage"              => [
				"prompt_tokens"         => 3019,
				"completion_tokens"     => 104,
				"total_tokens"          => 3123,
				"prompt_tokens_details" => [
					"cached_tokens" => 2048
				]
			]
		];
		if ($this->paramsType) {
			$payload = array_merge($payload, [
				"parameters" => [
					"result_format"      => "message",
					"incremental_output" => true
				],
				"input" => [
					"messages" => $messages
				]
			]);
		}
		return $payload;
	}

	/**
	 * 构造请求头
	 */
	private function buildHeaders()
	{
		$headers = [
			'Content-Type:application/json',
			'Authorization:Bearer ' . $this->token
		];
		if ($this->paramsType) {
			$headers[] = 'X-DashScope-SSE:enable';
		}
		return $headers;
	}

	/**
	 * 翻译引用内容
	 */
	private function translateReferences($targetLang)
	{
		$Translation = new \app\common\library\Translation();
		foreach ($this->references as $rKey => &$rValue) {
			$rValue['title'] = $Translation->xfyun($rValue['title'], 'cn', $targetLang);
			$rValue['content'] = $Translation->xfyun($rValue['content'], 'cn', $targetLang);
		}
	}

	/**
	 * 保存问答记录
	 */
	private function saveChatRecord($robot, $contextId, $msg, $ip, $area, $card, $lang, $answerStr, $image = '')
	{
		$referencesList = base64_encode(json_encode($this->save_references ?? []));
		$msgShort = mb_substr($msg, 0, 150);
		if($this->lastMsgId)
		{
			Db::name('ai_msg')->where('id', $this->lastMsgId)->update([
				'content' => $answerStr,
				'referrer'=> $referencesList,
			]);
		}else{
			$this->lastMsgId = Db::name('ai_msg')->insertGetId([
				'robot'   => $robot,
				'chatId'  => $contextId,
				'msg'     => $msgShort,
				'time'    => time(),
				'ip'      => $ip,
				'content' => $answerStr,
				'city'    => $area,
				'referrer'=> $referencesList,
				'card'    => $card,
				'lang'    => $lang,
				'model'   => $this->model,
				'images'  => $image
			]);
		}
	}



	public function addConsole($name,$memo)
	{
		$uuid = $this->generateUUIDv4();
		return $uuid;
	}


	public function deleteConsole($resource_id)
	{
		return ;
	}

	/**
	 * 文档标题向量化
	 * 
	 * @param $console_id
	 * @param $id
	 * @param $name
	 * @return mixed
	 */
	public function vectorFileName($console_id, $id, $name){

		$pg = $this->pg_connect();
		$name = mb_strcut($name, 0, 384);
		$vectorArr = $this->getEmbeddingsNew([$name]);
		
		$vectorContent = json_encode(['content'=>$vectorArr[0]['embedding']]);

        $vectorList = json_decode($vectorContent,true);
        if(!empty($vectorList['content']))
        {
            $queryVector = json_encode($vectorList['content']);


			$insertData = [
				'knowledge_base_id' => $console_id,
				'doc_id' => $id,
				'title' => str_replace("'", "''", $name),
				'title_embedding' => $queryVector,
				'tags' => '{}',
				'created_at' => date('Y-m-d H:i:s')
			];
			$sql = "SELECT id FROM public.document_titles WHERE doc_id ='{$id}'";
			$existing = $pg->query($sql);
			if (!empty($existing)) {
				$pg->execute("update public.document_titles set title='{$name}',title_embedding='{$queryVector}' where doc_id='{$id}'");
			}else{
				$sql = "INSERT INTO \"public\".\"document_titles\" 
							(knowledge_base_id, doc_id, title, title_embedding, tags, created_at) 
						VALUES (?, ?, ?, ?, ?, ?)";

				$params = [
					$insertData['knowledge_base_id'],
					$insertData['doc_id'],
					$insertData['title'],
					$insertData['title_embedding'],
					$insertData['tags'],
					$insertData['created_at']
				];
				$pg->execute($sql, $params);
			}
			return true;
		}
		return false;
	}
	public function addConsoleFile($console_number, $console_file_id, $file_url, $filename) 
	{
		$console_file = ConsoleFile::where("id", $console_file_id)->find();
		try {
			$query = $this->pg_connect();
			$row1 = $query->table($this->pgTable)
								->where(['file_id' => $console_file_id])
								->find();
			if (!empty($row1)) {
				return false;
			}
			// 处理文件 URL
			$file_url = substr($file_url, strpos($file_url, '/uploads'));

			// 使用封装后的切片方法，增加重试机制
			$sliceContent = $this->getSliceContent($file_url);
			if(empty($sliceContent)){
				ConsoleFile::where("id", $console_file_id)->update(['display_status' => 3]);
				error_log(date("Y-m-d H:i:s") . "|slice failed for file: {$file_url}\r\n", 3, ROOT_PATH . "/runtime/log/" . date("Ym") . "/local.log");
				return false;
			}
			$sliceList = json_decode($sliceContent, true);
			$word_count_all = 0;
			if (!empty($sliceList['content']['paragraphs'])) {
				// 用于批量处理数据
				$batchText = [];
				$batchEmbeddings = [];
				$currentMd5 = []; // 用于当前批次内去重
				foreach ($sliceList['content']['paragraphs'] as $slice) {
					// 清理文本中的单、双引号
					$text = str_replace(["'", '"'], "", $slice['text']);
					$md5 = md5($text);
					// 过滤掉已存在的数据，避免重复插入
					$old = $query->table($this->pgTable)
								->where(['file_id' => $console_file_id, 'md5' => $md5])
								->find();
					if (!empty($old)) {
						continue;
					}
					// 检查当前批次中是否已存在
					if (isset($currentMd5[$md5])) {
						continue;
					}
					$currentMd5[$md5] = true;
					// 添加到批量数组中（两个数组下标始终对应）
					$batchText[] = $text;
					$batchEmbeddings[] = mb_strcut($text, 0, 384);

					// 当累计到15条数据时，调用处理方法
					if (count($batchEmbeddings) >= 15) {
						$word_count_all += $this->processBatch($batchText, $batchEmbeddings, $console_file, $console_file_id, $query);
						// 清空批量数组，确保下个批次数据独立
						$batchText = [];
						$batchEmbeddings = [];
						$currentMd5 = [];
						usleep(100000); // 避免接口调用频繁
					}
				}
				// 处理剩余不足15条的数据
				if (!empty($batchEmbeddings)) {
					$word_count_all += $this->processBatch($batchText, $batchEmbeddings, $console_file, $console_file_id, $query);
				}
			} else {
				error_log(date("Y-m-d H:i:s") . "|sliceList:" . print_r($sliceList, 1) . "\r\n", 3, ROOT_PATH . "/runtime/log/" . date("Ym") . "/local.log");
			}

			if($console_file['type'] == 5){
				$uuid = $console_number;
			}else{
				$uuid = $this->generateUUIDv4();
			}
			if ($uuid && $console_file['word_count'] < 1 && $word_count_all > 0) {
				ConsoleFile::where("id", $console_file_id)->update(['file_number' => $uuid, 'display_status' => 2, 'word_count' => $word_count_all]);
				$return = true;
			} else {
				ConsoleFile::where("id", $console_file_id)->update(['display_status' => 3]);
				$return = false;
			}
			# 链接训练后标注状态
			if($console_file['type'] == 5){
				
				\app\admin\model\ai\UrlData::where('file_number', $uuid)->update(['status'=>$return?3:4]);

				$url_id = \app\admin\model\ai\UrlData::where('file_number' , $uuid)->value('url_id');
				$count = \app\admin\model\ai\UrlData::where(['url_id'=>$url_id,'status'=>1])->count();
				if($count<1)
				{
					$url_success_count = \app\admin\model\ai\UrlData::where(['url_id'=>$url_id,'status'=>3])->count();
					$url_fail_count = \app\admin\model\ai\UrlData::where(['url_id'=>$url_id,'status'=>4])->count();
					$error_msg = "训练成功:{$url_success_count}条，训练失败:{$url_fail_count}条";
					\app\admin\model\ai\UserUrl::where("id", $url_id)->update(['updatetime'=>time(),'status' => 1, 'error_msg' => $error_msg]);
				}
			}
			return $return;
		} catch (ValidateException | PDOException | Exception $e) {
			error_log(date("Y-m-d H:i:s") . "|getMessage:" . print_r($e->getMessage(), 1) . "\r\n", 3, ROOT_PATH . "/runtime/log/" . date("Ym") . "/local.log");
		}
	}

	// 封装切片方法，增加重试机制
	private function getSliceContent($file_url)
	{
		$sliceContent = $this->slice($file_url);
		$retryCount = 0;
		// 如果切片返回空，重试两次
		while (empty($sliceContent) && $retryCount < 2) {
			$retryCount++;
			sleep(1); // 可选：等待1秒再重试
			$sliceContent = $this->slice($file_url);
		}
		return $sliceContent;
	}

	/**
	 * 批量处理当前收集的文本数据：调用 embeddings 接口后插入 PGSQL 数据库
	 * 使用预处理语句逐行插入，确保每一条数据都独立写入
	 *
	 * @param array $batchText       待处理的完整文本数组
	 * @param array $batchEmbeddings 截取的文本数组（用于 embeddings 计算）
	 * @param array $console_file    当前 ConsoleFile 信息
	 * @param int   $console_file_id ConsoleFile 的 ID
	 * @param object $query          数据库连接对象
	 * @return int 返回本批次所有文本的字数总和
	 */
	private function processBatch($batchText, $batchEmbeddings, $console_file, $console_file_id, $query)
	{
		$word_count_batch = 0;
		$vectorArr = $this->getEmbeddingsNew($batchEmbeddings);
		if ($vectorArr && !empty($vectorArr)) {
			$insert = [];
			foreach ($vectorArr as $key => $vector) {
				if (!empty($vector['embedding'])) {
					$embedding = json_encode($vector['embedding']);
					$word_count = mb_strlen($batchText[$key]);
					$md5 = md5($batchText[$key]);
					$insert[] = [
						'console_id' => $console_file['console_id'],
						'file_id'    => $console_file_id,
						'content'    => $batchText[$key],
						'embedding'  => $embedding,
						'md5'        => $md5,
						'word_count' => $word_count
					];
					$word_count_batch += $word_count;
				}
			}
			if ($insert) {
				// 构造批量插入 SQL
				$sql = '';
				foreach ($insert as $v) {
					// 注意：如果 $v['content'] 或 $v['embedding'] 中存在特殊字符，建议使用参数绑定防止 SQL 注入
					$sql .= "({$v['console_id']}, {$v['file_id']}, '" . addslashes($v['content']) . "', '" . addslashes($v['embedding']) . "', '{$v['md5']}', {$v['word_count']}),";
				}
				$sql = rtrim($sql, ',');
				$sql1 = "INSERT INTO {$this->pgTable} (console_id, file_id, content, embedding, md5, word_count) VALUES " . $sql;
				$return = $query->execute($sql1);
				if (!$return) {
					error_log(date("Y-m-d H:i:s") . "|sql1:" . print_r($sql1, 1) . "|return:" . print_r($return, 1) . "\r\n", 3, ROOT_PATH . "/runtime/log/" . date("Ym") . "/local.log");
				}
			}
		}
		return $word_count_batch;
	}

	public function deleteConsoleFile($console_number,$console_file) 
	{
		try {
			$query = $this->pg_connect();
			
            $query->table($this->pgTable)->where(['file_id'=>$console_file['id']])->delete();
            $query->table('public.slice_qa')->where(['file_id'=>$console_file['id']])->delete();
            $query->table('public.document_titles')->where(['doc_id'=>$console_file['id']])->delete();
			return true;
		} catch (ValidateException|PDOException|Exception $e) {
			// error_log(date("Y-m-d H:i:s")."|getMessage:".print_r($e->getMessage(),1)."\r\n",3,ROOT_PATH."/runtime/log/".date("Ym")."/deleteConsoleFile.log");
			print_r($e->getMessage());
			return false;
		}
	}



	public function freshConsoleFileStatus($console)
    {
		return;
    }

    /** 切片 */
    public function slice($file_url){
		$path = '/mnt/sdc/wwwroot/ai-master/python/appbuilder/splitter.py';
		$safeString = ROOT_PATH . "public" . $file_url;
		$c = "{$this->pythonPath} {$path} '{$safeString}'";
		$return = exec($c);
        return $return;
    }

    /** 向量化 */
    public function vector($string){
		$path = '/mnt/sdc/wwwroot/ai-master/python/appbuilder/embedding.py';
		$safeString = str_replace(["'",'"'], "", $string);
		$c = "{$this->pythonPath} {$path} '{$safeString}'";
		$return = exec($c);
        return $return;
    }

	/** 
	 * 调用生成向量数组匹配对应关系
	 */
	function getEmbeddingsNew($arr) {
		$input = json_encode(['input' => $arr]);

		$embeddings = new \app\common\library\Embeddings;
		$embeddingsStr = $embeddings->run($input);
		$embeddingsArr = json_decode($embeddingsStr, true);

		// 检查 data 键是否存在
		if (!isset($embeddingsArr['data'])) {
			usleep(500000); // 停顿 0.5 秒
			$embeddingsStr = $embeddings->run($input); // 重试
			$embeddingsArr = json_decode($embeddingsStr, true);
		}

		return $embeddingsArr['data'] ?? []; // 如果 data 不存在，返回空数组或其他默认值
	}


	/** 查询向量库 */
	private function queryVector($project, $text)
	{
		$list = [];

		$query = $this->pg_connect();
		$text = mb_strcut($text, 0, 384);
		if($this->debug){
			dump($text);
		}
		if($text)
		{
			$vectorArr = $this->getEmbeddingsNew([$text]);
			$vectorContent = json_encode(['content'=>$vectorArr[0]['embedding']]);

			$vectorList = json_decode($vectorContent,true);
			if(!empty($vectorList['content']))
			{
				$queryVector = json_encode($vectorList['content']);
				$query->query("SET hnsw.ef_search = 100;");
				$sql = "SELECT id, file_id, content, 1 - (embedding <=> '{$queryVector}') AS cosine_similarity FROM {$this->pgTable} where console_id  in ({$project['console_ids']}) ORDER BY embedding <=> '{$queryVector}' LIMIT 20";
				if($this->debug){
					dump($sql);
				}
				$list = $query->query($sql);
			}
		}

		return $list;
    }



	/**
	 * 重排引用内容
	 */
	private function reorderReferences($references, $msg = '')
	{
		$batchSize = 40;
		$path = '/mnt/sdc/wwwroot/ai-master/python/qianwen/rerank.py';
		$msgEscaped = str_replace(['"', "'"], ['“', "’"], $msg?$msg:$this->msg);

		$sortedReferences = []; // 存储排序结果（引用键 => relevance_score）
		$unusedKeys = [];       // 未参与排序的 keys（没有出现在 rerank 结果中）

		$batches = array_chunk($references, $batchSize, true);
		foreach ($batches as $batch) {
			$vectorArr = [];
			$keyMap = [];

			foreach ($batch as $originalKey => $v) {
				$content = mb_substr((isset($v['title'])?$v['title'].":":'').$v['content'], 0, 2000);
				$escapedContent = str_replace(['"', "'", "(", ")"], ['“', "’", "（", "）"], $content);
				$vectorArr[] = $escapedContent;
				$keyMap[] = $originalKey; // 映射 index => 原始 key
			}
			if($this->debug){
				dump($keyMap);
			}

			$vectorStr = implode('@@', $vectorArr);
			$cmd = $this->pythonPath . ' ' . $path . ' "%s" "%s" ';
			$return = [];
			exec(sprintf($cmd, $msgEscaped, $vectorStr), $return, $returnCode);
			$usedKeys = [];

			if (!empty($return[0])) {
				$arr = json_decode($return[0], true);
				if (!empty($arr['output']['results'])) {
					foreach ($arr['output']['results'] as $result) {
						$indexInBatch = $result['index'];
						if(isset($keyMap[$indexInBatch]))
						{
							$key = $keyMap[$indexInBatch];
							$sortedReferences[] = [
								'key' => $key,
								'relevance_score' => $result['relevance_score']
							];
							$usedKeys[] = $key;
						}
					}
				}
			}

			// 剩下没被返回的 key，加入 unused 列表
			$unusedKeys = array_merge($unusedKeys, array_diff($keyMap, $usedKeys));
		}

		// 根据排序结果重建 references 顺序
		$final = [];

		if($this->debug){
			dump($sortedReferences);
		}
		foreach ($sortedReferences as $item) {
			$key = $item['key'];
			$references[$key]['relevance_score'] = $item['relevance_score'];
			$final[] = $references[$key];
		}

		// 追加未排序的
		foreach ($unusedKeys as $key) {
			$references[$key]['relevance_score'] = 0;
			$final[] = $references[$key];
		}
		// 按 relevance_score 倒序排序
		usort($final, function ($a, $b) {
			return ($b['relevance_score'] ?? 0) <=> ($a['relevance_score'] ?? 0);
		});

		return $final;
	}

	/**
	 * 获取相邻切片以恢复完整内容
	 * @param array $slices 原始切片结果
	 * @param object $query 数据库连接
	 * @param array $project 项目信息
	 * @return array 增强后的切片结果
	 */
	private function retrieveAdjacentSlices($slices, $query, $project)
	{
		$enhancedSlices = [];
		$processedIds = [];
		
		foreach ($slices as $slice) {
			// 如果已处理过该切片，跳过
			if (in_array($slice['id'], $processedIds)) {
				continue;
			}
			
			$processedIds[] = $slice['id'];
			
			// 检查是否包含关键词，表明内容可能被截断
			$isIncomplete = $this->isIncompleteContent($slice['content']);
			
			// 如果内容看起来不完整或包含步骤关键词，尝试获取相邻切片
			if ($isIncomplete || $this->containsStepKeywords($slice['content'])) {
				// 直接查询相邻ID的切片（前后各2个）
				$adjacentSlices = $query->query("
					SELECT id, content, file_id
					FROM vector_data_upgrades
					WHERE file_id = {$slice['file_id']}
					AND console_id IN ({$project['console_ids']})
					AND delete_time IS NULL
					AND id BETWEEN {$slice['id']} - 1 AND {$slice['id']} + 1
					ORDER BY id ASC
				");
				
				// 如果找不到相邻切片，使用原始切片
				if (empty($adjacentSlices)) {
					$enhancedSlices[] = $slice;
					continue;
				}
				
				// 合并相邻切片内容
				$combinedContent = '';
				foreach ($adjacentSlices as $adjacentSlice) {
					$combinedContent .= $adjacentSlice['content'] . "\n";
					// 将处理过的切片ID添加到已处理列表
					$processedIds[] = $adjacentSlice['id'];
				}
				
				// 创建新的合并切片
				$newSlice = $slice;
				$newSlice['content'] = $this->cleanupCombinedContent($combinedContent);
				$newSlice['is_combined'] = true;
				
				$enhancedSlices[] = $newSlice;
			} else {
				$enhancedSlices[] = $slice;
			}
		}
		
		return $enhancedSlices;
	}

	/**
	 * 清理合并后的内容，去除重复段落和格式化
	 * @param string $content 合并后的内容
	 * @return string 清理后的内容
	 */
	private function cleanupCombinedContent($content)
	{
		// 按行分割
		$lines = explode("\n", $content);
		$uniqueLines = [];
		$seenLines = [];
		
		foreach ($lines as $line) {
			$trimmedLine = trim($line);
			if (empty($trimmedLine)) continue;
			
			// 使用MD5作为行内容的唯一标识
			$lineHash = md5($trimmedLine);
			
			// 如果这行内容之前没见过，添加到结果中
			if (!isset($seenLines[$lineHash])) {
				$uniqueLines[] = $trimmedLine;
				$seenLines[$lineHash] = true;
			}
		}
		
		// 重新组合内容
		return implode("\n", $uniqueLines);
	}

	/**
	 * 判断内容是否不完整
	 * @param string $content 切片内容
	 * @return bool 是否不完整
	 */
	private function isIncompleteContent($content)
	{
		// 检查内容是否以数字+括号结尾，表示可能被截断的步骤
		if (preg_match('/\d+[\)）]\s*$/', $content)) {
			return true;
		}
		
		// 检查内容是否以不完整的句子结尾
		if (preg_match('/[,;，；]\s*$/', $content)) {
			return true;
		}
		
		// 检查是否以数字结尾，可能是被截断的编号
		if (preg_match('/\d+\s*$/', $content)) {
			return true;
		}
		
		// 检查是否包含"步骤"、"开车"、"停车"等关键词，但内容较短
		if ($this->containsStepKeywords($content) && mb_strlen($content) < 300) {
			return true;
		}
		
		// 检查是否包含序号但数量少于3个，表示可能是不完整的列表
		preg_match_all('/\d+[\)）]/', $content, $matches);
		if (!empty($matches[0]) && count($matches[0]) < 3) {
			return true;
		}
		
		// 检查是否包含数值范围表达式但被截断
		if (preg_match('/~\s*\d+(\.\d+)?[a-zA-Z]*\s*$/', $content)) {
			return true;
		}
		
		// 检查是否包含单位但没有后续内容，可能是被截断的测量值
		if (preg_match('/\d+(\.\d+)?\s*[a-zA-Z]+\s*$/', $content)) {
			return true;
		}
		
		// 检查是否包含开车/停车步骤但步骤数量少于预期
		if ($this->containsStepKeywords($content)) {
			preg_match_all('/\d+[\)）]/', $content, $stepMatches);
			$lastStepNumber = !empty($stepMatches[0]) ? (int)preg_replace('/[^\d]/', '', end($stepMatches[0])) : 0;
			
			// 如果最后一个步骤编号小于10且内容不长，可能是不完整的
			if ($lastStepNumber > 0 && $lastStepNumber < 10 && mb_strlen($content) < 800) {
				return true;
			}
		}
		
		return false;
	}
	
	/**
	 * 检查内容是否包含步骤相关关键词
	 * @param string $content 切片内容
	 * @return bool 是否包含步骤关键词
	 */
	private function containsStepKeywords($content)
	{
		$keywords = ['步骤', '开车', '停车', '操作', '流程', '顺序', '方法', '程序', '启动', '关闭'];
		foreach ($keywords as $keyword) {
			if (strpos($content, $keyword) !== false) {
				return true;
			}
		}
		return false;
	}
	
	/**
	 * 判断两个切片是否是连续的步骤
	 * @param string $prevContent 前一个切片内容
	 * @param string $nextContent 后一个切片内容
	 * @return bool 是否是连续步骤
	 */
	private function isSequentialStep($prevContent, $nextContent)
	{
		// 提取前一个切片中的最后一个步骤编号
		preg_match_all('/(\d+)[\)）]/', $prevContent, $prevMatches);
		// 提取后一个切片中的第一个步骤编号
		preg_match('/(\d+)[\)）]/', $nextContent, $nextMatch);
		
		if (!empty($prevMatches[1]) && !empty($nextMatch[1])) {
			$lastPrevNum = (int)end($prevMatches[1]);
			$firstNextNum = (int)$nextMatch[1];
			
			// 如果编号连续，则认为是连续步骤
			if ($firstNextNum == $lastPrevNum + 1) {
				return true;
			}
		}
		
		// 检查是否有其他连续性指标
		// 例如，前一个切片以不完整的句子结尾，后一个切片是其延续
		if (preg_match('/[,;，；]\s*$/', $prevContent)) {
			return true;
		}
		
		return false;
	}

	/**
	 * 处理查询结果
	 */
	private function processResults($results)
	{
		foreach ($results as &$result) {
			// 如果存在QA信息，增强内容
			if (!empty($result['extracted_question']) && !empty($result['extracted_answer'])) {
				$result['content'] = sprintf(
					"问题：%s\n回答：%s\n原文：%s",
					$result['extracted_question'],
					$result['extracted_answer'],
					$result['content']
				);
			}
			
			// 清理不需要的字段
			unset($result['extracted_question'], $result['extracted_answer']);
		}
		return $results;
	}

	/** 相似问生成 */
    public function similar_question($string){

        $path = ' /mnt/sdc/wwwroot/ai-master/python/appbuilder/similar_question.py "%s" ';
        $c = $this->pythonPath . ' ' . $path;
        // dump(sprintf($c, $string));exit;
        exec(sprintf($c, "请根据用户提问题的问题生成三个追问问题，不要前面的序号；问题：{$string}"), $return);
		$result = array_slice($return, 0, 3);
		foreach($result as $k=>$v){
			$result[$k] = substr($v, 3);
		}
        return $result;
    }

	/** 链接信息获取 */
    public function get_url($url, $internal = false){

        $path = ' /mnt/sdc/wwwroot/ai-master/python/collect/main.py "%s" %s ';
        $c = $this->pythonPath . ' ' . $path;
        // dump(sprintf($c, $url, $internal?'--internal':''));exit;
        $return = exec(sprintf($c, $url, $internal?'--internal':''));
		// dump($return);exit;
        return $return;
    }


	public function addConsoleUrl($url_id, $url, $internal, $user_id, $console_id) 
	{
		try {
			# 删除旧文件
			$userData = UrlData::where('url_id',$url_id)->select();
			if($userData){
				foreach ($userData as $value) {
					#  删除文件
					$item = \app\admin\model\ai\ConsoleFile::where('file_number',$value['file_number'])->find();
					if ($item) {
						\app\admin\model\ai\ConsoleFile::where('id', $item['id'])->update(['deletetime'=>time()]);
						$this->deleteConsoleFile($value['file_number'], $item);
					}
				}
				# 删除子链接
				UrlData::where('url_id',$url_id)->update(['deletetime'=>time()]);
			}

			$urlContent = $this->get_url($url, $internal);
			$urlList = json_decode($urlContent,true);
			if(empty($urlList['results'])){
				\app\admin\model\ai\UserUrl::where("id", $url_id)->update(['status' => 2, 'error_msg'=> '未获取到数据信息', 'updatetime' => time()]);
				return false;
			}
			$insert = [];
			foreach($urlList['results'] as $v){
				if(empty($v['page_content']) || strlen($v['page_content'])<500 || strpos($v['page_title'], 'No')!==false)continue;
				$file_name = '/uploads/'.date("Ymd").'/' . md5($v['url']).'.txt';
				$file_path = ROOT_PATH . "public" . $file_name;
				// dump(dirname($file_path));exit;
				if (!is_dir(dirname($file_path))) {
					mkdir(dirname($file_path), 0755, true);
				}
				file_put_contents($file_path, $v['page_content']);
				$file_size = filesize($file_path);
				$md5_file = md5_file($file_path);
				$file_number = $this->generateUUIDv4();
				$file_insert = [
					'console_id' => $console_id,
					'file_number' => $file_number,
					'name' => $v['page_title'].".txt",
					'user_id' => $user_id,
					'createtime' => time(),
					'updatetime' => time(),
					'display_status'=> 0,
					'file' => $file_name,
					'type' => 5,
					'file_size' => $file_size,
					'file_md5' => $md5_file,
				];

				$insert[] = [
					'url_id' => $url_id,
					'file_number' => $file_number,
					'title' => $v['page_title'],
					'content' => $v['page_content'],
					'content_length' => $v['content_length'],
					'url' => $v['url'],
					'createtime' => time(),
					'updatetime' => time(),
					'status' => 1,
				];
				$console_file_id = \app\admin\model\ai\ConsoleFile::insertGetId($file_insert);
				
				//推入训练队列中
				Queue::push('app\queue\job\UploadFileToConsole', [
					'url' => request()->domain() . $file_name,
					'console_number' => $file_number,
					'console_file_id' => $console_file_id,
					'filename' => $v['page_title'],
					'engine_type' => 'local',
					'user_id' => 1
				], config('task.file'));
			}
			if($insert){
				\app\admin\model\ai\UrlData::insertAll($insert);
				// $update = ['status' => 1, 'error_msg'=> '训练成功', 'updatetime' => time()];
				$return = true;
			}else{
				$update = ['status' => 2, 'error_msg'=> '训练失败', 'updatetime' => time()];
				$return = false;
				\app\admin\model\ai\UserUrl::where("id", $url_id)->update($update);
			}
			return $return;

        } catch (ValidateException|PDOException|Exception $e) {
			error_log(date("Y-m-d H:i:s")."|getMessage:".print_r($e->getMessage(),1)."\r\n",3,ROOT_PATH."/runtime/log/".date("Ym")."/addConsoleUrl.log");
        }
	}

	/** 
	 * 卡片、用户卡片、问答对、场景热点向量数据存入 pgsql
	 * type类型:1=卡片,2=场景热点,3=用户卡片,4=问答对
	 */
	public function addVectorData($data){
		try {
			$query = $this->pg_connect();

			$sql  = "INSERT INTO public.card_data (robot,type,card_id,content,embedding,createtime,updatetime,status,console_id) VALUES ";
			$time = time();
			$sql1 = "";
			foreach($data as $v){
				if($v['type']==3)
				{
					$old = $query->query("select * from public.card_data where robot='{$v['robot']}' and type='{$v['type']}' and card_id='{$v['card_id']}' and status='{$v['status']}' and content='{$v['content']}' limit 1");
				}else{
					$old = $query->query("select * from public.card_data where robot='{$v['robot']}' and type='{$v['type']}' and card_id='{$v['card_id']}' and status='{$v['status']}' limit 1");
				}
				if(!empty($old)){
					$query->execute("update public.card_data set content='{$v['content']}',embedding='{$v['embedding']}',updatetime={$time},console_id='{$v['console_id']}' where id='{$old[0]['id']}'");
					continue;
				}
				$sql1 .= "('{$v['robot']}','{$v['type']}','{$v['card_id']}','{$v['content']}','{$v['embedding']}',{$time},{$time},{$v['status']},{$v['console_id']}),";
			}
			if($sql1)
			{
				$sql1 = rtrim($sql1,',');
				$query->execute($sql . $sql1);
			}
			return true;
        } catch (ValidateException|PDOException|Exception $e) {
			error_log(date("Y-m-d H:i:s")."|getMessage:".print_r($e->getMessage(),1)."\r\n",3,ROOT_PATH."/runtime/log/".date("Ym")."/addVectorData.log");
        }
	}

	/**
	 * 删除 卡片、用户卡片、问答对、场景热点向量数据

	 */
	public function deleteVectorData($type, $ids){
		try {
			$query = $this->pg_connect();
			
            $query->table(config('postgres.database').'.public.card_data')->where(['type'=>$type, 'card_id'=>['in', explode(',', $ids)]])->delete();
			return true;
		} catch (ValidateException|PDOException|Exception $e) {
			error_log(date("Y-m-d H:i:s")."|getMessage:".print_r($e->getMessage(),1)."\r\n",3,ROOT_PATH."/runtime/log/".date("Ym")."/deleteVectorData.log");
		}
	}

	/** 卡片向量查询 */
	public function selectVectorData($robot, $type, $text, $matchingScore, $lang, $console_ids){
		$this->msg = $text;
		if($type==1 && $lang=='en')
		{
			/** 未配置英文卡片，转换为中文匹配卡片 */
			$cardNumEn = \app\admin\model\ai\Card::where(['robot' => $robot, 'lang'=> 'en'])->count();
			if($cardNumEn<1){
				$text = $this->translateMessage($text, $lang);
			}
		}
		$typeArr = $type==4?[$type]:[1,2,3];
		$limit = $type==4?1:10;
		$query = $this->pg_connect();

		if(empty($this->vectorContent))
		{
			$text = mb_strcut($text, 0, 384);
			$vectorArr = $this->getEmbeddingsNew([$text]);

			$this->vectorContent = json_encode(['content'=>$vectorArr[0]['embedding']]);
		}

		$vectorList = json_decode($this->vectorContent,true);
		$return = [];
		if(!empty($vectorList['content']))
		{
			$queryVector = json_encode($vectorList['content']);

			// 使用 DISTINCT ON 语法进行去重，按相似度排序
			$sql = "
				WITH ranked AS (
					SELECT 
						id, 
						content, 
						type,
						card_id,
						1 - (embedding <=> '{$queryVector}') AS cosine_similarity,
						ROW_NUMBER() OVER (PARTITION BY type, card_id ORDER BY embedding <-> '{$queryVector}') AS rn
					FROM public.card_data 
					WHERE (robot = " . $robot . " OR console_id IN (" . $console_ids . ") )
					AND type IN (" . implode(',', $typeArr) . ")
				)
				SELECT id, content, type, card_id, cosine_similarity
				FROM ranked
				WHERE rn = 1
				ORDER BY cosine_similarity DESC
				LIMIT {$limit}
			";
			if($this->debug){
				dump($sql);
			}
			$list = $query->query($sql);
			
			switch ($type) {
				case '4':
					if(!empty($list) && $list[0]['cosine_similarity']>$matchingScore)
					{
						$return = \app\admin\model\ai\Multimodel::where('id', $list[0]['card_id'])->field('a,question')->find()->toArray();
					}
					break;
				
				default:
					foreach ($list as $key => $value) {
						if($value['cosine_similarity']>$matchingScore)
						{
							$content = '';
							$item = [];
							
							switch ($value['type']) {
								case '1':
									$cardContent = \app\admin\model\ai\Card::where(['id'=>$value['card_id'],'lang'=>$lang=='cn'?'zh':'en'])->value('a');
									if ($cardContent) {
										// 解析Markdown格式 [![标题](图片链接)](内容链接)
										preg_match('/\[\!\[(.*?)\]\((.*?)\)\]\((.*?)\)/', $cardContent, $matches);
										if (isset($matches[1]) && isset($matches[2]) && isset($matches[3])) {
											$item = [
												'content' => $matches[1],
												'image' => $matches[2],
												'url' => $matches[3],
												'type' => 'card',
												'score' => $value['cosine_similarity'],
												'a' => $cardContent,
												'title' => $cardContent
											];
										} else {
											$item = [
												'content' => '卡片内容',
												'content' => $cardContent,
												'type' => 'card',
												'score' => $value['cosine_similarity'],
												'a' => $cardContent,
												'title' => $cardContent
											];
										}
									}
									break;
									
								case '2':
									$vrhot = \app\admin\model\ai\Vrhot::where('id', $value['card_id'])->find();
									if ($vrhot) {
										$image = $vrhot['image'] ? $vrhot['image'] : 'https://aimaster.jw100.com.cn/assets/img/pdf_image.jpg';
										$content = "[![{$vrhot['tag']}]({$image})]({$vrhot['url']})";
										$item = [
											'content' => $vrhot['tag'],
											'image' => $image,
											'url' => $vrhot['url'],
											'type' => 'vrhot',
											'score' => $value['cosine_similarity'],
											'a' => $content,
											'title' => $content
										];
									}
									break;
									
								case '3':
									$usercard = \app\admin\model\ai\UserCard::where(['id'=>$value['card_id'],'console_id'=>['in', explode(",", $console_ids)]])->find();
									if ($usercard) {
										$content = "[![{$usercard['name']}]({$usercard['cover']})]({$usercard['file']})";
										$item = [
											'content' => $value['content'],
											'image' => $usercard['cover'],
											'url' => $usercard['file'],
											'type' => 'usercard',
											'score' => $value['cosine_similarity'],
											'a' => $content,
											'title' => $content
										];
									}
									break;
							}
							
							if (!empty($item)) {
								$return[] = $item;
							}
						}
					}
					$return = $this->reorderReferences($return);
					if($this->debug){
						dump($return);exit;
					}
					$return = array_slice($return, 0, 5);
					foreach($return as $key => $value) {
						if($value['relevance_score']<$matchingScore){
							unset($return[$key]);
						}
					}
					break;
			}
		}

		return $return;
	}

	private function translateMessage($msg, $lang) {
		if ($lang && $lang != 'cn') {
			$translation = new \app\common\library\Translation();
			return $translation->xfyun($msg, $lang, 'cn');
		}
		return $msg;
	}



	/*
	* 非流式调用模型对话，直接返回结果
	*/
	public function chatAndReturn($modelId,$conversation_id,$msg,$robot)
	{
		# 获取项目信息
		$project = Db::name('ai_project')->where('id',$robot)->find();
		$console_ids = $project['console_ids'];

		# 获取模型信息
		$bigmodel = Db::name('ai_bigmodel')->where('id', $project['model_id'])->find();
		$this->url = $bigmodel['api_url'];
		$this->token = $bigmodel['api_key'];
		$this->model = $bigmodel['model'];
		$this->paramsType = in_array($this->model, $this->paramsTypeArr);

		/** 上下文 */
		$messages = [];
		// if(!in_array($this->model, $this->contextCacheArr)){
		// 	$msgList = Msg::where('chatId',$contextId)->order('id', 'desc')->limit(3)->select();
		// 	foreach($msgList as $item){
		// 		$messages[] = [
		// 			"role" => "user",
		// 			"content" => $item['msg']
		// 		];
		// 		$content = mb_substr($item['content'], 0, 50);
		// 		$messages[] = [
		// 			"role" => "assistant",
		// 			"content" => $content
		// 		];
		// 	}
		// }
		$messages[] = [
			"role" => "user",
			"content" => $msg
		];

		# 查询切片
		// $vectorList = $this->queryVector($console_ids, $msg);
		// dump($vectorList);exit;
		// $vectorStr = '';
		// $vectorId = 1;
		// // $this->references = [];
		// foreach($vectorList as $item){

		// 	if (count($this->references) > 2) {
		// 		break;
		// 	}

		// 	if($item['cosine_similarity']>0.3){
		// 		$vectorStr .= "[{$vectorId}] " . $item['content'] . "\n";
		// 		$vectorId++;
		// 		$consoleFile = Db::name('ai_console_file')->where('id',$item['file_id'])->find();
		// 		$slice = $robot == 61? '' : $item['content'];
		// 		$this->references[] = [
		// 			'title' => $consoleFile['name'] ?? '',
		// 			'content' => $slice,
		// 			'file' => !empty($consoleFile['file']) ? cdnurl($consoleFile['file'], true) : ''
		// 		];
		// 	}
		// }
		$systemPrompt = $project['system_prompt'] ?? '';

		# 区分上下文关系，是否需要引用上文内容
		$systemPrompt = "首先需要分析用户问题是否和上文相关，如果无相关联，请不要引用上文内容，直接回答用户问题。" . $systemPrompt;
        // if ($lang && $lang != 'cn') {
        //     $systemPrompt .= "前提：请用英文回答，不能使用汉字。不要复述问题。直接的回答。" ;
        // }

		// /** 系统提示词 */
		// if($vectorStr)
		// {
		// 	$messages[] = [
		// 		"role" => "system",
		// 		"content" => <<<jjj
		// 			{$systemPrompt}
		// 			请基于以下内容整理并回答用户问题；如果下述内容中无相关信息，请自行作答。回答时不得展示或引用任何下述内容内部信息，且仅在最终答案末尾标注。
					
		// 			要求：
		// 			1. 回答内容只针对用户问题，不展示提示词或下述内容内部信息。
		// 			2. 内部推理过程仅用于生成答案，最终回答中不包含任何内部思考细节或提示词内容。
		// 			3. 出现图片、视频等信息时，请忽略相关内容。
		// 			4. 回答仅依据问题核心进行独立分析，不依赖或重复提示词说明。
		// 			----
		// 			{$vectorStr}
		// 			----
		// 		jjj
		// 	];
		// } else if($systemPrompt) {
		// 	$messages[] = [
		// 		"role" => "system",
		// 		"content" => <<<jjj
		// 			{$systemPrompt}
		// 		jjj
		// 	];
		// }

		// dump($messages);


		// $model = "deepseek-r1";
		$stream = false;
		$maxTokens = 2096;
		$stop = ["null"];
		$temperature = 0.3;
		$topP = 0.3;
		$topK = 50;
		$frequencyPenalty = 1;
		$n = 1;
		$responseFormat = ["type" => "text"];
		$tools = [
			[
				"type" => "function",
				"function" => [
					"description" => "<string>",
					"name" => "<string>",
					"parameters" => [],
					"strict" => false
				]
			]
		];

		$payload = [
			"model" => $this->model,
			"messages" => $messages,
			"stream" => $stream,
			"max_tokens" => $maxTokens,
			"stop" => $stop,
			"temperature" => $temperature,
			"top_p" => $topP,
			"top_k" => $topK,
			"frequency_penalty" => $frequencyPenalty,
			"n" => $n,
			"response_format" => $responseFormat,
			"tools" => $tools,
			"usage" => [
				"prompt_tokens" => 3019,
				"completion_tokens" => 104,
				"total_tokens" => 3123,
				"prompt_tokens_details" => [
					"cached_tokens" => 2048
				]
			]
			// "tools" => $tools
		];
		if($this->paramsType){
			$payload = array_merge($payload,[
				"parameters" => [
					"result_format" => "message",
					"incremental_output" => true
				],
				"input" => [
					"messages" => $messages
				]
			]);
		}

		// dump($payload);die;

		$headers = [
			'Content-Type:application/json',
			'Authorization:Bearer ' . $this->token
		];

		if($this->paramsType){
			$headers = array_merge($headers,[
				'X-DashScope-SSE:enable'
			]);
		}

		$answerStr = '';
		$ip = request()->ip();
		$area = IpService::getArea($ip);
		$params = json_encode($payload);
		// dump($payload);
		// dump($headers);
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $this->url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		
		$response = curl_exec($ch);
		if (curl_errno($ch)) {
		    echo 'Curl error: ' . curl_error($ch);
		}
		curl_close($ch);

		$res = json_decode($response,true);
		if (!$res) {
            exception('接口响应错误:' . $response);
		}
		// if($this->paramsType){
		// 	$text = $res['output']['choices'][0]['message']['content'] ?? '';
		// }else{
			$text = $res['choices'][0]['message']['content'] ?? '';
			// $reasoning_content = $res['choices'][0]['delta']['reasoning_content'] ?? '';
		// }
		return json(['answer' => $text,'references' => $this->references]);

		// return $text;
	}
}
