<?php

namespace app\admin\model\flow;

use think\Model;
use traits\model\SoftDelete;

class CaseList extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'flow_caselist';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [

    ];
    

    







    public function project()
    {
        return $this->belongsTo('app\admin\model\ai\Project', 'robot', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function category()
    {
        return $this->belongsTo('Category', 'category_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
