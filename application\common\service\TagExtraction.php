<?php
namespace app\common\service;
use think\Db;

/**
 * 标签提取
 */
class TagExtraction {

    /** 需要提取的段落 */
    public static function run($str, $robot, $msg_id) {
        $python = '/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/TagExtraction.py "%s"';
        $return = shell_exec(sprintf($python,$str));
        // $return = "Message(name=msg, content=1.原子吸收装置
        // 2.进样
        // 3.样品分析
        // 4.小智讲解
        // 5.原子吸收法
        // 6.样品处理
        // 7.分析技术
        // 8.原子吸收光谱
        // 9.样品检测
        // 10.原子吸收测定, mtype=dict, extra={}, token_usage={'prompt_tokens': 73, 'completion_tokens': 55, 'total_tokens': 128})";
        preg_match_all("/\d+\.(.*)[\n|,]/isU", $return, $matches);
        $messages = [];
        foreach ($matches[1] as $index => $name) {
            $messages[] = $name;
        }
        $insert = [];
		foreach($messages as $item){
			$tagId = Db::name('ai_tagextraction')->where(['robot'=>$robot,'name'=>$item])->value('id');
			if($tagId){
				Db::name('ai_tagextraction')->where(['id'=>$tagId])->inc('num')->update(['msg_id'=>$msg_id,'updatetime'=>time()]);
			}else{
				$insert[] = [
					'robot'=>$robot,
					'name'=>$item,
					'num'=>1,
                    'createtime'=>time(),
                    'updatetime'=>time(),
                    'msg_id'=>$msg_id
				];
			}
		}
		if($insert){
			Db::name('ai_tagextraction')->insertAll($insert);
		}
        return $messages;
    }

    /** 切片信息-需要提取的标签 */
    public static function run_slice($str, $robot, $pid) {
        $python = '/mnt/sdc/wwwroot/ai-master/app-builder-master/38359869ba9082734816c52c0dc8a8e6_venv/bin/python3.9 /mnt/sdc/wwwroot/ai-master/TagExtraction.py "%s"';
        $return = shell_exec(sprintf($python,$str));
        // $return = "Message(name=msg, content=1.原子吸收装置
        // 2.进样
        // 3.样品分析
        // 4.小智讲解
        // 5.原子吸收法
        // 6.样品处理
        // 7.分析技术
        // 8.原子吸收光谱
        // 9.样品检测
        // 10.原子吸收测定, mtype=dict, extra={}, token_usage={'prompt_tokens': 73, 'completion_tokens': 55, 'total_tokens': 128})";
        preg_match_all("/\d+\.(.*)[\n|,]/isU", $return, $matches);
        $messages = [];
        foreach ($matches[1] as $index => $name) {
            $messages[] = $name;
        }
        $insert = [];
		foreach($messages as $item){
			$tagId = Db::name('ai_file_slice_tag')->where(['robot'=>$robot,'name'=>$item])->value('id');
			if($tagId){
				Db::name('ai_file_slice_tag')->where(['id'=>$tagId])->update(['pid'=>$pid,'updatetime'=>time()]);
			}else{
				$insert[] = [
					'robot'=>$robot,
					'name'=>$item,
                    'createtime'=>time(),
                    'updatetime'=>time(),
                    'pid'=>$pid
				];
			}
		}
		if($insert){
			Db::name('ai_file_slice_tag')->insertAll($insert);
		}
        return $messages;
    }
}