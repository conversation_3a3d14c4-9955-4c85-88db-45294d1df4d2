<?php

namespace app\queue\job;

use app\common\service\ai\AiService;

use think\Queue\Job;
use think\Db;


class UrlToConsole
{


    /**
     *
     * 链接地址训练存入知识库
     * 
     **/
    public function fire(Job $job, $data)
    {


        $url_id = $data['url_id'];
        $url = $data['url'];
        $internal = $data['internal'];
        $user_id = $data['user_id'];
        $console_id = $data['console_id'];

        //重试超过3次
        if ($job->attempts() > 3) {
            //通过这个方法可以检查这个任务已经重试了几次了
            $job->delete();
        }


        $engine = AiService::getEngine('local');
        $res = $engine->addConsoleUrl($url_id, $url, $internal, $user_id, $console_id);
        echo $res;
        echo '======执行成功======' . "\n";
        echo '执行结果：' . $res . "\n";
        echo date('Y-m-d H:i:s') . "\n";
            
        $job->delete();

    }


    public function failed($data){
        // ...任务达到最大重试次数后，失败了
    }
}