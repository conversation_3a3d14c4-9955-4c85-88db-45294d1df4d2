<?php
namespace app\api\controller\ai\v2;

use app\common\controller\Api;
use think\Db;
use app\common\service\ai\AiService;
use app\common\service\IpService;


class Index extends Api
{
	// 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];
    
	protected $lang = 'cn';
	
	// 实现调用AppBuilder接口并进行流式输出
    public function callStreamApi()
    {
        header("Access-Control-Allow-Origin: *");
		header('Content-Type: text/event-stream; charset=utf-8');
      	header('Cache-Control: no-cache');
      	header('Connection: keep-alive');
      	header('X-Accel-Buffering: no');
        
		$postData = file_get_contents("php://input");
        $postDataArr = json_decode($postData);
        $robot = $postDataArr->robot;
        $this->lang = isset($postDataArr->lang) ? $postDataArr->lang : '';
		$chatId = $postDataArr->chatId;
		$card = $postDataArr->card;
        $question = isset($postDataArr->prompt) ? $postDataArr->prompt : "";
        $uid = isset($postDataArr->uid) ? $postDataArr->uid : "";
		$image = isset($postDataArr->image) ? $postDataArr->image : ""; # 图片

		$group_id = 2;

        $model = isset($postDataArr->model) ? $postDataArr->model : ""; # 模型
        $console_type = isset($postDataArr->console_type) ? $postDataArr->console_type : ""; # 搜索范围

        $project = db::name('ai_project')->where('id',$robot)->field('id,type,console_key,enhance_status')->find();
        $console_key = $project['console_key'];

        if(empty($console_key)){
            $console_key = "8e36c365-f419-488c-8002-9789d4dcbaee";
        }


		if($uid>0)
		{
			$group_id = Db::name('user')->where(['id'=>$uid])->value('group_id');
			$chatRow = \app\admin\model\ai\ChatList::where(['robot'=>$robot, 'uid'=>$uid,'chatId'=>$chatId])->find();
			if(empty($chatRow))
			{
				$TitlePredictor = new \app\common\library\aliyun\TitlePredictor;
				$arr = $TitlePredictor->generateTitles($question);
				$title = $arr[0] ?? '';
				$insert = [
					'robot' => $robot,
					'uid' => $uid,
					'title' => $title,
					'chatId' => $chatId,
					'createtime' => time(),
					'updatetime' => time(),
				];
				\app\admin\model\ai\ChatList::insert($insert);
			}
		}

        if(false && $console_type == 2)
        {
            # 联网查询
			$SearchService = new \app\common\service\ai\engine\Search();
			$model = 'deepseek-r1' == $model ? 'deepseek-r1' : 'deepseek-v3';
			$SearchService->chat($model, $question, $chatId, $robot, $card, $this->lang);
        }else if($console_type == 3)
        {
            # 转人工
            $ip = request()->ip();

            $area = IpService::getArea($ip);
			$msg = mb_substr($question, 0, 150);
			Db::name('ai_msg')
			->insertGetId([
				'robot'=>$robot, 
				'chatId'=> $chatId, 
				'msg'=>$msg, 
				'time'=>time(), 
				'ip'=> $ip, 
				'content'=>'',
				'city'=>$area, 
				'card'=>$card,
				'lang'=>$this->lang,
				'answer_range' => 3,
			]);
			$r = json_encode(['text' => "已收到您的消息，请耐心等待回复！", 'reasoning_content'=> '','references' => ''],JSON_UNESCAPED_UNICODE);
			$this->sendMessage($chatId,$r);
			exit;
        }else{
            if ($console_type == 2 && strpos($question, '当前最热门的问题是什么') !== false) {
                $return = $this->handleHotQuestion($robot);
                $r = json_encode(['text' => $return['answer'], 'reasoning_content'=> '','references' => ''],JSON_UNESCAPED_UNICODE);
                $this->sendMessage($chatId,$r);
                exit;
            }

			# 优化模型回复
			$enhanceType = in_array($robot,[61]) ? 'professional' : ($project['enhance_status']==1 ? 'enhance' : 'basic');
			$ai = \app\common\service\ai\engine\LocalEngineFactory::create($enhanceType);
			$ext = ['image' => $image];
			$ai->chat($console_type, $chatId, $question, $robot, $card, $this->lang, $model, false, $ext);


            # 知识库回复
            // if(in_array($robot,[61]))
            // {
            //     $aiEngine = AiService::getEngine('localprofessional');
            // }else if($project['enhance_status']==1)
            // {
            //     $aiEngine = AiService::getEngine('localenhance');
            // }else{
            //     $aiEngine = AiService::getEngine($project['type']);
            // }
			// $aiEngine->group_id = $group_id;
            // $aiEngine->chat($console_type,$chatId,$question,$robot,$card,$this->lang,$model, false,['image' => $image]);
        }
        exit;
    }

    private function handleHotQuestion($robot) {
		$questionNumAll = Db::name('ai_msg')->where(['robot' => $robot])->count();
		$tagRow = \app\admin\model\ai\Tagextraction::where('robot', $robot)->order('num', 'desc')->find();
		if ($tagRow) {
			$question = $tagRow['name'];
			$questionNum = $tagRow['num'];
		} else {
			$res = $this->questionHot($robot);
			$question = $res['q'];
			$questionNum = $res['number'];
		}
		$answer = "当前已进行的问答次数：{$questionNumAll}次，其中最常被问到的问题是：“{$question}”相关的问题，这类似的问题已经被问到了{$questionNum}次";
		$return['answer'] = $this->translateMessage($answer);
        return $return;
	}

    private function translateMessage($msg) {
		if ($this->lang && $this->lang != 'cn') {
			$translation = new \app\common\library\Translation();
			return $translation->xfyun($msg, $this->lang, 'cn');
		}
		return $msg;
	}

    /**
	 * 获取最常问题
	 */
	function questionHot($robot){
		switch ($robot) {
			case 11:
				$tags = ['气相色谱','液相色谱','原子吸收'];
				break;
			case 16:
				$tags = ['细胞实验'];
				break;
			case 3:
				$tags = ['流体力学实验操作'];
				break;
			case 5:
				$tags = ['压片机的结构'];
				break;
			case 19:
				$tags = ['拜耳法'];
				break;
			case 2:
				$tags = ['人参的功效是什么'];
				break;
			default:
				$tags = ['气相色谱','液相色谱','原子吸收'];
				break;
		}
		$result = [];
		foreach($tags as $item){
			$questionNum = Db::name('ai_msg')->where(['robot'=>$robot,'msg'=>['like',"%{$item}%"]])->count();
			$questionNum += 10;
			$result[] = ['q'=>$item,'number'=>$questionNum];
		}
		usort($result, function($a, $b) {
			return $b['number'] - $a['number'];
		});
		return $result[0];
	}

    public function sendMessage($id, $data) {
        echo "id: $id" . PHP_EOL;
        echo "data: $data" . PHP_EOL;
        echo PHP_EOL;
        ob_flush();
        flush();
    }
    
}