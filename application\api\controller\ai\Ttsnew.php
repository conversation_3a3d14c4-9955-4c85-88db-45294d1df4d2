<?php
namespace app\api\controller\ai;

use app\common\controller\Api;
use think\Request;
use \think\Log;

class Ttsnew extends Api
{
	// 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];
    
    protected $appid = "8932835055";
    protected $token = "ITyoG6atrGgruSdb5kBSelNtBpW7X60B";
    protected $host = "openspeech.bytedance.com";
    protected $api_url = "wss://openspeech.bytedance.com/api/v1/tts/ws_binary";
    protected $default_header = "\x11\x10\x11\x00";

    public function testSubmit(Request $request)
    {
        $cluster = $request->get('cluster');
        $voice_type = $request->get('voice_type');
        $text = $request->get('text');
        $file_name = 'file.wav';
        $token = 'jiweiyuanhui';
        $reqid = uniqid();
        $uid = $reqid;

        $request_json = [
            "app" => [
                "appid" => $this->appid,
                "token" => $token,
                "cluster" => $cluster
            ],
            "user" => [
                "uid" => $uid
            ],
            "audio" => [
                "voice_type" => "S_nF63efa61",
                "encoding" => "mp3",
                "speed_ratio" => 1.0,
                "volume_ratio" => 1.0,
                "pitch_ratio" => 1.0,
            ],
            "request" => [
                "reqid" => $reqid,
                "text" => $text,
                "text_type" => "plain",
                "operation" => "submit"
            ]
        ];

        $submit_request_json = $request_json;
        $submit_request_json["audio"]["voice_type"] = $voice_type;
        $submit_request_json["request"]["reqid"] = uniqid();
        $submit_request_json["request"]["operation"] = "submit";

        $payload_bytes = gzencode(json_encode($submit_request_json));
        $full_client_request = $this->default_header . pack('N', strlen($payload_bytes)) . $payload_bytes;

        Log::info("request json: " . json_encode($submit_request_json));
        Log::info("request bytes: " . bin2hex($full_client_request));

        $file_to_save = fopen($file_name, "wb");
        $header = ["Authorization: Bearer; {$this->token}"];

        $context = stream_context_create([
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
            ]
        ]);

        $ws = stream_socket_client($this->api_url, $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);
        if (!$ws) {
            Log::error("Failed to connect: $errstr ($errno)");
            return json(['code' => 500, 'msg' => "Failed to connect: $errstr ($errno)"]);
        }

        fwrite($ws, implode("\r\n", $header) . "\r\n\r\n");
        fwrite($ws, $full_client_request);

        while (true) {
            $res = fread($ws, 2048);
            if (parseResponse($res, $file_to_save)) {
                fclose($file_to_save);
                break;
            }
        }

        Log::info("closing the connection...");
        fclose($ws);

        return json(['code' => 200, 'msg' => 'Success']);
    }

    public function testQuery(Request $request)
    {
        $cluster = $request->post('cluster');
        $voice_type = $request->post('voice_type');
        $token = $request->post('token');
        $uid = $request->post('uid');
        $reqid = $request->post('reqid');
        $text = $request->post('text');

        $request_json = [
            "app" => [
                "appid" => $this->appid,
                "token" => $token,
                "cluster" => $cluster
            ],
            "user" => [
                "uid" => $uid
            ],
            "audio" => [
                "voice_type" => "S_nF63efa61",
                "encoding" => "mp3",
                "speed_ratio" => 1.0,
                "volume_ratio" => 1.0,
                "pitch_ratio" => 1.0,
            ],
            "request" => [
                "reqid" => $reqid,
                "text" => $text,
                "text_type" => "plain",
                "operation" => "query"
            ]
        ];

        $query_request_json = $request_json;
        $query_request_json["audio"]["voice_type"] = $voice_type;
        $query_request_json["request"]["reqid"] = uniqid();
        $query_request_json["request"]["operation"] = "query";

        $payload_bytes = gzencode(json_encode($query_request_json));
        $full_client_request = $this->default_header . pack('N', strlen($payload_bytes)) . $payload_bytes;

        Log::info("request json: " . json_encode($query_request_json));
        Log::info("request bytes: " . bin2hex($full_client_request));

        $file_to_save = fopen("test_query.mp3", "wb");
        $header = ["Authorization: Bearer; {$this->token}"];

        $context = stream_context_create([
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
            ]
        ]);

        $ws = stream_socket_client($this->api_url, $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);
        if (!$ws) {
            Log::error("Failed to connect: $errstr ($errno)");
            return json(['code' => 500, 'msg' => "Failed to connect: $errstr ($errno)"]);
        }

        fwrite($ws, implode("\r\n", $header) . "\r\n\r\n");
        fwrite($ws, $full_client_request);

        $res = fread($ws, 2048);
        parseResponse($res, $file_to_save);
        fclose($file_to_save);

        Log::info("closing the connection...");
        fclose($ws);

        return json(['code' => 200, 'msg' => 'Success']);
    }

    private function parseResponse($res, $file)
    {
        Log::info("--------------------------- response ---------------------------");
        // Log::info("response raw bytes: " . bin2hex($res));

        $protocol_version = ord($res[0]) >> 4;
        $header_size = ord($res[0]) & 0x0f;
        $message_type = ord($res[1]) >> 4;
        $message_type_specific_flags = ord($res[1]) & 0x0f;
        $serialization_method = ord($res[2]) >> 4;
        $message_compression = ord($res[2]) & 0x0f;
        $reserved = ord($res[3]);
        $header_extensions = substr($res, 4, $header_size * 4 - 4);
        $payload = substr($res, $header_size * 4);

        Log::info("            Protocol version: " . dechex($protocol_version) . " - version $protocol_version");
        Log::info("                 Header size: " . dechex($header_size) . " - " . ($header_size * 4) . " bytes ");
        Log::info("                Message type: " . dechex($message_type) . " - " . MESSAGE_TYPES[$message_type]);
        Log::info(" Message type specific flags: " . dechex($message_type_specific_flags) . " - " . MESSAGE_TYPE_SPECIFIC_FLAGS[$message_type_specific_flags]);
        Log::info("Message serialization method: " . dechex($serialization_method) . " - " . MESSAGE_SERIALIZATION_METHODS[$serialization_method]);
        Log::info("         Message compression: " . dechex($message_compression) . " - " . MESSAGE_COMPRESSIONS[$message_compression]);
        Log::info("                    Reserved: " . dechex($reserved));

        if ($header_size != 1) {
            Log::info("           Header extensions: " . bin2hex($header_extensions));
        }

        if ($message_type == 0xb) { // audio-only server response
            if ($message_type_specific_flags == 0) { // no sequence number as ACK
                Log::info("                Payload size: 0");
                return false;
            } else {
                $sequence_number = unpack('N', $payload)[1];
                $payload_size = unpack('N', substr($payload, 4, 4))[1];
                $payload = substr($payload, 8);
                Log::info("             Sequence number: $sequence_number");
                Log::info("                Payload size: $payload_size bytes");
                fwrite($file, $payload);
                if ($sequence_number < 0) {
                    return true;
                } else {
                    return false;
                }
            }
        } elseif ($message_type == 0xf) {
            $code = unpack('N', $payload)[1];
            $msg_size = unpack('N', substr($payload, 4, 4))[1];
            $error_msg = substr($payload, 8);
            if ($message_compression == 1) {
                $error_msg = gzdecode($error_msg);
            }
            $error_msg = mb_convert_encoding($error_msg, 'UTF-8', 'auto');
            Log::info("          Error message code: $code");
            Log::info("          Error message size: $msg_size bytes");
            Log::info("               Error message: $error_msg");
            return true;
        } elseif ($message_type == 0xc) {
            $msg_size = unpack('N', $payload)[1];
            $payload = substr($payload, 4);
            if ($message_compression == 1) {
                $payload = gzdecode($payload);
            }
            Log::info("            Frontend message: $payload");
        } else {
            Log::info("undefined message type!");
            return true;
        }
    }
}