<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Queue;

/**
 * API-知识增强
 */
class RagEnhance extends Api
{
    // protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     * Project模型对象
     * @var \app\admin\model\ai\Project
     */
    protected $model = null;

    

    public function _initialize()
    {
        parent::_initialize();
    }
    

    /**
     * 开启/关闭知识增强
     */
    public function toggle()
    {
        $status = input('status');
        $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->find();
        $return = \app\admin\model\ai\Project::where('id',$project['id'])->update(['enhance_status'=>$status]);
        if($return){
            if($status == 1)
            {
                $fileList = \app\admin\model\ai\ConsoleFile::where(['enhance_status'=>0,'console_id'=>['in', explode(',', $project['console_ids'])],'display_status'=>2])->select();

                foreach($fileList as $item)
                {
                    // 推入构建文档增强
                    Queue::push('app\queue\job\BuildFileEnhance', [
                        'file_id' => $item['id'],
                        'user_id' => $this->auth->id
                    ], 'ai_file_emhance');
                }
            }
            $this->success('操作成功');
        }else{
            $this->error('操作失败');
        }
    }

    /**
     * 知识增强状态和进度
     */
    public function status()
    {
        $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->find();
        $current_progress = \app\admin\model\ai\ConsoleFile::where(['console_id'=>['in', explode(',', $project['console_ids'])],'display_status'=>2,'enhance_status'=>1,'type'=>['<>',5]])->count();
        $overall_goal = \app\admin\model\ai\ConsoleFile::where(['console_id'=>['in', explode(',', $project['console_ids'])],'display_status'=>2,'type'=>['<>',5]])->count();
        $current_progress1 = round($current_progress/$overall_goal*100,2);
        $this->success('操作成功',['enhance_type'=>$project['enhance_type'], 'enhance_status'=>$project['enhance_status'], 'current_progress'=>$current_progress1."%", 'enhance_count'=>$current_progress, 'file_count'=>$overall_goal]);

    }
}
