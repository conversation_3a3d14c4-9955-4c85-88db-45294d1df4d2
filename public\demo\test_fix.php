<?php
/**
 * 测试修复后的引擎是否正常工作
 */

// 引入必要的文件
require_once 'application/common/service/ai/engine/LocalEngineFactory.php';

try {
    echo "开始测试修复后的引擎...\n\n";
    
    // 测试1: 创建基础引擎
    echo "测试1: 创建基础引擎\n";
    $basicEngine = \app\common\service\ai\engine\LocalEngineFactory::create('basic');
    echo "✓ 基础引擎创建成功\n\n";
    
    // 测试2: 创建增强引擎
    echo "测试2: 创建增强引擎\n";
    $enhanceEngine = \app\common\service\ai\engine\LocalEngineFactory::create('enhance');
    echo "✓ 增强引擎创建成功\n\n";
    
    // 测试3: 创建专业引擎
    echo "测试3: 创建专业引擎\n";
    $professionalEngine = \app\common\service\ai\engine\LocalEngineFactory::create('professional');
    echo "✓ 专业引擎创建成功\n\n";
    
    // 测试4: 策略切换
    echo "测试4: 策略切换\n";
    $engine = new \app\common\service\ai\engine\LocalEngine();
    $engine->setStrategy(\app\common\service\ai\engine\LocalEngine::STRATEGY_BASIC);
    echo "✓ 切换到基础策略成功\n";
    $engine->setStrategy(\app\common\service\ai\engine\LocalEngine::STRATEGY_ENHANCE);
    echo "✓ 切换到增强策略成功\n";
    $engine->setStrategy(\app\common\service\ai\engine\LocalEngine::STRATEGY_PROFESSIONAL);
    echo "✓ 切换到专业策略成功\n\n";
    
    echo "🎉 所有测试通过！修复成功！\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "❌ 致命错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}
?>
