<?php
namespace app\common\library;

/**
 * 文心一言-生成向量数组
 */
class Embeddings {
    const API_KEY = "7GklvhqH4usdCwqLvE5vn1VZ";
    const SECRET_KEY = "IjfGdgkEV8ETRG3nAMGCCel1m2vkvE3H";

    public function run($input) {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/embeddings/embedding-v1?access_token={$this->getAccessToken()}",
            CURLOPT_TIMEOUT => 30,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER  => false,
            CURLOPT_SSL_VERIFYHOST  => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            
            CURLOPT_POSTFIELDS =>$input,
    
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),

        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if(strpos($response, 'error_code') !== false){
            error_log(date("Y-m-d H:i:s")."|input:".$input. "|".print_r($response, 1)."\r\n",3,ROOT_PATH."/runtime/log/".date("Ym")."/embeddings.log");
        }
        return $response;
    }
    
    /**
     * 使用 AK，SK 生成鉴权签名（Access Token）
     * @return string 鉴权签名信息（Access Token）
     */
    private function getAccessToken(){
        $curl = curl_init();
        $postData = array(
            'grant_type' => 'client_credentials',
            'client_id' => self::API_KEY,
            'client_secret' => self::SECRET_KEY
        );
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://aip.baidubce.com/oauth/2.0/token',
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_SSL_VERIFYPEER  => false,
            CURLOPT_SSL_VERIFYHOST  => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POSTFIELDS => http_build_query($postData)
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $rtn = json_decode($response);
        return $rtn->access_token;
    }

    /** 
	 * 调用生成向量数组匹配对应关系
	 */
	public function getEmbeddingsNew($arr){
		
		$input = json_encode(['input'=>$arr]);
		
		$embeddingsStr = $this->run($input);
		$embeddingsArr = json_decode($embeddingsStr,true);
		return $embeddingsArr['data'];
	}
}