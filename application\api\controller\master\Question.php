<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;

/**
 * API-AI问答初始问题
 */
class Question extends Api
{
    // protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     * UserUrl模型对象
     * @var \app\admin\model\ai\Question
     */
    protected $model = null;

    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\Question;

    }
    

    /**
     * 列表
     */
    public function index()
    {
        $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $where = ['robot' => $project_id];
        $limit = $this->request->post('limit') ?? 10;
        $page = $this->request->post('page') ?? 1;
        $this->request->get([config('paginate.var_page') => $page]);

        $list = $this->model
                ->with(['project'])
                ->where($where)
                ->order('id', 'desc')
                ->paginate($limit);

        foreach ($list as $row) {
            $row->visible(['id','question', 'createtime', 'updatetime', 'nickname']);
            $row->visible(['project']);
            $row->getRelation('project')->visible(['name']);
            $row->createtime = date('Y-m-d H:i:s', $row->createtime);
            $row->updatetime = date('Y-m-d H:i:s', $row->updatetime);
            $row->nickname = $this->auth->nickname;
        }

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }

    /**
     * 创建
     */
    public function create(){
        $question = $this->request->post('question');
        $result = false;
        Db::startTrans();
        try {
            $project_id = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
            $params['robot'] = $project_id;
            $params['question'] = $question;
            $params['createtime'] = time();
            $params['updatetime'] = time();
            $result = $this->model->allowField(true)->insertGetId($params);
            Db::commit();

        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        
        $this->success("创建成功!");
    }
    /**
     * 修改
     */
    public function edit(){
        $id = $this->request->post('id');
        $question = $this->request->post('question');

        $row = $this->model->get($id);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $result = false;
        Db::startTrans();
        try {
            $params['question'] = $question;
            $params['updatetime'] = time();

            $result = $row->allowField(true)->save($params);

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success("修改成功!");
    }

    /**
     * 删除（支持批量）
     */
    public function delete()
    {
        $ids = $this->request->post('ids');
        $idsArr = explode(',', $ids);
        if (empty($ids)) {
            $this->error(__('请选择要删除的数据'));
        }

        $result = false;
        Db::startTrans();
        try {
            foreach ($idsArr as $id) {
                $row = $this->model->get($id);
                if (!$row) {
                    throw new Exception(__("未找到ID为： {$id} 的记录", ['id' => $id]));
                }
                $params['deletetime'] = time();
                $result = $row->allowField(true)->save($params);
                if ($result === false) {
                    throw new Exception(__("删除ID为： {$id} 的记录失败", ['id' => $id]));
                }
            }

            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }

        $this->success("删除成功!");
    }

}
