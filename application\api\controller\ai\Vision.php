<?php

namespace app\api\controller\ai;

use app\common\controller\Api;
use app\common\library\aliyun\VisionModel;

class Vision extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function analyze()
    {
        $imageUrl = input('image_url');
        $text = input('text', '这是什么');

        if (empty($imageUrl)) {
            $this->error('图片URL不能为空');
        }

        $vision = new VisionModel();
        $result = $vision->analyze($imageUrl, $text);

        if ($result['success']) {
            $this->success('分析成功', $result);
        } else {
            $this->error($result['error']);
        }
    }
}