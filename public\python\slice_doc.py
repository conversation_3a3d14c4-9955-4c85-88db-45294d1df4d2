from langchain_experimental.text_splitter import SemanticChunker
from langchain_openai.embeddings import OpenAIEmbeddings
import os, sys
import json


client = OpenAIEmbeddings(
	model="text-embedding-3-small",
    api_key="sk-lM7Yk9WKur9otgV3nCOxh6sKhg20lYNkN3oPHZRIXrqO0Y7p",
    base_url="https://api.openai-proxy.org/v1"
)

# 读取文件内容
def read_docx_content(file_path):
    from docx import Document
    try:
        doc = Document(file_path)
        content_parts = []

        # 提取段落内容
        for para in doc.paragraphs:
            content_parts.append(para.text)

        # 提取表格内容
        for table in doc.tables:
            for row in table.rows:
                row_content = [cell.text for cell in row.cells]
                content_parts.append("\t".join(row_content))

        content = "\n".join(content_parts)
        return content
    except Exception as e:
        print(f"读取 .docx 文件时发生错误: {e}")
        return None

def read_xlsx_content(file_path, sheet_names=None):
    from openpyxl import load_workbook
    try:
        wb = load_workbook(file_path)
        if sheet_names is None:
            sheet_names = wb.sheetnames  # 如果没有指定sheet名称，则读取所有sheet
        contents = []
        for sheet_name in sheet_names:
            sheet = wb[sheet_name]
            sheet_content = "\n".join(["\t".join([str(cell.value) if cell.value is not None else "" for cell in row]) for row in sheet.iter_rows()])
            contents.append(f"Sheet: {sheet_name}\n{sheet_content}\n")
        return "\n".join(contents)
    except Exception as e:
        print(f"读取 .xlsx 文件时发生错误: {e}")
        return None


def read_pdf_content(file_path):
    import PyPDF2
    try:
        with open(file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            content = "\n".join([reader.pages[i].extract_text() for i in range(len(reader.pages))])
        return content
    except Exception as e:
        print(f"读取 .pdf 文件时发生错误: {e}")
        return None

def read_md_content(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        return content
    except Exception as e:
        print(f"读取 .md 文件时发生错误: {e}")
        return None

def read_txt_content(file_path):
    try:
        with open(file_path) as f:
            content = f.read()
        return content
    except Exception as e:
        print(f"读取 .txt 文件时发生错误: {e}")
        return None



def read_file_content(file_path):
    file_extension = os.path.splitext(file_path)[1].lower()

    if file_extension == '.docx':
        return read_docx_content(file_path)
    elif file_extension == '.xlsx':
        return read_xlsx_content(file_path)
    elif file_extension == '.pdf':
        return read_pdf_content(file_path)
    elif file_extension == '.md':
        return read_md_content(file_path)
    elif file_extension == '.txt':
        return read_txt_content(file_path)
    else:
        print(f"不支持的文件格式: {file_extension}")
        return None

# 获取结果
file_content = read_file_content('state.txt')
# with open('file_content.txt', 'w') as file:
#     file.write(file_content)

# text_splitter = RecursiveCharacterTextSplitter(
#     # Set a really small chunk size, just to show.
#     chunk_size=100,
#     chunk_overlap=20,
#     length_function=len,
#     is_separator_regex=False,
# )
# docs = text_splitter.create_documents([file_content])
text_splitter = SemanticChunker(
    client, breakpoint_threshold_type="standard_deviation"
)
data = []
docs = text_splitter.create_documents([file_content])
print(len(docs))
for item in docs:
        data.append({"content":item.page_content})
        #print(item.page_content)
        #print('--------------------------------------')

with open('result.txt', 'w') as file:
    file.write(json.dumps(data,ensure_ascii = True))