<?php

namespace app\admin\controller\ai;

use app\common\controller\Backend;
use app\admin\model\ai\Project;
use Exception;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\db\exception\BindParamException;
use think\exception\PDOException;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx as Xlsx1;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use think\Queue;

/**
 * 用户卡片管理
 *
 * @icon fa fa-circle-o
 */
class UserCard extends Backend
{

    /**
     * UserCard模型对象
     * @var \app\admin\model\ai\UserCard
     */
    protected $model = null;

    protected $embeddings = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\UserCard;
        $this->embeddings = new \app\common\library\Embeddings;
        $this->view->assign("typeList", $this->model->getTypeList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['user','project'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','type','name','file','status','createtime']);
                $row->visible(['user']);
				$row->getRelation('user')->visible(['nickname']);
				$row->visible(['project']);
				$row->getRelation('project')->visible(['name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 导入
     *
     * @return void
     * @throws PDOException
     * @throws BindParamException
     */
    public function import()
    {
        $file = $this->request->request('file');
        if (!$file) {
            $this->error(__('Parameter %s can not be empty', 'file'));
        }
        $filePath = ROOT_PATH . DS . 'public' . DS . $file;
        if (!is_file($filePath)) {
            $this->error(__('No results were found'));
        }
        //实例化reader
        $ext = pathinfo($filePath, PATHINFO_EXTENSION);
        if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
            $this->error(__('Unknown data format'));
        }
        if ($ext === 'csv') {
            $file = fopen($filePath, 'r');
            $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
            $fp = fopen($filePath, 'w');
            $n = 0;
            while ($line = fgets($file)) {
                $line = rtrim($line, "\n\r\0");
                $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                if ($encoding !== 'utf-8') {
                    $line = mb_convert_encoding($line, 'utf-8', $encoding);
                }
                if ($n == 0 || preg_match('/^".*"$/', $line)) {
                    fwrite($fp, $line . "\n");
                } else {
                    fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                }
                $n++;
            }
            fclose($file) || fclose($fp);

            $reader = new Csv();
        } elseif ($ext === 'xls') {
            $reader = new Xls();
        } else {
            $reader = new Xlsx();
        }
        //加载文件
        $insert = [];
        try {
            if (!$PHPExcel = $reader->load($filePath)) {
                $this->error(__('Unknown data format'));
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表
            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行
            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            $fields = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[] = $val;
                }
            }
            $robot = Project::where('name',$fields[0])->value('id');
            if($robot<1){
                throw new Exception('请填写正确的项目名称!');
            }
            $fields = [];
            for ($currentRow = 2; $currentRow <= 2; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[] = $val;
                }
            }
            if(!($fields[0]=='所属知识库' && $fields[1]=='标签' && $fields[2]=='封面图片链接' && $fields[3]=='答案链接')){
                throw new Exception('模版格式不正确!');
            }

            for ($currentRow = 3; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $values[] = is_null($val) ? '' : $val;
                }
                if(empty($values[0]))continue;
                $row = [];
                $row['robot'] = $robot;
                $console_id = \app\admin\model\ai\Console::where('name',$values[0])->value('id');
                if($console_id<1){
                    throw new Exception('请填写正确的知识库名称!行：'.$currentRow);
                }
                $row['console_id'] = $console_id;
                $row['name'] = "{$values[1]}";
                $row['cover'] = "{$values[2]}";
                $row['type'] = strpos($values[3], '.mp4') !==false ? 2 : 1;
                $row['file'] = "{$values[3]}";
                $result = $this->model->where(['robot'=>$robot,'name'=>$row['name']])->find();
                if($result || empty($row['name']))
                {
                    continue;
                }else{
                    if ($row) {
                        $insert[] = $row;
                    }
                }
            }
        } catch (Exception $exception) {
            $this->error($exception->getMessage());
        }
        if (!$insert) {
            $this->error(__('No rows were updated'));
        }
        try {
            $uid = \app\admin\model\User::where(['project_id'=>$robot,'group_id'=>1])->value('id');
            foreach($insert as $item)
            {
                $duration = '';
                if(strpos($item['file'],'.mp4')!==false)
                {
                    $fileNew = str_replace(request()->domain(),'',$file);
                    if(strpos($fileNew,'http')===false)
                    {
                        $cover = str_replace('.mp4','.jpg',$fileNew);
                        $this->extractVideoCover(ROOT_PATH ."public" . $fileNew, ROOT_PATH ."public" . $cover);
                        $duration = $this->getVideoDuration(ROOT_PATH ."public" . $fileNew);
                    }
                }else{
                    $cover = $file;
                }
                $item['createtime'] = time();
                $item['updatetime'] = time();
                $item['status'] = 1;
                $item['user_id'] = $uid;
                $file_path = str_replace(request()->domain(),'',$item['file']);
                if(strpos($file_path,'http')===false)
                {
                    $item['file_size'] = filesize(ROOT_PATH ."public" . $file_path);
                }else{
                    $item['file_size'] = 0;
                }
                $item['duration'] = $duration;
                $result = $this->model->allowField(true)->insertGetId($item);
                $insertExt = [];
                $newData = $this->embeddings->getEmbeddingsNew([$item['name']]);
                $vectorization = base64_encode(json_encode($newData[0]));
                $insertExt[] = [
                    'card_id' => $result,
                    'name' => $item['name'],
                    'vectorization' => $vectorization
                ];
                \app\admin\model\ai\UserCardExt::insertAll($insertExt);
                usleep(100000);
            }
            //推入训练队列中
            Queue::push('app\queue\job\CardToPGsql', [
                'type' => 3
            ],'card_to_pgsql');
        } catch (PDOException $exception) {
            $msg = $exception->getMessage();
            if (preg_match("/.+Integrity constraint violation: 1062 Duplicate entry '(.+)' for key '(.+)'/is", $msg, $matches)) {
                $msg = "导入失败，包含【{$matches[1]}】的记录已存在";
            };
            $this->error($msg);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success();
    }

    /**
     * 提取视频第一帧并保存为图片
     */
    function extractVideoCover($videoPath, $outputImagePath) {
        // FFmpeg命令，提取视频第一帧并保存为图片
        $command = "ffmpeg -i '{$videoPath}' -ss 00:00:01.000 -vframes 1 '{$outputImagePath}'";
        // 使用shell_exec执行命令
        $output = shell_exec($command);
    
        // 检查命令执行是否成功
        // if ($output === null) {
        //     throw new Exception("Failed to extract video cover.");
        // }
    
        return true; // 成功
    }

    function getVideoDuration($videoFilePath) {
        // 构建FFmpeg命令，用于获取视频时长
        $command = "ffmpeg -i {$videoFilePath} 2>&1 | grep 'Duration' | cut -d ' ' -f 4 | sed s/,//";
        
        // 使用shell_exec执行命令并捕获输出
        $output = shell_exec($command);
        
        // 解析输出的时长字符串，格式通常是 00:00:00.00，转换为秒
        if ($output) {
            list($hours, $minutes, $seconds) = explode(":", $output);
            $durationInSeconds = intval($hours * 3600) + intval($minutes * 60) + intval($seconds);
            return $durationInSeconds;
        } else {
            // 如果无法获取时长，返回null或其他错误处理
            return null;
        }
    }
    /**
     * 导出
     */
    public function export(){

        $where = [];
        $name = '用户卡片导出-'.date("Ymd");
        $list = $this->model
                ->where($where)
                ->field('*')
                ->select();
        $data = [];
        foreach ($list as $row) {
            $title = Project::where('id',$row['robot'])->value('name');
            $console_name = \app\admin\model\ai\Console::where('id',$row['console_id'])->value('name');
            $data[] = [
                'project' => $title,
                'console_name' => $console_name,
                'name' => $row['name'],
                'thum' => $row['cover'],
                'path' => $row['file']
            ];

        }
        
        $spreadsheet = new Spreadsheet();

        $this->opsheet($spreadsheet,0,$data,"导出全部");

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $name . '.xlsx"');
        header('Cache-Control: max-age=0');
        $writer = new Xlsx1($spreadsheet);
        $writer->save('php://output');
        //删除清空：
        $spreadsheet->disconnectWorksheets();
        unset($spreadsheet);
        exit;
    }

    // 处理表格
    public function opsheet($spreadsheet,$n, $data,$name){
        $spreadsheet->createSheet();//创建sheet
        $objActSheet = $spreadsheet->setActiveSheetIndex($n);//设置当前的活动sheet
        

        $sheet = $spreadsheet->getActiveSheet($n)->setTitle('sheet');//设置sheet的名称

        $spreadsheet->getActiveSheet($n)->mergeCells('A1:E1'); //合并单元格

        //$spreadsheet->getActiveSheet($n)->getStyle('A1')->getFont()->setSize(20); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('A')->getFont()->setSize(10); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('B')->getFont()->setSize(10); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('C')->getFont()->setSize(10); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('D')->getFont()->setSize(10); //设置title的字体大小
        $spreadsheet->getActiveSheet($n)->getStyle('E')->getFont()->setSize(10); //设置title的字体大小

        $spreadsheet->getActiveSheet($n)->getStyle('A')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('A')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('B')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('B')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('C')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('C')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('D')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('D')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('E')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER); //居中
        $spreadsheet->getActiveSheet($n)->getStyle('E')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER); //居中

        $spreadsheet->getActiveSheet($n)->getColumnDimension('A')->setWidth(40); //固定列宽
        $spreadsheet->getActiveSheet($n)->getColumnDimension('B')->setWidth(60); //固定列宽
        $spreadsheet->getActiveSheet($n)->getColumnDimension('C')->setWidth(80); //固定列宽
        $spreadsheet->getActiveSheet($n)->getColumnDimension('D')->setWidth(80); //固定列宽
        $spreadsheet->getActiveSheet($n)->getColumnDimension('E')->setWidth(80); //固定列宽

        $spreadsheet->getActiveSheet($n)->getDefaultRowDimension()->setRowHeight(20);

        $styleArray1 = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' =>  \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN //细边框
                ]
            ]
        ];

        $objActSheet->setCellValue('A1', $name);

        //表头
        $objActSheet->setCellValue('A2', '项目名称');
        $objActSheet->setCellValue('B2', '所属知识库');
        $objActSheet->setCellValue('C2', '标签');
        $objActSheet->setCellValue('D2', '封面图片链接');
        $objActSheet->setCellValue('E2', '答案链接');

        //数据
        if(!empty($data)){
            $i = 3;
            foreach ($data as $k => $v)
            {
                $objActSheet->setCellValue('A'.$i, $v['project']);
                $objActSheet->setCellValue('B'.$i, $v['console_name']);
                $objActSheet->setCellValue('C'.$i, $v['name']);
                $objActSheet->setCellValue('D'.$i, $v['thum']);
                $objActSheet->setCellValue('E'.$i, $v['path']);
                $i++;
            }
            $spreadsheet->getActiveSheet($n)->getStyle('A1'.':'.'E'.($i-1))->applyFromArray($styleArray1);
        }
    }
}
