<?php

namespace app\common\service\ai\engine;

use app\common\service\ai\BaseEngine;
use think\Db;
use app\admin\model\ai\ConsoleFile;
use app\admin\model\ai\UrlData;

/** Rag增强模型 */
class Ragenhance extends BaseEngine
{
	public $debug = false;
	protected $references = [];
    private $msg = '';

    /**
	 * 构造引用内容
	 */
	public function buildReferenceContent($project, $msg)
	{
        $this->msg = $msg;
		$vectorList = $this->queryVector($project, $msg);
		$vectorScore = $project['vector_score']>0 ? $project['vector_score'] : 0.3;
		$list = [];
		foreach ($vectorList as &$item) {
			if ((isset($item['cosine_similarity']) && $item['cosine_similarity'] > $vectorScore) || (isset($item['relevance_score']) && $item['relevance_score'] > $vectorScore)) {
				$consoleFile = ConsoleFile::where('id', $item['file_id'])->find();
				if($consoleFile)
				{
					$file_number = $consoleFile['file_number'] ?? 0;
					if(!empty($consoleFile['type']) && $consoleFile['type'] == 5){
						$urlData = UrlData::where('file_number', $file_number)->find();
						$fileName = !empty($urlData['title']) ? $urlData['title'] : $consoleFile['name'];
						$filePath = !empty($urlData['url']) ? $urlData['url'] : cdnurl($consoleFile['file'], true);
					}else{
						$fileName = !empty($consoleFile['name']) ? $consoleFile['name'] : '';
						$filePath = !empty($consoleFile['file'])? cdnurl($consoleFile['file'], true) : '';
					}
					$item['title'] = $fileName;
					$item['file'] = $filePath;
					$item['file_id'] = $item['file_id'];
					$item['file_number'] = $file_number;
					$list[] = $item;
				}
			}
		}
		$query = $this->pg_connect();
		// 尝试获取相邻切片以恢复完整内容
		if($list)
		{
			$list = $this->retrieveAdjacentSlices($list, $query, $project);
		
			// 按相似度排序
			$list = $this->reorderReferences($list);
			$vectorNum = $project['vector_num'] > 0 ? $project['vector_num'] : 6;
			$list = array_slice($list, 0, $vectorNum);
			foreach ($list as $item) {
				$slice = $item['content'];
				$this->references[] = [
					'title'   => $item['title'],
					'content' => $slice,
					'file'    => $item['file'],
					'cosine_similarity' => $item['cosine_similarity'] ?? 0,
					'relevance_score' => $item['relevance_score'] ?? 0,
					'document_id' => $item['file_number'],
					'file_id' => $item['file_id']
				];
			}
		}
		return $this->references;
	}

    /** 查询向量库 */
	private function queryVector($project, $text)
	{
		$vectorNum = 100;
		$query = $this->pg_connect();
		$text = mb_strcut($text, 0, 384);
		$vectorArr = $this->getEmbeddingsNew([$text]);

		$vectorContent = json_encode(['content'=>$vectorArr[0]['embedding']]);
		
		$vectorList = json_decode($vectorContent, true);
		$list = [];
		
		if (!empty($vectorList['content'])) {
			$queryVector = json_encode($vectorList['content']);
			
			// 先从slice_qa表查询相关切片
			$query->query("
				SET hnsw.ef_search = 50;
			");
			$qaSlices = $query->query("
				SELECT 
					vdu.id,
					vdu.file_id,
					vdu.content,
					1 - (slice_qa.embedding <=> '{$queryVector}') AS cosine_similarity,
					slice_qa.extracted_question,
					slice_qa.extracted_answer
				FROM slice_qa
				JOIN vector_data_upgrades vdu ON vdu.id = slice_qa.slice_id
				WHERE vdu.console_id IN ({$project['console_ids']})
				AND vdu.delete_time IS NULL
				ORDER BY slice_qa.embedding <=> '{$queryVector}'
				LIMIT 50;
			");
			
			if($this->debug && false){
				dump("
					SET hnsw.ef_search = 100;
					SELECT 
						vdu.id,
						vdu.file_id,
						vdu.content,
						1 - (slice_qa.embedding <=> '{$queryVector}') AS cosine_similarity,
						slice_qa.extracted_question,
						slice_qa.extracted_answer
					FROM slice_qa
					JOIN vector_data_upgrades vdu ON vdu.id = slice_qa.slice_id
					WHERE vdu.console_id IN ({$project['console_ids']})
					AND vdu.delete_time IS NULL
					ORDER BY slice_qa.embedding <=> '{$queryVector}'
					LIMIT 50;
				");

				dump($qaSlices);
			}

			// 如果qa结果不足，补充原始切片
			if (count($qaSlices) < $vectorNum) {
				$remainingNum = $vectorNum - count($qaSlices);
				$existingIds = array_column($qaSlices, 'id');
				$excludeIds = empty($existingIds) ? "0" : implode(',', $existingIds);
				$query->query("SET hnsw.ef_search = 100;");
				$regularSlices = $query->query("
					SELECT 
						v.id,
						v.file_id,
						v.content,
						1 - (v.embedding <=> '{$queryVector}') AS cosine_similarity,
						NULL AS extracted_question,
						NULL AS extracted_answer
					FROM vector_data_upgrades v
					LEFT JOIN (
						SELECT unnest(array[{$excludeIds}]) AS id_to_exclude
					) ex ON v.id = ex.id_to_exclude
					WHERE v.console_id IN ({$project['console_ids']})
						AND ex.id_to_exclude IS NULL
						AND v.delete_time IS NULL
					ORDER BY v.embedding <=> '{$queryVector}'
					LIMIT {$remainingNum};
				");

				if($this->debug && false){
					dump("
						SET hnsw.ef_search = 100;

						SELECT 
							v.id,
							v.file_id,
							v.content,
							1 - (v.embedding <=> '{$queryVector}') AS cosine_similarity,
							NULL AS extracted_question,
							NULL AS extracted_answer
						FROM vector_data_upgrades v
						LEFT JOIN (
							SELECT unnest(array[{$excludeIds}]) AS id_to_exclude
						) ex ON v.id = ex.id_to_exclude
						WHERE v.console_id IN ({$project['console_ids']})
							AND ex.id_to_exclude IS NULL
							AND v.delete_time IS NULL
						ORDER BY v.embedding <=> '{$queryVector}'
						LIMIT {$remainingNum};
				");
	
					dump($regularSlices);
				}

				// 合并结果
				$list = array_merge($qaSlices, $regularSlices);
			} else {
				$list = $qaSlices;
			}
			
			
			// 记录日志
			if ($this->debug) {
				$logData = [
					'query' => $text,
					'vector' => $queryVector,
					'results' => $list,
					'time' => date('Y-m-d H:i:s')
				];
				error_log(
					date("Y-m-d H:i:s") . "|vector_query:" . print_r($logData, true) . "\r\n",
					3,
					ROOT_PATH . "/runtime/log/" . date("Ym") . "/vector_query.log"
				);
			}
		}

		return $this->processResults($list);
    }

	/**
	 * 处理查询结果
	 */
	private function processResults($results)
	{
		foreach ($results as &$result) {
			// 如果存在QA信息，增强内容
			if (!empty($result['extracted_question']) && !empty($result['extracted_answer'])) {
				$result['content'] = sprintf(
					"问题：%s\n回答：%s\n原文：%s",
					$result['extracted_question'],
					$result['extracted_answer'],
					$result['content']
				);
			}
			
			// 清理不需要的字段
			unset($result['extracted_question'], $result['extracted_answer']);
		}
		return $results;
	}

    /** 链接PGsql数据库 */
	private function pg_connect()
	{
		$request = Db::connect(
			[
				'type' => 'pgsql',
				'hostname' => '127.0.0.1',
				'database' => config('postgres.database'),
				'username' => 'postgres',
				'password' => 'DRrTmhCKrLWs2b34',
				'hostport' => '5432'
			]
		);
		return $request;
	}

    /** 
	 * 调用生成向量数组匹配对应关系
	 */
	function getEmbeddingsNew($arr) {
		$input = json_encode(['input' => $arr]);

		$embeddings = new \app\common\library\Embeddings;
		$embeddingsStr = $embeddings->run($input);
		$embeddingsArr = json_decode($embeddingsStr, true);

		// 检查 data 键是否存在
		if (!isset($embeddingsArr['data'])) {
			usleep(500000); // 停顿 0.5 秒
			$embeddingsStr = $embeddings->run($input); // 重试
			$embeddingsArr = json_decode($embeddingsStr, true);
		}

		return $embeddingsArr['data'] ?? []; // 如果 data 不存在，返回空数组或其他默认值
	}

    /**
	 * 重排引用内容
	 */
	private function reorderReferences($references, $msg = '')
	{
		$batchSize = 40;
		$path = '/mnt/sdc/wwwroot/ai-master/python/qianwen/rerank.py';
		$msgEscaped = str_replace(['"', "'"], ['“', "’"], $msg?$msg:$this->msg);

		$sortedReferences = []; // 存储排序结果（引用键 => relevance_score）
		$unusedKeys = [];       // 未参与排序的 keys（没有出现在 rerank 结果中）

		$batches = array_chunk($references, $batchSize, true);
		foreach ($batches as $batch) {
			$vectorArr = [];
			$keyMap = [];

			foreach ($batch as $originalKey => $v) {
				$content = mb_substr((isset($v['title'])?$v['title'].":":'').$v['content'], 0, 2000);
				$escapedContent = str_replace(['"', "'", "(", ")"], ['“', "’", "（", "）"], $content);
				$vectorArr[] = $escapedContent;
				$keyMap[] = $originalKey; // 映射 index => 原始 key
			}
			if($this->debug){
				dump($keyMap);
			}

			$vectorStr = implode('@@', $vectorArr);
			$cmd = $this->pythonPath . ' ' . $path . ' "%s" "%s" ';
			$return = [];
			exec(sprintf($cmd, $msgEscaped, $vectorStr), $return, $returnCode);
			$usedKeys = [];

			if (!empty($return[0])) {
				$arr = json_decode($return[0], true);
				if (!empty($arr['output']['results'])) {
					foreach ($arr['output']['results'] as $result) {
						$indexInBatch = $result['index'];
						if(isset($keyMap[$indexInBatch]))
						{
							$key = $keyMap[$indexInBatch];
							$sortedReferences[] = [
								'key' => $key,
								'relevance_score' => $result['relevance_score']
							];
							$usedKeys[] = $key;
						}
					}
				}
			}

			// 剩下没被返回的 key，加入 unused 列表
			$unusedKeys = array_merge($unusedKeys, array_diff($keyMap, $usedKeys));
		}

		// 根据排序结果重建 references 顺序
		$final = [];

		if($this->debug){
			dump($sortedReferences);
		}
		foreach ($sortedReferences as $item) {
			$key = $item['key'];
			$references[$key]['relevance_score'] = $item['relevance_score'];
			$final[] = $references[$key];
		}

		// 追加未排序的
		foreach ($unusedKeys as $key) {
			$references[$key]['relevance_score'] = 0;
			$final[] = $references[$key];
		}
		// 按 relevance_score 倒序排序
		usort($final, function ($a, $b) {
			return ($b['relevance_score'] ?? 0) <=> ($a['relevance_score'] ?? 0);
		});

		return $final;
	}

	/**
	 * 获取相邻切片以恢复完整内容
	 * @param array $slices 原始切片结果
	 * @param object $query 数据库连接
	 * @param array $project 项目信息
	 * @return array 增强后的切片结果
	 */
	private function retrieveAdjacentSlices($slices, $query, $project)
	{
		$enhancedSlices = [];
		$processedIds = [];
		
		foreach ($slices as $slice) {
			// 如果已处理过该切片，跳过
			if (in_array($slice['id'], $processedIds)) {
				continue;
			}
			
			$processedIds[] = $slice['id'];
			
			// 检查是否包含关键词，表明内容可能被截断
			$isIncomplete = $this->isIncompleteContent($slice['content']);
			
			// 如果内容看起来不完整或包含步骤关键词，尝试获取相邻切片
			if ($isIncomplete || $this->containsStepKeywords($slice['content'])) {
				// 直接查询相邻ID的切片（前后各2个）
				$adjacentSlices = $query->query("
					SELECT id, content, file_id
					FROM vector_data_upgrades
					WHERE file_id = {$slice['file_id']}
					AND console_id IN ({$project['console_ids']})
					AND delete_time IS NULL
					AND id BETWEEN {$slice['id']} - 1 AND {$slice['id']} + 1
					ORDER BY id ASC
				");
				
				// 如果找不到相邻切片，使用原始切片
				if (empty($adjacentSlices)) {
					$enhancedSlices[] = $slice;
					continue;
				}
				
				// 合并相邻切片内容
				$combinedContent = '';
				foreach ($adjacentSlices as $adjacentSlice) {
					$combinedContent .= $adjacentSlice['content'] . "\n";
					// 将处理过的切片ID添加到已处理列表
					$processedIds[] = $adjacentSlice['id'];
				}
				
				// 创建新的合并切片
				$newSlice = $slice;
				$newSlice['content'] = $this->cleanupCombinedContent($combinedContent);
				$newSlice['is_combined'] = true;
				
				$enhancedSlices[] = $newSlice;
			} else {
				$enhancedSlices[] = $slice;
			}
		}
		
		return $enhancedSlices;
	}

	/**
	 * 清理合并后的内容，去除重复段落和格式化
	 * @param string $content 合并后的内容
	 * @return string 清理后的内容
	 */
	private function cleanupCombinedContent($content)
	{
		// 按行分割
		$lines = explode("\n", $content);
		$uniqueLines = [];
		$seenLines = [];
		
		foreach ($lines as $line) {
			$trimmedLine = trim($line);
			if (empty($trimmedLine)) continue;
			
			// 使用MD5作为行内容的唯一标识
			$lineHash = md5($trimmedLine);
			
			// 如果这行内容之前没见过，添加到结果中
			if (!isset($seenLines[$lineHash])) {
				$uniqueLines[] = $trimmedLine;
				$seenLines[$lineHash] = true;
			}
		}
		
		// 重新组合内容
		return implode("\n", $uniqueLines);
	}

	/**
	 * 判断内容是否不完整
	 * @param string $content 切片内容
	 * @return bool 是否不完整
	 */
	private function isIncompleteContent($content)
	{
		// 检查内容是否以数字+括号结尾，表示可能被截断的步骤
		if (preg_match('/\d+[\)）]\s*$/', $content)) {
			return true;
		}
		
		// 检查内容是否以不完整的句子结尾
		if (preg_match('/[,;，；]\s*$/', $content)) {
			return true;
		}
		
		// 检查是否以数字结尾，可能是被截断的编号
		if (preg_match('/\d+\s*$/', $content)) {
			return true;
		}
		
		// 检查是否包含"步骤"、"开车"、"停车"等关键词，但内容较短
		if ($this->containsStepKeywords($content) && mb_strlen($content) < 300) {
			return true;
		}
		
		// 检查是否包含序号但数量少于3个，表示可能是不完整的列表
		preg_match_all('/\d+[\)）]/', $content, $matches);
		if (!empty($matches[0]) && count($matches[0]) < 3) {
			return true;
		}
		
		// 检查是否包含数值范围表达式但被截断
		if (preg_match('/~\s*\d+(\.\d+)?[a-zA-Z]*\s*$/', $content)) {
			return true;
		}
		
		// 检查是否包含单位但没有后续内容，可能是被截断的测量值
		if (preg_match('/\d+(\.\d+)?\s*[a-zA-Z]+\s*$/', $content)) {
			return true;
		}
		
		// 检查是否包含开车/停车步骤但步骤数量少于预期
		if ($this->containsStepKeywords($content)) {
			preg_match_all('/\d+[\)）]/', $content, $stepMatches);
			$lastStepNumber = !empty($stepMatches[0]) ? (int)preg_replace('/[^\d]/', '', end($stepMatches[0])) : 0;
			
			// 如果最后一个步骤编号小于10且内容不长，可能是不完整的
			if ($lastStepNumber > 0 && $lastStepNumber < 10 && mb_strlen($content) < 800) {
				return true;
			}
		}
		
		return false;
	}
	
	/**
	 * 检查内容是否包含步骤相关关键词
	 * @param string $content 切片内容
	 * @return bool 是否包含步骤关键词
	 */
	private function containsStepKeywords($content)
	{
		$keywords = ['步骤', '开车', '停车', '操作', '流程', '顺序', '方法', '程序', '启动', '关闭'];
		foreach ($keywords as $keyword) {
			if (strpos($content, $keyword) !== false) {
				return true;
			}
		}
		return false;
	}
}