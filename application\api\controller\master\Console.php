<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use Exception;
use app\common\service\ai\AiService;

/**
 * API-知识库管理
 */
class Console extends Api
{
    // protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    /**
     * ConsoleFile模型对象
     * @var \app\admin\model\ai\ConsoleFile
     */
    protected $model = null;
    

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ai\Console;
    }
    

    /**
     * 列表
     */
    public function index()
    {
        $limit = $this->request->post('limit') ?? 10;
        $page = $this->request->post('page') ?? 1;
        $this->request->get([config('paginate.var_page') => $page]);
        $sort = input('sort');
        $order = input('order');
        $where = [];
        if($this->request->post('name')){
            $where['name'] = ['like',"%{$this->request->post('name')}%"];
        }
        $project = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->find();
        $consoleArr = isset($project['console_ids']) ? explode(',', $project['console_ids']) : [];
        $list = $this->model
                ->where(['id'=>['in', $consoleArr]])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
        foreach ($list as $row) {
            $row->visible(['id','name','updatetime','image', 'sort', 'invoking_status']);
            $row['updatetime'] = date("Y-m-d H:i",$row['updatetime']);
            $row['image'] = cdnurl($row['image'], true);
        }

        $menu = Db::name('ai_menu_permissions')->where(['robot'=>$project['id']])->select();
        $result = array("total" => $list->total(), "rows" => $list->items(),"menu"=> $menu);

        return json($result);
        
    }

    /**
     * 创建
     */
    public function create(){
        $name = $this->request->post('name');
        $image = $this->request->post('image');
        
        $robot = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('id');
        $project = db::name('ai_project')->where('id',$robot)->field('id,console_key,type,console_ids')->find();
        $AiEngine = AiService::getEngine($project['type']);
        $console_number = $AiEngine->addConsole($name, '');
        
        if($console_number){
            $result = false;
            Db::startTrans();
            try {
                $params['console_number'] = $console_number;
                $params['name'] = $name;
                $params['user_id'] = $this->auth->id;
                $params['image'] = $image;
                $params['createtime'] = time();
                $params['updatetime'] = time();
                $params['type'] = $project['type'] ?? 'local';
                $result = $this->model->insertGetId($params);
                Db::commit();
                $consoleArr = [];
                if($project['console_ids'])
                {
                    $consoleArr = \app\admin\model\ai\Console::where(['id'=>['in', explode(',', $project['console_ids'])]])->column('id');
                }
                $consoleArr[] = $result;
                \app\admin\model\ai\Project::where(['id'=>$robot])->update(['console_ids'=> implode(',', $consoleArr)]);

                \app\admin\model\ai\ConsoleFileLog::insert(['user_id'=> $this->auth->id, 'action'=>1, 'type'=>2, 'filename'=> $name, 'createtime' => time(), 'updatetime' => time()]);

            } catch (ValidateException|PDOException|Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($result === false) {
                $this->error(__('No rows were inserted'));
            }
        }else{
            $this->error("创建失败!");
        }
        
        $this->success("创建成功!");
    }

    /**
     * 修改
     */
    public function edit(){
        $id = $this->request->post('id');
        $name = $this->request->post('name');
        $image = $this->request->post('image');
        
        
        $result = false;
        Db::startTrans();
        try {
            $oldRow = $this->model->where('id',$id)->find();
            $params['name'] = $name;
            $params['image'] = $image;
            $params['updatetime'] = time();
            $result = $this->model->where('id',$id)->update($params);

            $content = "";
            if($oldRow['name'] != $name){
                $content .= "知识库{$oldRow['name']}重命名，".$oldRow['name']."改为".$name;
            }
            if($oldRow['image'] != $image){
                $content .= ($content?"，":"")."知识库{$oldRow['name']}更换封面图";
            }
            if($content){
                \app\admin\model\ai\ConsoleFileLog::insert(['user_id'=> $this->auth->id, 'action'=>2, 'type'=>2, 'filename'=> $content, 'createtime' => time(), 'updatetime' => time()]);
            }
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        
        $this->success("修改成功!");
    }

    /**
     * 删除
     */
    public function delete(){
        $id = $this->request->post('id');
        
        
        $result = false;
        Db::startTrans();
        try {
            $name = $this->model->where('id',$id)->value('name');
            $params['deletetime'] = time();
            $result = $this->model->where('id',$id)->update($params);

            \app\admin\model\ai\ConsoleFileLog::insert(['user_id'=> $this->auth->id, 'action'=>3, 'type'=>2, 'filename'=> $name, 'createtime' => time(), 'updatetime' => time()]);
            
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        
        $this->success("删除成功!");
    }

    /**
     * 修改知识库状态
     */
    public function status(){
        $id = $this->request->post('id');
        $status = $this->request->post('invoking_status') ? 1 : 0;
        
        
        $result = false;
        Db::startTrans();
        try {
            $params['invoking_status'] = $status;
            $result = $this->model->where('id',$id)->update($params);
            
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result) {
            $this->success('修改成功!');
        }else{
            $this->error('修改失败!');
        }
    }
}
