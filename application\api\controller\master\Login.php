<?php

namespace app\api\controller\master;

use app\common\controller\Api;
use app\common\controller\Backend;
use app\admin\model\AdminLog;
use think\Config;
use think\Hook;
use think\Session;
use think\Validate;
use app\common\model\User;
use think\Db;

/**
 * API-登录
 */
class Login extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    

    /**
     * 管理员登录
     */
    public function index()
    {
        $url = $this->request->get('url', '', 'url_clean');
        $url = $url ?: 'index/index';
        // if ($this->auth->isLogin()) {
        //     $this->success(__("You've logged in, do not login again"), $url);
        // }
        //保持会话有效时长，单位:小时
        $keeyloginhours = 24;
        if ($this->request->isPost()) {
            $username = $this->request->post('username');
            $password = $this->request->post('password', '', null);
            $keeplogin = $this->request->post('keeplogin');
            // $rule = [
            //     'username'  => 'require|length:3,30',
            //     'password'  => 'require|length:3,30',
            // ];
            // $data = [
            //     'username'  => $username,
            //     'password'  => $password,
            // ];
            // if (Config::get('fastadmin.login_captcha')) {
            //     $rule['captcha'] = 'require|captcha';
            //     $data['captcha'] = $this->request->post('captcha');
            // }
            // $validate = new Validate($rule, [], ['username' => __('Username'), 'password' => __('Password'), 'captcha' => __('Captcha')]);
            // $result = $validate->check($data);
            // if (!$result) {
            //     $this->error($validate->getError(), $url, ['token' => $this->request->token()]);
            // }
            AdminLog::setTitle(__('Login'));
            $result = $this->auth->login($username, $password);
            if ($result === true) {
                Hook::listen("admin_login_after", $this->request);
                $group_name = Db::name('user_group')->where(['id' => $this->auth->group_id])->value('name');
                $help_url = \app\admin\model\ai\Project::where('FIND_IN_SET('.$this->auth->id.',user_ids)')->value('knowledgebase_help_url');
                $return = [
                    'id' => $this->auth->id, 
                    'username' => $username, 
                    'nickname' => $this->auth->nickname, 
                    'avatar' => $this->auth->avatar, 
                    'token' => $this->auth->getToken(),
                    'help_url' => $help_url,
                    'group_id' => $this->auth->group_id,
                    'group_name' => $group_name
                ];
                $this->success(__('Login successful'), $return);
            } else {
                $msg = $this->auth->getError();
                $msg = $msg ? $msg : __('Username or password is incorrect');
                $this->error($msg);
            }
        }
        $this->error(__('Account can not be empty'));

    }



    /**
     * 仅用账号登陆（交付页免登录跳转）
     */
    public function loginByUsername()
    {
        //保持会话有效时长，单位:小时
        $keeyloginhours = 24;
        if ($this->request->isPost()) {
            $username = $this->request->post('username');

            $user = User::get(['username' => $username]);
            if (!$user) {
                $this->error('用户不存在');
            }

            if ($user->status != 'normal') {
                $this->error('用户状态不允许登录');
            }

            //直接登录会员
            $this->auth->direct($user->id);
            Hook::listen("admin_login_after", $this->request);
            $this->success(__('Login successful'), ['id' => $this->auth->id, 'username' => $username, 'nickname' => $this->auth->nickname, 'avatar' => $this->auth->avatar, 'token' => $this->auth->getToken()]);
          
        }
        $this->error(__('Account can not be empty'));

    }




}
