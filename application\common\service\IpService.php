<?php

namespace app\common\service;

class IpService
{
		
	/*
	* 根据ip获取地域信息
	*/
	public static function getArea($ip)
	{
		$key = '7f0d284841eb7e9b4ea8cf356d4eccff';
        $url = 'https://restapi.amap.com/v3/ip?ip='.$ip.'&key='.$key;

        $res = \fast\Http::get($url);
        $res = json_decode($res,true);
        
        $area = '';
        if($res['status'] == 1) {
            $area = $res['province'] ?? ''.$res['city'] ?? '';
        }

        return $area;
	}

}